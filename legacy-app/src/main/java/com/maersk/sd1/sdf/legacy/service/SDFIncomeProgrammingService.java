package com.maersk.sd1.sdf.legacy.service;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDFIncomeProgrammingService {

	@RequestMapping(value = "/sdfincomeProgrammingList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfincomeProgrammingList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.income_programming_list","SDF");
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("business_unit_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			pResult.input("transport_planning_id", Jpo.DECIMAL);
			pResult.input("eta_min", Jpo.DATE);
			pResult.input("eta_max", Jpo.DATE);
			pResult.input("container", Jpo.STRING);
			pResult.input("iso_code", Jpo.STRING);
			pResult.input("shipping_line_id", Jpo.DECIMAL);
			pResult.input("shipper_name", Jpo.STRING);
			pResult.input("consignee_name", Jpo.STRING);
			pResult.input("reference", Jpo.STRING);
			pResult.input("status", Jpo.DECIMAL);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdfincomeProgrammingRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfincomeProgrammingRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.income_programming_register","SDF");
			pResult.input("language", Jpo.STRING);
			pResult.input("business_unit_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			pResult.input("operation", Jpo.DECIMAL);
			pResult.input("reference_type", Jpo.DECIMAL);
			pResult.input("reference", Jpo.STRING);
			pResult.input("comments", Jpo.STRING);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("shipper_id", Jpo.DECIMAL);
			pResult.input("consignee_id", Jpo.DECIMAL);
			pResult.input("container_data", Jpo.STRING);
			pResult.input("user_registration_id", Jpo.INTEGER);
			pResult.output("result_new_id", Jpo.INTEGER);
			pResult.output("result_code", Jpo.DECIMAL);
			pResult.output("result_message", Jpo.STRING);
			pResult.output("message_json", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdfincomeProgrammingEdit", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfincomeProgrammingEdit(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.income_programming_edit","SDF");
			pResult.input("language", Jpo.STRING);
			pResult.input("business_unit_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			pResult.input("transport_planning_id", Jpo.DECIMAL);
			pResult.input("operation", Jpo.DECIMAL);
			pResult.input("reference_type", Jpo.DECIMAL);
			pResult.input("reference", Jpo.STRING);
			pResult.input("comments", Jpo.STRING);
			pResult.input("shipping_line_id", Jpo.DECIMAL);
			pResult.input("shipper_id", Jpo.DECIMAL);
			pResult.input("consignee_id", Jpo.DECIMAL);
			pResult.input("container_data", Jpo.STRING);
			pResult.input("user_registration_id", Jpo.INTEGER);
			pResult.output("result_new_id", Jpo.INTEGER);
			pResult.output("result_code", Jpo.DECIMAL);
			pResult.output("result_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdfincomeProgrammingDelete", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfincomeProgrammingDelete(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.income_programming_delete","SDF");
			pResult.input("language", Jpo.STRING);
			pResult.input("business_unit_id", Jpo.DECIMAL);
			pResult.input("sub_business_unit_id", Jpo.DECIMAL);
			pResult.input("transport_planning_id", Jpo.INTEGER);
			pResult.input("contenedor_id", Jpo.INTEGER);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.output("result_new_id", Jpo.INTEGER);
			pResult.output("result_code", Jpo.DECIMAL);
			pResult.output("result_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdfincomeProgrammingLoadRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfincomeProgrammingLoadRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.income_programming_load_register","SDF");
			pResult.input("language", Jpo.STRING);
			pResult.input("business_unit_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			pResult.input("data", Jpo.STRING);
			pResult.input("user_registration_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdfincomeProgrammingListV2", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfincomeProgrammingListV2(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.income_programming_list_v2","SDF");
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("business_unit_id", Jpo.DECIMAL);
			pResult.input("sub_business_unit_id", Jpo.DECIMAL);
			pResult.input("transport_planning_id", Jpo.INTEGER);
			pResult.input("eta_min", Jpo.DATE);
			pResult.input("eta_max", Jpo.DATE);
			pResult.input("container", Jpo.STRING);
			pResult.input("iso_code", Jpo.STRING);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("shipper_name", Jpo.STRING);
			pResult.input("consignee_name", Jpo.STRING);
			pResult.input("reference", Jpo.STRING);
			pResult.input("status", Jpo.DECIMAL);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdfincomeProgrammingGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfincomeProgrammingGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.income_programming_get","SDF");
			pResult.input("transport_planning_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdfdocumentationFullList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdfdocumentationFullList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdf.documentation_full_list","SDF");
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("business_unit_id", Jpo.DECIMAL);
			pResult.input("sub_business_unit_id", Jpo.DECIMAL);
			pResult.input("status_id", Jpo.INTEGER);
			pResult.input("eta_min", Jpo.DATE);
			pResult.input("eta_max", Jpo.DATE);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("shipper_name", Jpo.STRING);
			pResult.input("consignee_name", Jpo.STRING);
			pResult.input("document_number", Jpo.STRING);
			pResult.input("movement_type", Jpo.DECIMAL);
			pResult.input("containers", Jpo.STRING);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}