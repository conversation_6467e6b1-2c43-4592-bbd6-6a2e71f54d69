package com.maersk.sd1.adm.legacy.service;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class ADMIdiomaService {

	@RequestMapping(value = "/segidiomaListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object segidiomaListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("seg.idioma_listar","ADM");
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("codigo", Jpo.CHARACTER);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}