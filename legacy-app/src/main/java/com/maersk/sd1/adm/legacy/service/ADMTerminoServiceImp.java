package com.maersk.sd1.adm.legacy.service;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/adm/ADMTerminoServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class ADMTerminoServiceImp extends ADMTerminoService {

	@RequestMapping(value = "/gesterminoEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesterminoEditar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gesterminoEditar(ppo, request);
	}

	@RequestMapping(value = "/gesterminoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesterminoListar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gesterminoListar(ppo, request);
	}

	@RequestMapping(value = "/gesterminoObtener", method = {RequestMethod.POST})
	public Object gesterminoObtener(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gesterminoObtener(ppo, request);
	}

	@RequestMapping(value = "/gesterminoRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesterminoRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gesterminoRegistrar(ppo, request);
	}

}