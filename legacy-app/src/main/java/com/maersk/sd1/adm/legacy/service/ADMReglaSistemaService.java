package com.maersk.sd1.adm.legacy.service;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class ADMReglaSistemaService {

	@RequestMapping(value = "/gesreglaSistemaObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesreglaSistemaObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("ges.regla_sistema_obtener","ADM");
			pResult.input("regla_sistema_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/gesreglaSistemaRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesreglaSistemaRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("ges.regla_sistema_registrar","ADM");
			pResult.input("id", Jpo.STRING);
			pResult.input("sistema_id", Jpo.DECIMAL);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("descripcion", Jpo.STRING);
			pResult.input("regla", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/gesreglaSistemaListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesreglaSistemaListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("ges.regla_sistema_listar","ADM");
			pResult.input("regla_sistema_id", Jpo.INTEGER);
			pResult.input("id", Jpo.STRING);
			pResult.input("sistema_id", Jpo.DECIMAL);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("descripcion", Jpo.STRING);
			pResult.input("regla", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/gesreglaSistemaEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesreglaSistemaEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("ges.regla_sistema_eliminar","ADM");
			pResult.input("regla_sistema_id", Jpo.INTEGER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/gesreglaSistemaEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gesreglaSistemaEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("ges.regla_sistema_editar","ADM");
			pResult.input("regla_sistema_id", Jpo.INTEGER);
			pResult.input("id", Jpo.STRING);
			pResult.input("sistema_id", Jpo.DECIMAL);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("descripcion", Jpo.STRING);
			pResult.input("regla", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}