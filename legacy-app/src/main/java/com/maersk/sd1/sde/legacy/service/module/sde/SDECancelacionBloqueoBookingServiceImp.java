package com.maersk.sd1.sde.legacy.service.module.sde;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sde/SDECancelacionBloqueoBookingServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDECancelacionBloqueoBookingServiceImp extends SDECancelacionBloqueoBookingService {

	@RequestMapping(value = "/sdecancelacionBloqueoBookingRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdecancelacionBloqueoBookingRegistrar(ppo, request);
	}

	@RequestMapping(value = "/sdecancelacionBloqueoBookingListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingListar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdecancelacionBloqueoBookingListar(ppo, request);
	}

	@RequestMapping(value = "/sdecancelacionBloqueoBookingEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingEditar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdecancelacionBloqueoBookingEditar(ppo, request);
	}

	@RequestMapping(value = "/sdecancelacionBloqueoBookingLiberacionEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingLiberacionEditar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdecancelacionBloqueoBookingLiberacionEditar(ppo, request);
	}

	@RequestMapping(value = "/sdecancelacionBloqueoBookingLiberacionObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingLiberacionObtener(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdecancelacionBloqueoBookingLiberacionObtener(ppo, request);
	}

	@RequestMapping(value = "/sdecancelacionBloqueoBookingLiberacionRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingLiberacionRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdecancelacionBloqueoBookingLiberacionRegistrar(ppo, request);
	}

	@RequestMapping(value = "/sdecancelacionBloqueoBookingObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingObtener(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdecancelacionBloqueoBookingObtener(ppo, request);
	}

}