package com.maersk.sd1.sde.legacy.service.module.sde;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEPrincipalService {

	@RequestMapping(value = "/sdelisInicializar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdelisInicializar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.lis_inicializar","SDE");
			pResult.input("Usuario_id", Jpo.STRING);
			pResult.input("Empresa_id", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdedatosGenerales", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdedatosGenerales(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.datos_generales","SDE");
			pResult.input("Unidad_negocio_id", Jpo.INTEGER);
			pResult.input("Usuario_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeobtenerCatalogoPorTablas", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeobtenerCatalogoPorTablas(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.obtener_catalogo_por_tablas","SDE");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}