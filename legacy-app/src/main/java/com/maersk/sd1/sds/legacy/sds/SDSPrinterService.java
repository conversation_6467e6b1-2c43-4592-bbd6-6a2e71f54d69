package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSPrinterService {

	@RequestMapping(value = "/sdsprinterList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsprinterList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.printer_list","SDS");
			pResult.input("printer_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			pResult.input("printer_name", Jpo.STRING);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("pf_page", Jpo.INTEGER);
			pResult.input("pf_size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsprintBySource", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsprintBySource(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.print_by_source","SDS");
			pResult.input("printer_id", Jpo.INTEGER);
			pResult.input("content", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsprinterFormatSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsprinterFormatSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.printer_format_search","SDS");
			pResult.input("printer_name", Jpo.STRING);
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}