package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSBookingService {

	@RequestMapping(value = "/sdsaprobarBooking", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsaprobarBooking(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.aprobar_booking","SDS");
			pResult.input("booking_id", Jpo.INTEGER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("cat_origen_aprobacion", Jpo.DECIMAL);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingNavesListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingNavesListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_naves_listar","SDS");
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("nombre_nave", Jpo.STRING);
			pResult.input("viaje", Jpo.STRING);
			pResult.input("language_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingPuertoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingPuertoListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_puerto_listar","SDS");
			pResult.input("programacion_nave_detalle_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_registrar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("numero_booking", Jpo.STRING);
			pResult.input("fecha_emision_booking", Jpo.DATETIME);
			pResult.input("programacion_nave_detalle_id", Jpo.INTEGER);
			pResult.input("puerto_embarque_id", Jpo.INTEGER);
			pResult.input("puerto_descarga_id", Jpo.INTEGER);
			pResult.input("puerto_destino_id", Jpo.INTEGER);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("empresa_cliente_id", Jpo.DECIMAL);
			pResult.input("empresa_embarcador_id", Jpo.DECIMAL);
			pResult.input("deposito_vacio_id", Jpo.INTEGER);
			pResult.input("deposito_full_id", Jpo.INTEGER);
			pResult.input("producto_id", Jpo.INTEGER);
			pResult.input("temperatura_c", Jpo.STRING);
			pResult.input("imo_id", Jpo.INTEGER);
			pResult.input("mercaderia", Jpo.STRING);
			pResult.input("cat_estado_booking", Jpo.DECIMAL);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("es_roleo", Jpo.CHARACTER);
			pResult.input("detalle", Jpo.STRING);
			pResult.input("cat_move_type_id", Jpo.DECIMAL);
			pResult.input("maersk_depot_with_sd1", Jpo.CHARACTER);
			pResult.input("origin_destination_depot_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_obtener","SDS");
			pResult.input("booking_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_listar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("numero_booking", Jpo.STRING);
			pResult.input("nomre_nave", Jpo.STRING);
			pResult.input("viaje", Jpo.STRING);
			pResult.input("operacion_id", Jpo.DECIMAL);
			pResult.input("nombre_cliente", Jpo.STRING);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("fecha_emision_booking_min", Jpo.DATE);
			pResult.input("fecha_emision_booking_max", Jpo.DATE);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_eliminar","SDS");
			pResult.input("booking_id", Jpo.INTEGER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_editar","SDS");
			pResult.input("booking_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("numero_booking", Jpo.STRING);
			pResult.input("fecha_emision_booking", Jpo.DATETIME);
			pResult.input("programacion_nave_detalle_id", Jpo.INTEGER);
			pResult.input("puerto_embarque_id", Jpo.INTEGER);
			pResult.input("puerto_descarga_id", Jpo.INTEGER);
			pResult.input("puerto_destino_id", Jpo.INTEGER);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("empresa_cliente_id", Jpo.DECIMAL);
			pResult.input("empresa_embarcador_id", Jpo.DECIMAL);
			pResult.input("deposito_vacio_id", Jpo.INTEGER);
			pResult.input("deposito_full_id", Jpo.INTEGER);
			pResult.input("producto_id", Jpo.INTEGER);
			pResult.input("temperatura_c", Jpo.STRING);
			pResult.input("imo_id", Jpo.INTEGER);
			pResult.input("mercaderia", Jpo.STRING);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("cat_move_type_id", Jpo.DECIMAL);
			pResult.input("maersk_depot_with_sd1", Jpo.CHARACTER);
			pResult.input("origin_destination_depot_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbuscarBookingGateOutEmpty", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbuscarBookingGateOutEmpty(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.buscar_booking_gate_out_empty","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("tipo_movimiento_id", Jpo.DECIMAL);
			pResult.input("numero_booking", Jpo.STRING);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingValidarExistencia", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingValidarExistencia(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_validar_existencia","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("numero_booking", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingDetalleEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingDetalleEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_detalle_eliminar","SDS");
			pResult.input("booking_detalle_id", Jpo.INTEGER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingDetalleRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingDetalleRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_detalle_registrar","SDS");
			pResult.input("booking_id", Jpo.INTEGER);
			pResult.input("cat_tamano_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_contenedor_id", Jpo.DECIMAL);
			pResult.input("cantidad_reserva", Jpo.DECIMAL);
			pResult.input("carga_maxima_requerido", Jpo.DECIMAL);
			pResult.input("cat_origen_creacion_booking_id", Jpo.DECIMAL);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_det_new_id", Jpo.INTEGER);
			pResult.output("resp_det_estado", Jpo.INTEGER);
			pResult.output("resp_det_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingDetalleEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingDetalleEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_detalle_editar","SDS");
			pResult.input("booking_detalle_id", Jpo.INTEGER);
			pResult.input("booking_id", Jpo.INTEGER);
			pResult.input("cat_tamano_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_contenedor_id", Jpo.DECIMAL);
			pResult.input("cantidad_reserva", Jpo.DECIMAL);
			pResult.input("cantidad_atendida", Jpo.DECIMAL);
			pResult.input("carga_maxima_requerido", Jpo.DECIMAL);
			pResult.input("cat_origen_creacion_booking_id", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingPortSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingPortSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_port_search","SDS");
			pResult.input("ship_programming_detail_id", Jpo.INTEGER);
			pResult.input("port_ids", Jpo.STRING);
			pResult.input("port_name", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsbookingGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsbookingGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.booking_get","SDS");
			pResult.input("booking_id", Jpo.INTEGER);
			pResult.input("language_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}