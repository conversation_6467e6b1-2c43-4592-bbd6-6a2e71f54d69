package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSApiMtyFullService {

	@RequestMapping(value = "/sdsapiMtyFull", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsapiMtyFull(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.api_mty_full","SDS");
			pResult.input("location", Jpo.STRING);
			pResult.input("equipment_type", Jpo.STRING);
			pResult.input("move_type", Jpo.STRING);
			pResult.input("full_empty", Jpo.STRING);
			pResult.input("operation_type", Jpo.STRING);
			pResult.input("reference", Jpo.STRING);
			pResult.input("reference_type", Jpo.STRING);
			pResult.input("equipment_number", Jpo.STRING);
			pResult.input("iso_code", Jpo.STRING);
			pResult.input("shipping_line", Jpo.STRING);
			pResult.input("shipper", Jpo.STRING);
			pResult.input("shipper_name", Jpo.STRING);
			pResult.input("consignee", Jpo.STRING);
			pResult.input("consignee_name", Jpo.STRING);
			pResult.input("truck_company", Jpo.STRING);
			pResult.input("truck_company_name", Jpo.STRING);
			pResult.input("tare", Jpo.DECIMAL);
			pResult.input("tare_units", Jpo.STRING);
			pResult.input("max_payload", Jpo.DECIMAL);
			pResult.input("max_payload_units", Jpo.STRING);
			pResult.input("reported_weight", Jpo.DECIMAL);
			pResult.input("reported_weight_units", Jpo.STRING);
			pResult.input("vessel", Jpo.STRING);
			pResult.input("voyage", Jpo.STRING);
			pResult.input("driver_first_name", Jpo.STRING);
			pResult.input("driver_last_name", Jpo.STRING);
			pResult.input("driver_id", Jpo.STRING);
			pResult.input("license_plate", Jpo.STRING);
			pResult.input("manufacture_date", Jpo.STRING);
			pResult.input("grade", Jpo.STRING);
			pResult.input("class", Jpo.STRING);
			pResult.input("move_date", Jpo.STRING);
			pResult.input("move_time", Jpo.STRING);
			pResult.input("truck_departure_date", Jpo.STRING);
			pResult.input("truck_departure_time", Jpo.STRING);
			pResult.input("seal_1", Jpo.STRING);
			pResult.input("seal_2", Jpo.STRING);
			pResult.input("seal_3", Jpo.STRING);
			pResult.input("seal_4", Jpo.STRING);
			pResult.input("remarks", Jpo.STRING);
			pResult.input("language", Jpo.STRING);
			pResult.output("isCorrect", Jpo.CHARACTER);
			pResult.output("message", Jpo.STRING);
			pResult.output("eir", Jpo.INTEGER);
			Object ohb_response = pResult.execute();
			ppo.commit();
		return new Response(ohb_response);
   }

}