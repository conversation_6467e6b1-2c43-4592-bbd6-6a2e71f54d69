package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sds/SDSConductorServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSConductorServiceImp extends SDSConductorService {

	@RequestMapping(value = "/sdsconductorObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsconductorObtener(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsconductorObtener(ppo, request);
	}

}