package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sds/SDSAppEIRConfigServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSAppEIRConfigServiceImp extends SDSAppEIRConfigService {

	@RequestMapping(value = "/sdsappEirConfigList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsappEirConfigList(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsappEirConfigList(ppo, request);
	}

}