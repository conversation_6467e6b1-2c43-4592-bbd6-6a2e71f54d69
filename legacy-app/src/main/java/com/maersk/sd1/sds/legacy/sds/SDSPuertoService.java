package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSPuertoService {

	@RequestMapping(value = "/sdspuertoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdspuertoListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.puerto_listar","SDS");
			pResult.input("puerto_id", Jpo.INTEGER);
			pResult.input("puerto", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("pais_nombre", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdspuertoRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdspuertoRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.puerto_registrar","SDS");
			pResult.input("puerto", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("pais_id", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdspuertoObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdspuertoObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.puerto_obtener","SDS");
			pResult.input("puerto_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdspuertoEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdspuertoEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.puerto_editar","SDS");
			pResult.input("puerto_id", Jpo.INTEGER);
			pResult.input("puerto", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("pais_id", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdspuertoEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdspuertoEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.puerto_eliminar","SDS");
			pResult.input("puerto_id", Jpo.DECIMAL);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}