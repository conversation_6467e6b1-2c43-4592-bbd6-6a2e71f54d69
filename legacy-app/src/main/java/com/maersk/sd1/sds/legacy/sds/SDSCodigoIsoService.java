package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSCodigoIsoService {

	@RequestMapping(value = "/sdscodigoIsoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscodigoIsoListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.codigo_iso_listar","SDS");
			pResult.input("codigo_iso_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("codigo_iso", Jpo.STRING);
			pResult.input("descripcion", Jpo.STRING);
			pResult.input("cat_tipo_contenedor_id", Jpo.DECIMAL);
			pResult.input("cat_tamano_id", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("tara", Jpo.DECIMAL);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsisoCodeSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsisoCodeSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.iso_code_search","SDS");
			pResult.input("container_type", Jpo.INTEGER);
			pResult.input("container_size", Jpo.INTEGER);
			pResult.input("iso_code", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}