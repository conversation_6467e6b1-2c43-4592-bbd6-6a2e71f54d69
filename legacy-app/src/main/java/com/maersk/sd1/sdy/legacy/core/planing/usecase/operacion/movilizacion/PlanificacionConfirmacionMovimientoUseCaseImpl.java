package com.maersk.sd1.sdy.legacy.core.planing.usecase.operacion.movilizacion;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.stream.Collectors;

import com.maersk.sd1.sdy.legacy.core.planing.domain.ColaTrabajo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Contenedor;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Vehiculo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.VehiculoContenedor;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.DatosPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanificacionRequestCommand;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.VisitaContenedor;
import com.maersk.sd1.sdy.legacy.core.planing.port.BloqueRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ColaTrabajoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.InstruccionMovimientoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.OperacionRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ReglasUbicacionRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UbicacionContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UsuarioRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.VehiculoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.InstruccionMovimientoBuilderUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ActualizarSecuenciaAColaTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ColasTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.DeterminarUbicacionOrigenUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.EstrategiaPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.PlanificacionBaseUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.SeleccionUbicacionDisponible;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.movilizacion.PlanificacionMovilizacionUseCaseImpl;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.reglaubicacion.ObtenerReglasPlanificacionUseCase;

public class PlanificacionConfirmacionMovimientoUseCaseImpl 
	extends PlanificacionMovilizacionUseCaseImpl
	implements PlanificacionBaseUseCase {

	private static String ColaTrabajo_EqCtrl = "EQ_CTRL";
	
	private final ColaTrabajoRepository colaTrabajoRepository;
	private final VehiculoRepository vehiculoRepository;
	private final InstruccionMovimientoRepository instruccionMovimientoRepository; 
	
	public PlanificacionConfirmacionMovimientoUseCaseImpl(BloqueRepository bloqueRepository,
			UbicacionContenedorRepository ubicacionContenedorRepository, ContenedorRepository contenedorRepository,
			InstruccionMovimientoRepository instruccionMovimientoRepository, OperacionRepository operacionRepository,
			UsuarioRepository usuarioRepository, ReglasUbicacionRepository reglasUbicacionRepository,
			ColasTrabajoUseCase colaTrabajoUseCase, SeleccionUbicacionDisponible seleccionUbicacionDisponible,
			InstruccionMovimientoBuilderUseCase instruccionMovimientoBuilderUseCase,
			ObtenerReglasPlanificacionUseCase obtenerReglasPlanificacionUseCase,
			EstrategiaPlanificacion asignadorPorUbicacionBase,
			EstrategiaPlanificacion asignadorPorUbicacionSeleccionada,
			EstrategiaPlanificacion asignadorPorUbicacionPorRegla, ColasTrabajoUseCase colasTrabajoUseCase,
			ActualizarSecuenciaAColaTrabajoUseCase actualizarSecuenciaIntruccionMovimientoUseCase,
			DeterminarUbicacionOrigenUseCase determinarUbicacionOrigenUseCase,
			ColaTrabajoRepository colaTrabajoRepository,
			VehiculoRepository vehiculoRepository) {
		super(bloqueRepository, ubicacionContenedorRepository, contenedorRepository, instruccionMovimientoRepository,
				operacionRepository, usuarioRepository, reglasUbicacionRepository, colaTrabajoUseCase,
				seleccionUbicacionDisponible, instruccionMovimientoBuilderUseCase, obtenerReglasPlanificacionUseCase,
				asignadorPorUbicacionBase, asignadorPorUbicacionSeleccionada, asignadorPorUbicacionPorRegla,
				colasTrabajoUseCase, actualizarSecuenciaIntruccionMovimientoUseCase, determinarUbicacionOrigenUseCase);
		this.colaTrabajoRepository = colaTrabajoRepository;
		this.vehiculoRepository = vehiculoRepository;
		this.instruccionMovimientoRepository = instruccionMovimientoRepository;
	}

	@Override
	public PlanificacionRequestCommand execute(PlanificacionRequestCommand command) throws Exception {
		// TODO Auto-generated method stub
		return super.execute(command);
	}
	
	@Override
	public DatosPlanificacion completarDatosPlanificacion(PlanificacionRequestCommand command) throws Exception {
		
		var datos = super.completarDatosPlanificacion(command);

		var vehiculos = vehiculoRepository.TraerPorLista(getNumeroPlacas(command.getContenedores()));
		var vehiculos_contenedores = construirLista(
				datos.getEquipmentPlanificationData().stream().map(c -> c.getContenedor()).collect(Collectors.toList()), 
				vehiculos, 
				command.getContenedores());				
		datos.setVehiculos_contenedor(vehiculos_contenedores);
		
		for(var c : command.getContenedores()) {
			var instruccion_movimiento_anterior = instruccionMovimientoRepository.TraerPorId(c.getInstruccion_numero());
			datos.getEquipmentPlanificationData().stream()
				.filter(e -> e.getContenedor().getNumero_contenedor().equals(c.getNumero_contenedor()))
				.forEach(e -> e.setInstruccion_previa(instruccion_movimiento_anterior));
		}

		return datos;
	}

	@Override
	public ColaTrabajo seleccionarColaTrabajo(DatosPlanificacion datos) throws SQLException {
		return colaTrabajoRepository.TraerPorCodigo(datos.getPatio_id(), ColaTrabajo_EqCtrl);
	}

	private Collection<String> getNumeroPlacas(Collection<VisitaContenedor> placas) {
		return placas.stream().map(c -> c.getPlaca_vehiculo()).collect(Collectors.toList()); 
	}
	
	private Collection<VehiculoContenedor> construirLista(Collection<Contenedor> contenedores, 
			Collection<Vehiculo> vehiculos,
			Collection<VisitaContenedor> requested_contenedores) {
		var lista_vehiculos = new ArrayList<VehiculoContenedor>();
		
		vehiculos.forEach(vehiculo -> {
			var new_vehi_cont = new VehiculoContenedor(vehiculo.getPlaca(), new ArrayList<VisitaContenedor>(), vehiculo);
			requested_contenedores.stream().filter(req_cnt -> req_cnt.getPlaca_vehiculo().equals(vehiculo.getPlaca()))
				.forEach(v -> {
					var contenedor = contenedores.stream().filter(c -> c.getNumero_contenedor().equals(v.getNumero_contenedor())).findFirst();
					new_vehi_cont.getContenedores().add(
							new VisitaContenedor(contenedor.get().getContenedor_id(), 
									contenedor.get().getNumero_contenedor(),
									v.getPlaca_vehiculo()));
				});
			lista_vehiculos.add(new_vehi_cont);
		});
		
		return lista_vehiculos;
	}
		
}
