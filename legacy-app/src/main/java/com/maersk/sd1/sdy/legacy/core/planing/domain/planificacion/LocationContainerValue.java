package com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@Getter
@Setter
public class LocationContainerValue {

	public String getLocation() {
		return formatLegacy();
	}

	private String code_block;
	private String row;
	private String column;
	private Integer level;

	private String location40;
	private String code_block40;
	private String row40;
	private String column40;
	private Integer level40;

	private Integer container_id;
	private String block_type;

	public LocationContainerValue(String block, String block_type, String row, String column, Integer level) {
		this.code_block = block;
		this.block_type = block_type;
		this.row = row;
		this.column = column;
		this.level = level;
	}

	public LocationContainerValue(String block, String block_type, String row, String column, Integer level,
			String block40, String row40, String column40, Integer level40) {
		this.block_type = block_type;

		this.code_block = block;
		this.row = row;
		this.column = column;
		this.level = level;

		this.code_block40 = block40;
		this.row40 = row40;
		this.column40 = column40;
		this.level40 = level40;
	}

	public static LocationContainerValue createFromUbicacionContenedorValor(UbicacionContenedorValor ubicacion) {
		return new LocationContainerValue(ubicacion.getBloque_codigo(),
				ubicacion.getBloque_tipo(),
				ubicacion.getFila(),
				ubicacion.getColumna(),
				ubicacion.getNivel(),
				ubicacion.getBloque_codigo40() == null ? null : ubicacion.getBloque_codigo40(),
				ubicacion.getFila40() == null ? null : ubicacion.getFila40(),
				ubicacion.getColumna40() == null ? null : ubicacion.getColumna40(),
				ubicacion.getNivel40() == null ? null : ubicacion.getNivel40());
	}

	private String format() {
		return this.code_block +
				"." + this.row.trim() +
				"." + this.column.trim() +
				"." + String.valueOf(this.level);
	}

	private String formatHeapVirtual() {
		return this.code_block;
	}

	public String formatLegacy() {
		switch (block_type) {
			case "STACK":
				return this.code_block +
						this.row.trim() +
						this.column.trim() +
						"." + String.valueOf(this.level);
			case "HEAP":
				return formatHeapVirtual();
			case "VIRTUAL":
				return formatHeapVirtual();
			default:
				return this.code_block +
						this.row.trim() +
						this.column.trim() +
						"." + String.valueOf(this.level);
		}
	}

	@Override
	public String toString() {
		switch (code_block) {
			case "STACK":
				return format();
			case "HEAP":
				return formatHeapVirtual();
			case "VIRTUAL":
				return formatHeapVirtual();
			default:
				return format();
		}
	}

	@Override
	public boolean equals(Object obj) {
		if (obj == this)
			return true;
		if (!(obj instanceof LocationContainerValue))
			return false;
		LocationContainerValue u = (LocationContainerValue) obj;

		u.code_block40 = u.row40 == null && u.column40 == null && u.level40 == null ? null : u.code_block40;
		this.code_block40 = this.row40 == null && this.column40 == null && this.level40 == null ? null
				: this.code_block40;

		return u.code_block.equals(this.code_block) &&
				u.row.equals(this.row) &&
				u.column.equals(this.column) &&
				u.level == this.level

				&& ((u.code_block40 == null && this.code_block40 == null)
						|| u.code_block40.equals(this.code_block40))
				&& ((u.row40 == null && this.row40 == null) || u.row40.equals(this.row40))
				&& ((u.column40 == null && this.column40 == null) || u.column40.equals(this.column40))
				&& ((u.level40 == null && this.level40 == null) || u.level40.equals(this.level40));
	}
}