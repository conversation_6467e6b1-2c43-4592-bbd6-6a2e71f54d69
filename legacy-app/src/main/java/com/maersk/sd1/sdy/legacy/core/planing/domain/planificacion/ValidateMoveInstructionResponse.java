package com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion;

import java.util.List;
import java.util.stream.Collectors;

public class ValidateMoveInstructionResponse {	
	
	private List<PlannedEquipmentResponse> response;
	
	public boolean ExistAnyMoveInstruction() {
		return response.size() > 0;
	}
	
	public List<String> getContainerNumberList() {
		return response.stream().map(p -> p.getEquipment_number()).collect(Collectors.toList());
	}
	
	public List<PlannedEquipmentResponse> getResponse() {
		return response;
	}
	
	public void setResponse(List<PlannedEquipmentResponse> response) {
		this.response = response;
	}
	
}
