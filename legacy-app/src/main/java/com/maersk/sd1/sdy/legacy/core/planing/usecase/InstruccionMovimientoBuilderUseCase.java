package com.maersk.sd1.sdy.legacy.core.planing.usecase;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Optional;

import com.maersk.sd1.sdy.legacy.core.planing.domain.InstruccionMovimiento;
import com.maersk.sd1.sdy.legacy.core.planing.domain.UbicacionContenedor;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.DatosPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.VisitaContenedor;

public interface InstruccionMovimientoBuilderUseCase {
	
	public Collection<InstruccionMovimiento> crear(Collection<UbicacionContenedor> ubicaciones, DatosPlanificacion datos, Boolean apr, Collection<VisitaContenedor> conts, Optional<Integer> fatherMovementInstructionId) throws SQLException;
	
	public Collection<InstruccionMovimiento> crear(Collection<UbicacionContenedor> ubicaciones, DatosPlanificacion datos, Boolean apr, Collection<VisitaContenedor> conts) throws SQLException;
	
}
