package com.maersk.sd1.sdy.legacy.core.planing.usecase.operacion.movilizacion;

import com.maersk.sd1.sdy.legacy.core.planing.domain.Tipo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.DatosPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanificacionRequestCommand;
import com.maersk.sd1.sdy.legacy.core.planing.port.BloqueRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.InstruccionMovimientoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.OperacionRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ReglasUbicacionRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UbicacionContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UsuarioRepository;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.InstruccionMovimientoBuilderUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ActualizarSecuenciaAColaTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ColasTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.DeterminarUbicacionOrigenUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.EstrategiaPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.PlanificacionBaseUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.SeleccionUbicacionDisponible;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.movilizacion.PlanificacionMovilizacionUseCaseImpl;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.reglaubicacion.ObtenerReglasPlanificacionUseCase;

public class PlanificacionCorreccionUbicacionUseCaseImpl 
	extends PlanificacionMovilizacionUseCaseImpl
	implements PlanificacionBaseUseCase {

	private static Tipo Tipo_Movimiento_Ubicacion = new Tipo(47833, null, "UBI", "UBICACION", true);
	
	public PlanificacionCorreccionUbicacionUseCaseImpl(BloqueRepository bloqueRepository,
			UbicacionContenedorRepository ubicacionContenedorRepository, ContenedorRepository contenedorRepository,
			InstruccionMovimientoRepository instruccionMovimientoRepository, OperacionRepository operacionRepository,
			UsuarioRepository usuarioRepository, ReglasUbicacionRepository reglasUbicacionRepository,
			ColasTrabajoUseCase colaTrabajoUseCase, SeleccionUbicacionDisponible seleccionUbicacionDisponible,
			InstruccionMovimientoBuilderUseCase instruccionMovimientoBuilderUseCase,
			ObtenerReglasPlanificacionUseCase obtenerReglasPlanificacionUseCase,
			EstrategiaPlanificacion asignadorPorUbicacionBase,
			EstrategiaPlanificacion asignadorPorUbicacionSeleccionada,
			EstrategiaPlanificacion asignadorPorUbicacionPorRegla, ColasTrabajoUseCase colasTrabajoUseCase,
			ActualizarSecuenciaAColaTrabajoUseCase actualizarSecuenciaIntruccionMovimientoUseCase,
			DeterminarUbicacionOrigenUseCase determinarUbicacionOrigenUseCase) {
		super(bloqueRepository, ubicacionContenedorRepository, contenedorRepository, instruccionMovimientoRepository,
				operacionRepository, usuarioRepository, reglasUbicacionRepository, colaTrabajoUseCase,
				seleccionUbicacionDisponible, instruccionMovimientoBuilderUseCase, obtenerReglasPlanificacionUseCase,
				asignadorPorUbicacionBase, asignadorPorUbicacionSeleccionada, asignadorPorUbicacionPorRegla,
				colasTrabajoUseCase, actualizarSecuenciaIntruccionMovimientoUseCase, determinarUbicacionOrigenUseCase);
		// TODO Auto-generated constructor stub
	}

	@Override
	public DatosPlanificacion completarDatosPlanificacion(PlanificacionRequestCommand command) throws Exception {
		var datos = super.completarDatosPlanificacion(command);
		
		datos.setTipo_movimiento_interno(Tipo_Movimiento_Ubicacion);
		
		if (datos.getUbicaciones_propuestas().isEmpty()) {
			throw new Exception("No se ha enviado ninguna ubicación.");			
		}

		if (datos.getEquipmentPlanificationData().stream()
				.map(c -> c.getContenedor())
				.filter(c -> c.getUbicacion_actual() != null).count() != 0) {
			throw new Exception("Se ha(n) enviado contenedor(es) que no están ubicados en ninguna posición dentro del patio.");			
		}

		if (datos.getUbicaciones_propuestas().size() != datos.getEquipmentPlanificationData().size()) {
			throw new Exception("La cantidad de ubicaciones enviadas no corresponde a la cantidad de contenedores enviados.");
		}
				
		return datos;
	}

}
