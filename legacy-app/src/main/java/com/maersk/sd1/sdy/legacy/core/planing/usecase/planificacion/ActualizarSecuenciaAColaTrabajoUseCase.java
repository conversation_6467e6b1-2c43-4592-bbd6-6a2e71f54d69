package com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion;

import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion_secuencia.ActualizarSecuenciasIntruccionMovimientoCommand;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion_secuencia.ActualizarSecuenciasIntruccionMovimientoResponse;

public interface ActualizarSecuenciaAColaTrabajoUseCase {

	public ActualizarSecuenciasIntruccionMovimientoResponse Execute(ActualizarSecuenciasIntruccionMovimientoCommand command) throws Exception;
}
