package com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
public class VisitaContenedorRequest {
	private String numero_contenedor;
	private Integer instruccion_numero;
	private Integer contador_peticion = 1;
	private Integer contenedor_id;
	
	private UbicacionContenedorValor ubicacion;
	
	public VisitaContenedorRequest(String numero_contenedor,
				Integer instruccion_numero, UbicacionContenedorValor ubicacion) {
		this.numero_contenedor = numero_contenedor;
		this.instruccion_numero = instruccion_numero;
		this.ubicacion = ubicacion;
		this.contador_peticion = 1;
	}
}
