package com.maersk.sd1.sdy.legacy.core.planing.port;

import java.sql.SQLException;
import java.util.List;

import com.maersk.sd1.sdy.legacy.core.planing.domain.Bloque;
import com.maersk.sd1.sdy.legacy.core.planing.domain.RangoReglaPlanificacionPatio;
import com.maersk.sd1.sdy.legacy.core.planing.domain.UbicacionContenedor;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.DelimitedBlockRangeRequest;

public interface BloqueRepository {
	public Bloque TraerPorId(int bloque_id)  throws SQLException;
	
	public Bloque TraerPorCodigo(int patio_id, String bloque_codigo) throws SQLException;	
	
	public Bloque ObtenerCeldasNivelesPorLimites(int bloque_id, int row_index_from, int col_index_from, int row_index_to, int col_index_to) throws SQLException;
	
	public List<RangoReglaPlanificacionPatio> getPositionsByRangeInABlock(List<DelimitedBlockRangeRequest> listDelimitedBlockRange) throws SQLException;
	
	public UbicacionContenedor ObtenerUbicacionVirtual(String bloque_codigo, int patio_id) throws SQLException;

	
}
