package com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.salida;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;

import com.maersk.sd1.sdy.legacy.core.planing.domain.ColaTrabajo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Contenedor;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Operacion;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Patio;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Tipo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Usuario;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.AssignmentGateOutRequest;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.AvailableContainer;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.CriterioSeleccionColaTrabajo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.DatosPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.EquipmentDocument;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.GateOutRequest;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanificacionRequestCommand;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanningEquipmentVisit;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.VisitaContenedor;
import com.maersk.sd1.sdy.legacy.core.planing.port.AssignmentGateOutRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.BloqueRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.CatalogoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.InstruccionMovimientoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.OperacionRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.PatioRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UbicacionContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UsuarioRepository;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.InstruccionMovimientoBuilderUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ActualizarSecuenciaAColaTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ColasTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.DeterminarUbicacionOrigenUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.EstrategiaPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.PlanificacionBaseUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.PlanificacionBaseUseCaseImpl;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.reglaubicacion.ObtenerReglasPlanificacionUseCase;
import com.maersk.sd1.sdy.legacy.core.shared.BusinessConstants;
import com.maersk.sd1.sdy.legacy.infraestructure.shared.constants.CommonConstants;
import com.maersk.sd1.sdy.legacy.infraestructure.shared.utils.GsonUtil;
import org.apache.tomcat.util.json.JSONParser;
import org.json.JSONArray;
import org.json.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanningDocument;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanningEquipment;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanningGateRequest;
import ohSolutions.ohRest.util.bean.Response;


public class PlanificacionSalidaUseCaseImpl extends PlanificacionBaseUseCaseImpl implements PlanificacionBaseUseCase {

	private static String Codigo_Tipo_Operacion_Salida = "PUERTO";
	private static String Tipo_Tabla_Operacion = "TIPOPE";	

	private final CatalogoRepository catalogoRepository;
	private final ColasTrabajoUseCase colasTrabajoUseCase;
	private final InstruccionMovimientoRepository instruccionMovimientoRepository;
	private final PatioRepository patioRepository;
	private final OperacionRepository operacionRepository;
	private final UsuarioRepository usuarioRepository;

	@Autowired
	private AssignmentGateOutRepository gateOutRepository;

	public PlanificacionSalidaUseCaseImpl(BloqueRepository bloqueRepository,
			InstruccionMovimientoRepository instruccionMovimientoRepository,
			UbicacionContenedorRepository ubicacionContenedorRepository, UsuarioRepository usuarioRepository,
			ContenedorRepository contenedorRepository, OperacionRepository operacionRepository,
			CatalogoRepository catalogoRepository, InstruccionMovimientoBuilderUseCase builder,
			ObtenerReglasPlanificacionUseCase cargarReglasPlanificacion, EstrategiaPlanificacion asignadorUbicacion,
			ColasTrabajoUseCase colasTrabajoUseCase,
			ActualizarSecuenciaAColaTrabajoUseCase actualizarSecuenciaIntruccionMovimientoUseCase,
			DeterminarUbicacionOrigenUseCase determinarUbicacionOrigenUseCase, PatioRepository patioRepository) {

		super(instruccionMovimientoRepository, usuarioRepository, contenedorRepository, operacionRepository, builder,
				cargarReglasPlanificacion, asignadorUbicacion, colasTrabajoUseCase,
				actualizarSecuenciaIntruccionMovimientoUseCase);

		this.catalogoRepository = catalogoRepository;
		this.colasTrabajoUseCase = colasTrabajoUseCase;
		this.instruccionMovimientoRepository = instruccionMovimientoRepository;
		this.patioRepository = patioRepository;
		this.operacionRepository = operacionRepository;
		this.usuarioRepository = usuarioRepository;
	}

	@Override
	public PlanificacionRequestCommand execute(PlanificacionRequestCommand command) throws Exception {
		try {
			// Obtener el codigo del patio (si el comando no lo contiene)
			if (command.getPatio_codigo() == null) {
				Patio patio = patioRepository.obtenerPatioByUnidadNegocio(command.getUnidad_negocio_id());
				if (patio != null) {
					command.setPatio_codigo(patio.getCodigo());
				} else {
					throw new Exception("sdy.exceptions.noYardBusinessUnit");
				}
			}

			PlanificacionRequestCommand result = new PlanificacionRequestCommand();
			if (command.getPatio_codigo() != null) {
				/*
				 * Validar si dicho contenedor no posee una instruccion de salida ya creada y en
				 * espera de atencion (PreAsignaciones)
				 */
				List<VisitaContenedor> contenedoresProgramados = new ArrayList<VisitaContenedor>();
				command.getContenedores().forEach(cnt -> {
					try {
						int instruccionesPendientes = instruccionMovimientoRepository
								.ValidarInstruccionesMovimiento(cnt.getNumero_contenedor(), command.getPatio_codigo());
						if (instruccionesPendientes > 0) {
							contenedoresProgramados.add(cnt);
						}
					} catch (SQLException e) {
						e.printStackTrace();
					}
				});
				/*
				 * Remover los contenedores que ya tengan una instruccion programada
				 */
				command.getContenedores().removeIf(cnt -> contenedoresProgramados.contains(cnt));
				if (command.getContenedores().size() > 0) {
					result = super.execute(command);
				}
				result.setIsCorrect(CommonConstants.ISCORRECT);
				result.setBusinessMessage(CommonConstants.SUCCESS);
			} else {
				result.setIsCorrect(CommonConstants.ERROR);
				result.setBusinessMessage(CommonConstants.BUSINESSERROR);
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e.getMessage());
		}
	}

	@Override
	public DatosPlanificacion completarDatosPlanificacion(PlanificacionRequestCommand command) throws Exception {

		var datos = super.completarDatosPlanificacion(command);

		var tipo_operacion = catalogoRepository.TraerTipoPorCodigo(Tipo_Tabla_Operacion, command.getTipo_operacion());
		if (tipo_operacion == null) {
			throw new Exception("sdy.exceptions.errorOperationTypeSent");
		}
		datos.setTipo_operacion(tipo_operacion);

		var tipo_ingreso_salida = catalogoRepository.TraerTipoPorId(tipo_operacion.getVariable_3());
		datos.setTipo_movimiento(tipo_ingreso_salida);

		var tipo_movimiento_interno = new Tipo(42903, null, "DEV", "ENTREGA", true);
		datos.setTipo_movimiento_interno(tipo_movimiento_interno);

		return datos;
	}

	@Override
	public ColaTrabajo seleccionarColaTrabajo(DatosPlanificacion datos) throws SQLException {		
		var criterio_seleccion_cola = new CriterioSeleccionColaTrabajo(datos.getPatio_id(),
				datos.getTipo_movimiento().getCodigo(), datos.getTipo_operacion().getCodigo(),
				!datos.getCommand().getAditional_data().isEmpty() ? datos.getCommand().getAditional_data().stream().findFirst().get().getDocument().getCat_empty_full_id() : null);
		return colasTrabajoUseCase.SeleccionarColaTrabajo(criterio_seleccion_cola);		
	}
	
	
	@Override
	public PlanificacionRequestCommand executePlanning(PlanificacionRequestCommand command) throws Exception {
		try {
			// Obtener el codigo del patio (si el comando no lo contiene)
			if (command.getPatio_codigo() == null) {
				Patio patio = patioRepository.GetYardByBusinessUnitAlias(command.getSub_unidad_negocio_alias());
				if (patio != null) {
					command.setPatio_codigo(patio.getCodigo());
				} else {
					throw new Exception("sdy.exceptions.noYardBusinessUnit");
				}
			}

			PlanificacionRequestCommand result = new PlanificacionRequestCommand();
			if (command.getPatio_codigo() != null) {
				/*
				 * Validar si dicho contenedor no posee una instruccion de salida ya creada y en
				 * espera de atencion (PreAsignaciones)
				 */
				List<VisitaContenedor> contenedoresProgramados = new ArrayList<VisitaContenedor>();
				command.getContenedores().forEach(cnt -> {
					try {
						int instruccionesPendientes = instruccionMovimientoRepository.ValidateMoveInstruction(cnt.getNumero_contenedor(), command.getSub_unidad_negocio_alias());
						if (instruccionesPendientes > 0) {
							contenedoresProgramados.add(cnt);
						}
					} catch (SQLException e) {
						e.printStackTrace();
					}
				});
				/*
				 * Remover los contenedores que ya tengan una instruccion programada 
				 */				
				command.getContenedores().removeIf(cnt -> contenedoresProgramados.contains(cnt));
				if (command.getContenedores().size() > 0) {
					result = super.executePlanning(command);
				}
				result.setIsCorrect(CommonConstants.ISCORRECT);
				result.setBusinessMessage(CommonConstants.SUCCESS);			
			} else {
				result.setIsCorrect(CommonConstants.ERROR);
				result.setBusinessMessage(CommonConstants.BUSINESSERROR);
			}
			result.setAditional_data(null);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e.getMessage());
		}
	}

	@Override
	public DatosPlanificacion completePlanningData(PlanificacionRequestCommand command) throws Exception {

		var firstAditionalData = command.getAditional_data().stream().findFirst().get();
		var firstDocument = firstAditionalData.getDocument();

		Collection<PlanningEquipmentVisit> planning_equipment_visit = new ArrayList<PlanningEquipmentVisit>();
		command.getAditional_data().forEach(aditional_data -> {
			var model = new PlanningEquipmentVisit();
			model.setNumero_contenedor(aditional_data.getEquipment().getNumero_contenedor());
			model.setEir_numero(aditional_data.getDocument().getEir_id());
			planning_equipment_visit.add(model);
		});

		String visitas_json = GsonUtil.ConvertToJSON(planning_equipment_visit);
		var aditionalEquipmentDocumentData = operacionRepository.GetAditionalEquipmentDocumentData(
				command.getUsuario_alias(),
				command.getSub_unidad_negocio_alias(),
				firstAditionalData.getDocument().getVariable_3(),
				visitas_json,
				BusinessConstants.INSTRUCTION_MOVEMENTE_TYPE.DEVOLUTION_CODE);

		// SETTING YARD VALUES

		command.setPatio_codigo(aditionalEquipmentDocumentData.getPatio().getCodigo());
		command.setUsuario_id(aditionalEquipmentDocumentData.getUsuario().getUsuario_id());
		command.setUnidad_negocio_id(aditionalEquipmentDocumentData.getUnidad_negocio().getUnidad_negocio_id());
		command.setUnidad_negocio_padre_id(
				aditionalEquipmentDocumentData.getUnidad_negocio().getUnidad_negocio_padre_id());
		command.setPatio_id(aditionalEquipmentDocumentData.getPatio().getPatio_id());

		var planningData = new DatosPlanificacion(command,
				command.getUnidad_negocio_id(),
				command.getPatio_id(),
				new Usuario());

		// SETTING USER ID
		planningData.getUsuario().setUsuario_id(command.getUsuario_id());

		// SETTING EQUIPMENT DATA
		command.getAditional_data().forEach(aditional_data -> {
			var equipment = aditional_data.getEquipment();
			var equipmentSelected = aditionalEquipmentDocumentData.getEquipments()
					.stream()
					.filter(eqp -> eqp.getContenedor_id() == equipment.getContenedor_id())
					.findFirst().orElse(null);
			if (equipmentSelected != null) {
				// ----------- EQUIPMENT --------------
				equipmentSelected.setCat_familia_id(equipment.getCat_familia_id());
				equipmentSelected.setNumero_contenedor(equipment.getNumero_contenedor());
				equipmentSelected.setCat_familia_id(equipment.getCat_familia_id());
				equipmentSelected.setCat_tamano_id(equipment.getCat_tamano_id());
				equipmentSelected.setCat_tipo_contenedor_id(equipment.getCat_tipo_contenedor_id());
				equipmentSelected.setCat_clase_id(equipment.getCat_clase_id());
				equipmentSelected.setLinea_naviera_id(equipment.getLinea_naviera_id());
				equipmentSelected.setTara(equipment.getTara());
				equipmentSelected.setCarga_maxima(equipment.getCarga_maxima());
				equipmentSelected.setCodigo_iso_id(equipment.getCodigo_iso_id());
				equipmentSelected.setCat_tipo_reefer_id(equipment.getCat_tipo_reefer_id());
				equipmentSelected.setCat_marca_motor_id(equipment.getCat_marca_motor_id());
				equipmentSelected.setShipper_own(equipment.isShipper_own());
				// ----------- EIR ZONE --------------
				equipmentSelected.setCat_tipo_actividad_id(equipment.getCat_tipo_actividad_id());
				// ----------- EQUIPMENT ACTIVITY --------------
				equipmentSelected.setActividad_codigo(equipment.getActividad_codigo());
				equipmentSelected.setActividad_descripcion(equipment.getActividad_descripcion());
				// ----------- SHIPPING LINE --------------
				equipmentSelected.setLinea_naviera_nombre(equipment.getLinea_naviera_nombre());
			}
		});

		Collection<VisitaContenedor> equipmentVisits = command.getContenedores();

		Collection<Contenedor> equipmentList = MapToEquipment(aditionalEquipmentDocumentData.getEquipments());

		// UPDATE EQUIPMENT ID IN VISITS
		equipmentList.forEach(equipment -> {
			equipmentVisits.stream()
					.filter(e -> e.getNumero_contenedor().equals(equipment.getNumero_contenedor()))
					.forEach(c -> {
						c.setContenedor_id(equipment.getContenedor_id());
					});
		});

		// SETTING DOCUMENT DATA
		Collection<Operacion> documentList = MapToDocument(command.getAditional_data(), equipmentList);

		planningData.CargarDatosContenedor(equipmentList, documentList);

		// CONFIGURE ADITIONAL TYPES
		planningData.setTipo_movimiento(aditionalEquipmentDocumentData.getTipo_movimiento());
		planningData.setTipo_operacion(firstDocument.GetProcedencia());
		planningData.setTipo_movimiento_interno(aditionalEquipmentDocumentData.getTipo_movimiento_interno());

		// CONFIGURE VEHICLE BY EQUIPMENT
		// var operationEquipmentList =
		// MapToOperationEquipment(command.getAditional_data());
		// planningData.setVehiculos_contenedor(operationEquipmentList.stream().map(o ->
		// o.getVehiculo()).collect(Collectors.toList()));

		return planningData;
	}


	@Override
	public Object gateOutGeneralAssignmentRegister(AssignmentGateOutRequest request)
			throws Exception {

		final ObjectMapper objectMapper = new ObjectMapper();

		Response response = (Response) gateOutRepository.sdyGateOutGeneralAssignmentRegisterV3(request);
		List<Object> result = (List<Object>) response.getResult();

		if (result.get(2) != null) {

			JSONArray integration_data_array = new JSONArray(result.get(2).toString());
			JSONObject jsonObj = integration_data_array.getJSONObject(0);

			if (jsonObj.get("type_product_integration").equals("sdy")) {

				JSONArray json = new JSONArray(jsonObj.getString("document"));
				JSONObject jsonObjDocument = json.getJSONObject(0).getJSONObject("document");

				PlanningDocument document = objectMapper.readValue(jsonObjDocument.toString(), new TypeReference<PlanningDocument>() {});

				JSONArray jsonArrayEquipments = new JSONArray(jsonObj.getString("equipment"));
				JSONObject jsonEquipment = jsonArrayEquipments.getJSONObject(0).getJSONObject("equipment");

				PlanningEquipment equipment = objectMapper.readValue(jsonEquipment.toString(), new TypeReference<PlanningEquipment>() {});

				ArrayList<EquipmentDocument> data = new ArrayList<>();
				data.add(EquipmentDocument.builder().document(document).equipment(equipment).build());

				PlanningGateRequest handMadeRequest = PlanningGateRequest.builder()
						.yard_code(jsonArrayEquipments.getJSONObject(0).getString("yard_code"))
						.user_alias(request.getUser_alias())
						.sub_business_unit_alias(jsonArrayEquipments.getJSONObject(0).getString("sub_business_unit_local_alias"))
						.operation_type(request.getOperation_type())
						.process_realized("GO")
						.aditional_data(data)
						.build();

				PlanificacionRequestCommand requestGateOut = PlanificacionRequestCommand
						.CreateGateFrom(handMadeRequest);

				return executePlanning(requestGateOut);
			}
		}

		return response;
	}
	
	@Override
	public PlanificacionRequestCommand gateOutGeneralRegister(GateOutRequest request) throws Exception {
		//Validate container assignment
		if (request.getContainers_assignment()) {
			ArrayList<ArrayList<Object>> assign_containers = new ArrayList<>();
			if (request.getContainer_content_type().equals("E")) {
				assign_containers = (ArrayList<ArrayList<Object>>) gateOutRepository.sdyFindMostAvailableEmptyContainerGateOut(request);
			} else {
				assign_containers = (ArrayList<ArrayList<Object>>) gateOutRepository.sdyFindMostAvailableFullContainerGateOut(request);
			}
			if (!assign_containers.isEmpty()) {
				ArrayList<AvailableContainer> available_containers = new ArrayList<>();
				assign_containers.stream().forEach(ac -> {
					available_containers.add(AvailableContainer.fromArray(ac));
				});
				if (!available_containers.stream().anyMatch(ac -> ac.getContainer_id()== 0) && request.getContainers().size() == available_containers.size()) {
					for (int i = 0; i < available_containers.size(); i++) {
						request.getContainers().get(i).setContainer_number(available_containers.get(i).getContainer_number());
					}
				} else {
					throw new Exception("sdy.exceptions.NotFoundContainers");
				}
			} else {
				throw new Exception("sdy.exceptions.NotFoundContainers");
			}			
		}		
		
		ArrayList<EquipmentDocument> data = (ArrayList<EquipmentDocument>) this.getDocumentEquipmentAdditionalData(request);
		Usuario user = usuarioRepository.TraerPorId(Integer.parseInt(request.getUser_id()));
		
		if (user == null) {
			request.setUser_id(null);
			throw new Exception("sdy.exception.NotFoundUser");
		}
		
		PlanningGateRequest handMadeRequest = PlanningGateRequest.builder()
				.yard_code(data.get(0).getDocument().getYard_code())
				.user_alias(user.getId())
				.sub_business_unit_alias(data.get(0).getDocument().getSub_business_unit_local_alias())
				.operation_type(request.getOperation_type())
				.process_realized("GO")
				.aditional_data(data)
				.build();
		
		PlanificacionRequestCommand requestGateOut = PlanificacionRequestCommand.CreateGateFrom(handMadeRequest);
		
		return executePlanning(requestGateOut);
	}
	

}
