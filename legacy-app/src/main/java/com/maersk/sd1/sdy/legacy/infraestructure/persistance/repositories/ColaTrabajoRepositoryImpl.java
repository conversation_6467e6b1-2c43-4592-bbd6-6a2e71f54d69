package com.maersk.sd1.sdy.legacy.infraestructure.persistance.repositories;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;

import com.maersk.sd1.sdy.legacy.core.planing.domain.ColaTrabajo;
import com.maersk.sd1.sdy.legacy.core.planing.port.ColaTrabajoRepository;
import com.maersk.sd1.sdy.legacy.core.shared.BoolenUtils;
import com.maersk.sd1.sdy.legacy.core.shared.IntUtils;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.ActualizaColaTrabajoEstadoCommandDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.ActualizarPuntoTrabajoAColaTrabajoCommandDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.ActualizarPuntoTrabajoAColaTrabajoResponseDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.BusquedaInstruccionMovimientoPorColaTrabajoCommandDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.BusquedaInstruccionMovimientoPorColaTrabajoResponseDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.MoverInstruccionAOtraColaCommandDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.MoverInstruccionAOtraColaResponseDto;
import com.maersk.sd1.sdy.legacy.infraestructure.shared.utils.GsonUtil;

import lombok.RequiredArgsConstructor;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.Procedure;

@Repository
@RequiredArgsConstructor
public class ColaTrabajoRepositoryImpl implements ColaTrabajoRepository {

	@Autowired
	private ApplicationContext context;

	@Override
	public ColaTrabajo TraerPorUsuario(int patio_id, int usuario_id) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.colatrabajo_obtener_usuario");
		ColaTrabajo respuestaBusqueda = null;

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("usuario_id", String.valueOf(usuario_id), Jpo.INTEGER);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado, ColaTrabajo.class);
		
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public Collection<BusquedaInstruccionMovimientoPorColaTrabajoResponseDto> buscarInstruccionesMovimientoPorColaTrabajo(
			BusquedaInstruccionMovimientoPorColaTrabajoCommandDto command) throws SQLException {
		List<BusquedaInstruccionMovimientoPorColaTrabajoResponseDto> result = new ArrayList<BusquedaInstruccionMovimientoPorColaTrabajoResponseDto>();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_instruccionmovimiento_por_colatrabajo_buscar");
		try {
			pResult.input("cola_trabajo_id", IntUtils.getStringValue(command.getCola_trabajo_id()), Jpo.INTEGER);
			pResult.input("horas_historico", IntUtils.getStringValue(command.getHoras_historico()), Jpo.INTEGER);
			pResult.input("sub_unidad_negocio_local_id", IntUtils.getStringValue(command.getSub_unidad_negocio_local_id()), Jpo.INTEGER);
			
			List<Object> resultado = (List<Object>) pResult.execute();
			List<BusquedaInstruccionMovimientoPorColaTrabajoResponseDto> resultHistorico = GsonUtil.GetList(resultado.get(0),
					BusquedaInstruccionMovimientoPorColaTrabajoResponseDto.class);
			result.addAll(resultHistorico);

			List<BusquedaInstruccionMovimientoPorColaTrabajoResponseDto> resultTrabajando = GsonUtil.GetList(resultado.get(1),
					BusquedaInstruccionMovimientoPorColaTrabajoResponseDto.class);
			result.addAll(resultTrabajando);

			conn.commit();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public Collection<MoverInstruccionAOtraColaResponseDto> moverIntruccionesMovimientoAOtraCola(
			MoverInstruccionAOtraColaCommandDto command) throws SQLException {
		List<MoverInstruccionAOtraColaResponseDto> result = new ArrayList<MoverInstruccionAOtraColaResponseDto>();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_colatrabajo_mover_intruccionmovimiento");
		try {
			pResult.input("instrucciones_movimiento", command.getInstrucciones_movimiento(), Jpo.STRING);
			pResult.input("cola_trabajo_destino_id", String.valueOf(command.getCola_trabajo_destino_id()), Jpo.INTEGER);
			pResult.input("usuario_id", String.valueOf(command.getUsuario_id()), Jpo.INTEGER);
			List<Object> resultado = (List<Object>) pResult.execute();
			result = GsonUtil.GetList(resultado, MoverInstruccionAOtraColaResponseDto.class);
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public Boolean actualizarPuntoTrabajoAColaTrabajo(ActualizarPuntoTrabajoAColaTrabajoCommandDto command) throws SQLException {
		ActualizarPuntoTrabajoAColaTrabajoResponseDto response = new ActualizarPuntoTrabajoAColaTrabajoResponseDto();
		response.setResp_estado(false);
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_colatrabajo_actualizar_puntotrabajo");
		try {
			var punto_trabajo_id = command.getPunto_trabajo_id() == 0 ? null
					: String.valueOf(command.getPunto_trabajo_id());
			pResult.input("punto_trabajo_id", punto_trabajo_id, Jpo.INTEGER);
			pResult.input("cola_trabajo_id", String.valueOf(command.getCola_trabajo_id()), Jpo.INTEGER);
			pResult.input("usuario_id", String.valueOf(command.getUsuario_id()), Jpo.INTEGER);
			List<Object> resultado = (List<Object>) pResult.execute();
			response = GsonUtil.GetModel(resultado, ActualizarPuntoTrabajoAColaTrabajoResponseDto.class);
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return response.isResp_estado();
	}

	@Override
	public ColaTrabajo TraerPorDefecto(int patio_id) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_colatrabajo_por_defecto");
		ColaTrabajo respuestaBusqueda = null;

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado, ColaTrabajo.class);
		
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public boolean ActualizaColaTrabajoEstado(ActualizaColaTrabajoEstadoCommandDto command) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_colatrabajo_actualizar_estado");
		try {
			pResult.input("cola_trabajo_id", String.valueOf(command.getCola_trabajo_id()), Jpo.INTEGER);
			pResult.input("activo", BoolenUtils.getStringValue(command.isActivo()), Jpo.STRING);
			pResult.input("usuario_id", String.valueOf(command.getUsuario_id()), Jpo.INTEGER);
			var resultado = pResult.execute();
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return true;
	}

	@Override
	public ColaTrabajo TraerPorTipoMovimiento(int patio_id, String tipo_movimiento, String procedencia) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.colatrabajo_obtener_tipo_movimiento");
		ColaTrabajo respuestaBusqueda = null;
		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("tipo_movimiento", tipo_movimiento, Jpo.STRING);
			pResult.input("procedencia", procedencia, Jpo.STRING);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado, ColaTrabajo.class);
		
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	
	@Override
	public ColaTrabajo TraerPorId(int cola_trabajo_id) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_colatrabajo_traer");
		ColaTrabajo respuestaBusqueda = null;
		try {
			pResult.input("cola_trabajo_id", String.valueOf(cola_trabajo_id), Jpo.INTEGER);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado, ColaTrabajo.class);
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public ColaTrabajo TraerPorCodigo(int patio_id, String codigo) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_colatrabajo_traer_codigo");
		ColaTrabajo respuestaBusqueda = null;
		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("cola_trabajo_codigo", codigo, Jpo.STRING);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado, ColaTrabajo.class);
			
			conn.commit();
			
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public ColaTrabajo SeleccionarColaTrabajo(int patio_id, String tipo_movimiento, String procedencia,
			Object cat_empty_full_id, Object bloque_id) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.cola_trabajo_seleccionar");
		ColaTrabajo respuestaBusqueda = null;
		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("tipo_movimiento", tipo_movimiento, Jpo.STRING);
			pResult.input("procedencia", procedencia, Jpo.STRING);
			pResult.input("cat_empty_full_id", String.valueOf(cat_empty_full_id), Jpo.INTEGER);
			pResult.input("bloque_id", String.valueOf(bloque_id), Jpo.INTEGER);			

			ArrayList<Object> resultado = (ArrayList<Object>) pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado.get(1), ColaTrabajo.class);
		
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}
}
	