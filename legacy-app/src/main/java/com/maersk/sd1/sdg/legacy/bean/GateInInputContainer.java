package com.maersk.sd1.sdg.legacy.bean;

public class GateInInputContainer {
	
	private int programacion_nave_detalle_id;
	private int documento_carga_detalle_id;
	private int planning_detail_id;
	private int equipment_id;
	private String equipment_number;
	private int iso_code_id;
	private String manifest_weight;
	private int manifest_weight_unit_id;
	private String temperature;
	private int temperature_unit_id;
	private String seal_1;
	private String seal_2;
	private String seal_3;
	private String seal_4;
	private String reefer_dangerous;
	private Object imos;
	private String description_dangerous_substance;
	private Object pictures;
	private String aps_id;
	
	public int getProgramacion_nave_detalle_id() {
		return programacion_nave_detalle_id;
	}
	public void setProgramacion_nave_detalle_id(int programacion_nave_detalle_id) {
		this.programacion_nave_detalle_id = programacion_nave_detalle_id;
	}
	public int getDocumento_carga_detalle_id() {
		return documento_carga_detalle_id;
	}
	public void setDocumento_carga_detalle_id(int documento_carga_detalle_id) {
		this.documento_carga_detalle_id = documento_carga_detalle_id;
	}
	public int getPlanning_detail_id() {
		return planning_detail_id;
	}
	public void setPlanning_detail_id(int planning_detail_id) {
		this.planning_detail_id = planning_detail_id;
	}
	public int getEquipment_id() {
		return equipment_id;
	}
	public void setEquipment_id(int equipment_id) {
		this.equipment_id = equipment_id;
	}
	public String getEquipment_number() {
		return equipment_number;
	}
	public void setEquipment_number(String equipment_number) {
		this.equipment_number = equipment_number;
	}
	public int getIso_code_id() {
		return iso_code_id;
	}
	public void setIso_code_id(int iso_code_id) {
		this.iso_code_id = iso_code_id;
	}
	public String getManifest_weight() {
		return manifest_weight;
	}
	public void setManifest_weight(String manifest_weight) {
		this.manifest_weight = manifest_weight;
	}
	public int getManifest_weight_unit_id() {
		return manifest_weight_unit_id;
	}
	public void setManifest_weight_unit_id(int manifest_weight_unit_id) {
		this.manifest_weight_unit_id = manifest_weight_unit_id;
	}
	public String getTemperature() {
		return temperature;
	}
	public void setTemperature(String temperature) {
		this.temperature = temperature;
	}
	public int getTemperature_unit_id() {
		return temperature_unit_id;
	}
	public void setTemperature_unit_id(int temperature_unit_id) {
		this.temperature_unit_id = temperature_unit_id;
	}
	public String getSeal_1() {
		return seal_1;
	}
	public void setSeal_1(String seal_1) {
		this.seal_1 = seal_1;
	}
	public String getSeal_2() {
		return seal_2;
	}
	public void setSeal_2(String seal_2) {
		this.seal_2 = seal_2;
	}
	public String getSeal_3() {
		return seal_3;
	}
	public void setSeal_3(String seal_3) {
		this.seal_3 = seal_3;
	}
	public String getSeal_4() {
		return seal_4;
	}
	public void setSeal_4(String seal_4) {
		this.seal_4 = seal_4;
	}
	public String getReefer_dangerous() {
		return reefer_dangerous;
	}
	public void setReefer_dangerous(String reefer_dangerous) {
		this.reefer_dangerous = reefer_dangerous;
	}
	public Object getImos() {
		return imos;
	}
	public void setImos(Object imos) {
		this.imos = imos;
	}
	public String getDescription_dangerous_substance() {
		return description_dangerous_substance;
	}
	public void setDescription_dangerous_substance(String description_dangerous_substance) {
		this.description_dangerous_substance = description_dangerous_substance;
	}
	public Object getPictures() {
		return pictures;
	}
	public void setPictures(Object pictures) {
		this.pictures = pictures;
	}
	public String getAps_id() {
		return aps_id;
	}
	public void setAps_id(String aps_id) {
		this.aps_id = aps_id;
	}
	
}
