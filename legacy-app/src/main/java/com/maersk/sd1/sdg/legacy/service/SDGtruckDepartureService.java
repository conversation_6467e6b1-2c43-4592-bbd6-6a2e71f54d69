package com.maersk.sd1.sdg.legacy.service;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDGtruckDepartureService {

	@RequestMapping(value = "/sdgtruckDepartureList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.truck_departure_list","SDG");
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_local_id", Jpo.DECIMAL);
			pResult.input("Page", Jpo.INTEGER);
			pResult.input("Size", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("show_list_asignment_gate_out", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdgtruckDepartureRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.truck_departure_register","SDG");
			pResult.input("sub_business_unit_local_id", Jpo.DECIMAL);
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.input("pass_restriction", Jpo.INTEGER);
			pResult.input("languaje_id", Jpo.INTEGER);
			pResult.input("truck_out_date", Jpo.DATETIME);
			pResult.input("seal_1", Jpo.STRING);
			pResult.input("seal_2", Jpo.STRING);
			pResult.input("seal_3", Jpo.STRING);
			pResult.input("seal_4", Jpo.STRING);
			pResult.input("remarks", Jpo.STRING);
			pResult.input("reject", Jpo.CHARACTER);
			pResult.input("temperature", Jpo.STRING);
			pResult.input("temperature_unit", Jpo.DECIMAL);
			pResult.input("photos", Jpo.STRING);
			pResult.input("container_id", Jpo.INTEGER);
			pResult.input("chassis_id", Jpo.INTEGER);
			pResult.input("documento_carga_detalle_id", Jpo.INTEGER);
			pResult.input("planning_detail_id", Jpo.INTEGER);
			pResult.input("operation_code", Jpo.STRING);
			pResult.input("is_for_gate_out_assignation", Jpo.INTEGER);
			pResult.input("confirm_add_container_not_match", Jpo.INTEGER);
			pResult.output("resp_result", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			pResult.output("restriction_container_id", Jpo.DECIMAL);
			Object ohb_response = pResult.executeL();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdgtruckDeparturePrintTicket", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDeparturePrintTicket(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.truck_departure_print_ticket","SDG");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_impresion", Jpo.STRING);
			pResult.input("visualizarMontoDanos", Jpo.CHARACTER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdgeirSignatureRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgeirSignatureRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.eir_signature_register","SDG");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.input("driver_date_signature", Jpo.DATETIME);
			pResult.input("url_driver_signature", Jpo.STRING);
			pResult.input("url_inspector_signature", Jpo.STRING);
			pResult.input("url_signature_chassis_inspector", Jpo.STRING);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdgtruckDepartureGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.truck_departure_get","SDG");
			pResult.input("eir", Jpo.INTEGER);
			pResult.input("language", Jpo.INTEGER);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("is_for_gate_out_assignation", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdgtruckDepartureDelete", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureDelete(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.truck_departure_delete","SDG");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_local_id", Jpo.INTEGER);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("user_modification_id", Jpo.DECIMAL);
			pResult.output("resp_state", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			pResult.output("resp_flag_removeworkorder", Jpo.CHARACTER);
			Object ohb_response = pResult.executeL();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdgtruckDepartureRegisterBeforeyardv", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureRegisterBeforeyardv(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.truck_departure_register_beforeyardv","SDG");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.input("container_id", Jpo.INTEGER);
			pResult.input("user_registration_id", Jpo.INTEGER);
			pResult.output("resp_result", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			pResult.output("resp_flag_createworkorder", Jpo.CHARACTER);
			pResult.output("resp_container_number", Jpo.STRING);
			pResult.output("resp_empty_full_alias", Jpo.CHARACTER);
			Object ohb_response = pResult.execute();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdgtruckDepartureRejectContainerFind", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureRejectContainerFind(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdg.truck_departure_reject_container_find","SDG");
			pResult.input("eir_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}