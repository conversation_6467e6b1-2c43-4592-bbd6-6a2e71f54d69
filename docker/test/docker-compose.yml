version: '3.1'
services:
  sqlcmd:
    container_name: tools
    image: mcr.microsoft.com/mssql-tools:latest
    stdin_open: true
    environment:
      - MSSQL_SA_PASSWORD=password!123
      - MSSQL_DATABASE=sd1_db
    volumes:
      - ./data:/data
    depends_on:
        - db
    command: bash -c "chmod +x /data/wait-for-it.sh && /data/wait-for-it.sh --timeout=0 azure_edge:1433 && /opt/mssql-tools/bin/sqlcmd -S azure_edge -U sa -P 'password!123' -i /data/01_schema.sql && /opt/mssql-tools/bin/sqlcmd -S azure_edge -U sa -P 'password!123' -i /data/02_procedures.sql"
  db: 
    container_name: azure_edge
    image: mcr.microsoft.com/azure-sql-edge:latest
    environment: 
      - SA_PASSWORD=password!123
      - ACCEPT_EULA=Y 
      - DATABASE_NAME=sd1_db
      - LOGICAL_NAME=sd1_db
      - DUMP_NAME=dump.sql
    ports: 
      - 1433:1433
    volumes:
      - ./data:/data

  app:
    image: sd1-backend:latest
    ports:
      - 8080:8080