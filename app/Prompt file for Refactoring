I need your help to refactor large methods in my service files. 

service file path: "paste here the absolute service file path"

Please follow these steps:

1. Analyze my service file:
	List all methods with their line counts
	Identify methods exceeding 100-150 lines of code (LOC)
	Provide a high-level overview of the file's structure and purpose

2. Create a refactoring plan for each identified large method:
	Analyze the method's responsibilities
	Identify logical boundaries for separation
	Determine shared variables and state
	Propose smaller methods to replace the large one
	Outline how these methods will interact

3. Implement the refactoring iteratively:
	Extract one logical component at a time
	Maintain original behavior and error handling
	Use appropriate return mechanisms (HashMaps for multiple values if needed)
	Handle shared state and variable scope issues
	Compile after each change to catch errors early

4. Resolve any compilation errors:
	Use mvn clean compile
	If errors occur, analyze and fix them systematically
	Address issues with imports, variable declarations, and method signatures
	Fix null pointer risks with proper initialization and checks
	Focus on resolving one error at a time

5. The refactored code should pass mvn clean install , retain original business logic, and reduce method size to under 50–75 LOC where possible.

Note:
1. In case the module to be refactored is too long or you get warnings like too large of input to process, use the logical boundaries that you created
2. Send one part of the logic and refactor it and iteratively refactor the whole method keeping states
3. try to keep the logical boundaries short so you don't run into too large of an input
	
Important!!
Please refer to your memories file for context about previous refactoring work we've done together. This will help you understand my coding style and preferences.