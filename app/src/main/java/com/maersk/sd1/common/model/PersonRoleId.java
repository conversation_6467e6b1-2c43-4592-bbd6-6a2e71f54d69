package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class PersonRoleId implements Serializable {
    private static final long serialVersionUID = -1396761179094733384L;
    @NotNull
    @Column(name = "persona_id", nullable = false)
    private Integer personId;

    @NotNull
    @Column(name = "tipo_rol_persona_id", nullable = false)
    private Integer typeRolePersonId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        PersonRoleId entity = (PersonRoleId) o;
        return Objects.equals(this.typeRolePersonId, entity.typeRolePersonId) &&
                Objects.equals(this.personId, entity.personId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(typeRolePersonId, personId);
    }

}