package com.maersk.sd1.common.service;

import com.maersk.sd1.common.repository.MessageLanguageRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class MessageLanguageService {

    private final MessageLanguageRepository messageLanguageRepository;

    public String getMessage(String type, int code, Integer languageId) {
        String message = messageLanguageRepository.findMensaje(type, code, languageId);
        if (message == null || message.isEmpty()) {
            message = "Message (" + type + ":" + code + ":" + languageId + ")";
        }
        return message;
    }

    public String getMessage(String type, int code, int languageId,  Map<String, String> replacement) {

        String message = messageLanguageRepository.findMensaje(type, code, languageId);
        if (message == null || replacement == null) {
            return message;
        }

        // Replacing values
        for (Map.Entry<String, String> entry : replacement.entrySet()) {
            message = message.replace(entry.getKey(), entry.getValue());
        }

        return message;
    }

}