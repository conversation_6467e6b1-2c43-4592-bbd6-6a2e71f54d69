package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.MenuAction;
import com.maersk.sd1.common.model.MenuActionId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface MenuActionRepository extends JpaRepository<MenuAction, MenuActionId> {

    @Query("SELECT ma FROM MenuAction ma WHERE ma.menu.id = :menuId")
    List<MenuAction> findByMenuId(@Param("menuId") Integer menuId);
}