package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.MessageLanguage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface MessageLanguageRepository extends JpaRepository<MessageLanguage, Integer> {

    @Query(value = "SELECT ges.fn_MensajeTraducido(:messageType, :messageCode, :languageId)", nativeQuery = true)
    String fnTranslatedMessage(@Param("messageType") String messageType, @Param("messageCode") Integer messageCode, @Param("languageId") Integer languageId);

    @Query("SELECT m.message FROM MessageLanguage m WHERE UPPER(m.type) = UPPER(:type) AND m.code = :code AND m.language.id = :languageId AND m.active = true")
    String findMensaje(@Param("type") String type, @Param("code") int code, @Param("languageId") Integer languageId);

    @Query(value = "select adm.message_language_find(:type, :language_id, :fields)", nativeQuery = true)
    String findAdmMessage(@Param("type") String type, @Param("language_id") int languageId, @Param("fields") String fields);

    @Query(value = "SELECT adm.message_language_find(:type, :languageId, :fields)", nativeQuery = true)
    String findMessage(@Param("type") String type, @Param("languageId") Integer languageId, @Param("fields") String fields);

    @Query("SELECT m.message FROM MessageLanguage m " +
            "WHERE m.type = :type AND m.code = :code AND m.language.id = :languageId AND m.active = true")
    String findTranslatedMessage(String type, Integer code, Integer languageId);
}