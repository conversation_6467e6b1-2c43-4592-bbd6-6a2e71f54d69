package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "usuario_preguntas", schema = "seg")
public class UserQuestions {
    @Id
    @Column(name = "usuario_preguntas_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta1_id")
    private Catalog catQuestion1;

    @Size(max = 200)
    @Column(name = "respuesta1", length = 200)
    private String answer1;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta2_id")
    private Catalog catQuestion2;

    @Size(max = 200)
    @Column(name = "respuesta2", length = 200)
    private String answer2;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta3_id")
    private Catalog catQuestion3;

    @Size(max = 200)
    @Column(name = "respuesta3", length = 200)
    private String answer3;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta4_id")
    private Catalog catQuestion4;

    @Size(max = 200)
    @Column(name = "respuesta4", length = 200)
    private String answer4;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pregunta5_id")
    private Catalog catQuestion5;

    @Size(max = 200)
    @Column(name = "respuesta5", length = 200)
    private String answer5;

    @Column(name = "cant_preguntas_realizadas")
    private Integer questionsMadeQuantity;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

}