package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.GateTransmissionLocalSetting;
import com.maersk.sd1.common.model.GateTransmissionSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface GateTransmissionLocalSettingRepository extends JpaRepository<GateTransmissionLocalSetting, Integer> {

    void deleteByGateTransmissionSetting(GateTransmissionSetting gateTransmissionSetting);

    List<GateTransmissionLocalSetting> findByGateTransmissionSetting_IdAndActive(Integer gateTransmissionSettingId, Boolean active);

    /** GATE_IN_EMPTY condition:
     *  a.sendGateInEmpty = true, a.active = true, c.active = true.
     */
    @Query("select c from GateTransmissionLocalSetting c "
            + "join c.gateTransmissionSetting a "
            + "where a.sendGateInEmpty = true "
            + "  and a.active = true "
            + "  and c.active = true")
    List<GateTransmissionLocalSetting> findGateInEmpty();

    /** STATUS_ACTIVITY condition:
     *  a.sendStatusActivity = true, a.active = true, c.active = true,
     *  a.catStatusActivityFormat.id in (telex, 322)
     */
    @Query("select c from GateTransmissionLocalSetting c "
            + "join c.gateTransmissionSetting a "
            + "where a.sendStatusActivity = true "
            + "  and a.active = true "
            + "  and c.active = true "
            + "  and (a.catStatusActivityFormat.alias = 'sd1_messagetype_gatetrans_telex' "
            + "       or a.catStatusActivityFormat.alias = 'sd1_messagetype_gatetrans_322')")
    List<GateTransmissionLocalSetting> findStatusActivity();

    /** GATE_OUT_EMPTY condition:
     *  a.sendGateOutEmpty = true, a.active = true, c.active = true.
     */
    @Query("select c from GateTransmissionLocalSetting c "
            + "join c.gateTransmissionSetting a "
            + "where a.sendGateOutEmpty = true "
            + "  and a.active = true "
            + "  and c.active = true")
    List<GateTransmissionLocalSetting> findGateOutEmpty();

    /** GATE_IN_FULL condition:
     *  a.sendGateInFull = true, a.active = true, c.active = true.
     */
    @Query("select c from GateTransmissionLocalSetting c "
            + "join c.gateTransmissionSetting a "
            + "where a.sendGateInFull = true "
            + "  and a.active = true "
            + "  and c.active = true")
    List<GateTransmissionLocalSetting> findGateInFull();

    /** GATE_OUT_FULL condition:
     *  a.sendGateOutFull = true, a.active = true, c.active = true.
     */
    @Query("select c from GateTransmissionLocalSetting c "
            + "join c.gateTransmissionSetting a "
            + "where a.sendGateOutFull = true "
            + "  and a.active = true "
            + "  and c.active = true")
    List<GateTransmissionLocalSetting> findGateOutFull();

    @Query("""
            SELECT gtls FROM GateTransmissionLocalSetting gtls
            WHERE gtls.gateTransmissionSetting.shippingLine.id = :shippingLineId
            AND gtls.gateTransmissionSetting.subBusinessUnit.id = :subBusinessUnitId
            AND gtls.localSubBusinessUnit.id = :subBusinessUnitLocalId
            AND gtls.gateTransmissionSetting.sendStatusActivity = :sendActivityStatus
            AND gtls.gateTransmissionSetting.catStatusActivityFormat.id = :catFormatStatusActivityId
            AND gtls.gateTransmissionSetting.active = :active 
            """)
    Optional<GateTransmissionLocalSetting> findByShippingLineIdAndBusinessUnitsAndCatStatusActivityFormatAndActive(Integer shippingLineId, Integer subBusinessUnitId, Integer subBusinessUnitLocalId, boolean sendActivityStatus, Integer catFormatStatusActivityId, boolean active);
}