package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.NotificationUser;
import com.maersk.sd1.common.model.NotificationUserId;
import com.maersk.sd1.seg.dto.SystemValidateUserDTO;
import org.springframework.data.jpa.repository.JpaRepository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface NotificationUserRepository extends JpaRepository<NotificationUser, NotificationUserId> {

    @Query("SELECT DISTINCT " +
            "NTF AS notificationClass, " +
            "CASE " +
            "WHEN NTF.onlyTime = '0' AND NTF.onlyAlert = '1' THEN (SELECT count(NOL.notification.id) FROM NotificationRead NOL WHERE NOL.user.id = 1 AND NOL.notification.id = NTF.id) " +
            "WHEN NTF.onlyTime = '0' AND (NTF.onlyAlert IS NULL OR NTF.onlyAlert = '0') THEN 0 " +
            "WHEN NTF.onlyTime = '1' THEN 0 " +
            "END AS read "+
            "FROM NotificationUser NTU " +
            "INNER JOIN Notification NTF ON NTF.id = NTU.notification.id " +
            "WHERE " +
            "(:businessUnitId IS NULL OR NTF.businessUnit.id = :businessUnitId) AND " +
            "NTU.user.id = :userId AND " +
            "NTF.status = '1' AND " +
            "(NTF.onlyTime = '0' OR NOT EXISTS (SELECT 1 FROM NotificationRead NOL WHERE NOL.user.id = 1 AND NOL.notification.id = NTF.id)) AND " +
            "(NTF.expirationDate IS NULL OR (NTF.expirationDate IS NOT NULL AND CURRENT_TIMESTAMP <= NTF.expirationDate))")
    List<SystemValidateUserDTO> getUserDetails(@Param("userId") Integer userId, @Param("businessUnitId") Integer businessUnitId);

    @Transactional
    @Modifying
    @Query("INSERT INTO NotificationUser (notification.id, user.id) " +
            "SELECT DISTINCT :notificationId, ur.user.id FROM UserRole ur WHERE ur.role.id IN :roleIds")
    void insertNotificationUsers(@Param("notificationId") int notificationId, @Param("roleIds") List<Integer> roleIds);

}