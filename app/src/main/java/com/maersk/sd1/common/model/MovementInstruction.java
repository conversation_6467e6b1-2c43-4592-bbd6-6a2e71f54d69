package com.maersk.sd1.common.model;

import com.maersk.sd1.sdy.dto.ResponseInstruccionMovimientoConfirma1;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "InstruccionMovimiento.instrucccionMovimientoConfirma1",
        procedureName = "sdy.instrucccion_movimiento_confirma1",
        resultClasses = ResponseInstruccionMovimientoConfirma1.class,
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "movement_instruction_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "user_id", type = Integer.class)
        }
)
@Table(name = "instruccion_movimiento", schema = "sdy", indexes = {
        @Index(name = "nci_msft_1_instruccion_movimiento_D704D287C17A73484E86B9805446D5C2", columnList = "eir_id, activo, cat_operacion_id, contenedor_id, estado_id, patio_id"),
        @Index(name = "nci_msft_1_instruccion_movimiento_8DE58339E1CCCEC2A36A1A9D366F13B5", columnList = "activo, estado_id, destino_bloque_id, destino40_celda_id"),
        @Index(name = "nci_msft_1_instruccion_movimiento_B34B74A33F4BBC4FE403A6BBAE71F3C5", columnList = "estado_id, cat_tipo_movimiento_id, contenedor_id"),
        @Index(name = "nci_msft_1_instruccion_movimiento_A00A00250D3E75803A11349E24939BB1", columnList = "eir_reference_id")
})
public class MovementInstruction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "instruccion_movimiento_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "contenedor_id", nullable = false)
    private Container container;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "origen_patio_id", nullable = false)
    private Yard originYard;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "origen_bloque_id", nullable = false)
    private Block originBlock;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "origen_celda_id", nullable = false)
    private Cell originCell;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "origen_nivel_id", nullable = false)
    private Level originLevel;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "origen40_patio_id")
    private Yard origin4OYard;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "orgein40_bloque_id")
    private Block origin40Block;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "origen40_celda_id")
    private Cell origin40Cell;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "origen40_nivel_id")
    private Level origin40Level;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "destino_patio_id", nullable = false)
    private Yard destinationYard;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "destino_bloque_id", nullable = false)
    private Block destinationBlock;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "destino_celda_id", nullable = false)
    private Cell destinationCell;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "destino_nivel_id", nullable = false)
    private Level destinationLevel;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino40_patio_id")
    private Yard destination40Yard;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino40_bloque_id")
    private Block destination40Block;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino40_celda_id")
    private Cell destination40Cell;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino40_nivel_id")
    private Level destination40Level;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino_propuesto_patio_id")
    private Yard proposedDestinationYard;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino_propuesto_bloque_id")
    private Block proposedDestinationBlock;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino_propuesto_celda_id")
    private Cell proposedDestinationCell;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destino_propuesto_nivel_id")
    private Level proposedDestinationLevel;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recurso_id")
    private Resource resource;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_operador_id")
    private User operatorUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "estado_id")
    private Catalog catStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patio_id")
    private Yard yard;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_id")
    private Catalog cat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cola_trabajo_id")
    private WorkQueue workQueue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "codigo_grupo_id")
    private GroupCode groupCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_operacion_id")
    private Catalog catOperation;

    @Column(name = "fecha_ejecucion")
    private LocalDateTime executionDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "camion_id")
    private Truck truck;

    @Size(max = 200)
    @Nationalized
    @Column(name = "comentario", length = 200)
    private String comment;

    @ColumnDefault("1")
    @Column(name = "activo")
    private Boolean active;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "secuencia", nullable = false)
    private Integer sequence;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tipo_movimiento_id")
    private Catalog catMovementType;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "es_inminente", nullable = false)
    private Boolean isImminent = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "release_order_id")
    private Booking releaseOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "instruccion_movimiento_padre_id")
    private MovementInstruction parentMovementInstruction;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "requiere_camion", nullable = false)
    private Boolean requiresTruck = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_id")
    private Eir eir;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_reference_id")
    private Eir referenceEir;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_container_restore_id")
    private EirContainerRestore eirContainerRestore;

}