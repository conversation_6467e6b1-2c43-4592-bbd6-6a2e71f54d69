package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.BookingDetail;
import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.sde.dto.BookingDetailsProcessDTO;
import com.maersk.sd1.sde.dto.DocumentationEmptyListTb01Projection;
import com.maersk.sd1.sds.dto.*;
import com.maersk.sd1.sds.service.BookingDetailEditService;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Primary
@Repository
public interface BookingDetailRepository extends JpaRepository<BookingDetail, Integer> {
    @Query("select b from BookingDetail b where b.id = :id and b.active = true")
    BookingDetail findByIdAndActiveTrue(@Param("id") Integer id);

    @Query(value = "SELECT det.booking_detalle_id, det.booking_id, det.cat_tamano_id, "
            + "CATTAM.descripcion as cat_tamano_desc, det.cat_tipo_contenedor_id, "
            + "IIF(ISNULL(remark_rules_name,'')='FLAG_TO_FLEX','DC/HC',CATTIP.descripcion) as cat_tipo_contenedor_desc, "
            + "det.cantidad_reserva, det.cantidad_atendida, det.carga_maxima_requerido, "
            + "det.cat_origen_creacion_booking_id, det.activo, det.usuario_registro_id, "
            + "usucre.nombres as usuario_registro_nombre, "
            + "(usucre.apellido_paterno + ' ' + usucre.apellido_materno) as usuario_registro_apellidos, "
            + "det.fecha_registro, det.usuario_modificacion_id, usumod.nombres as usuario_modificacion_nombre, "
            + "(usumod.apellido_paterno + ' ' + usumod.apellido_paterno) as usuario_modificacion_apellidos, "
            + "det.fecha_modificacion "
            + "FROM sds.booking_detalle det (NOLOCK) "
            + "LEFT JOIN seg.usuario USUCRE (NOLOCK) on USUCRE.usuario_id = det.usuario_registro_id "
            + "LEFT JOIN seg.usuario USUMOD (NOLOCK) on USUMOD.usuario_id = det.usuario_modificacion_id "
            + "LEFT JOIN ges.catalogo CATTAM (NOLOCK) on CATTAM.catalogo_id = det.cat_tamano_id "
            + "LEFT JOIN ges.catalogo CATTIP (NOLOCK) on CATTIP.catalogo_id = det.cat_tipo_contenedor_id "
            + "WHERE det.booking_id = :bookingId AND activo = 1", nativeQuery = true)
    List<Object[]> findBookingDetailByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT b FROM BookingDetail b WHERE b.id = :id")
    BookingDetail findByBookingDetailId(@Param("id") Integer id);

    @Query("SELECT bd FROM BookingDetail bd WHERE id IN :bookingDetailIds AND bd.active = true")
    List<BookingDetail> findByBookingIdsAndActive(@Param("bookingDetailIds") List<Integer> bookingDetailIds);

    @Query("SELECT new com.maersk.sd1.sds.dto.BookDetailDTO(b.catSize.id, b.catContainerType.id, b.maximumLoadRequired, b.reservationQuantity) " +
            "FROM BookingDetail b " +
            "WHERE b.id = :bookingDetailId")
    List<BookDetailDTO> getBookingDetails(@Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT new com.maersk.sd1.sds.dto.BookingInfoDTO(b.approvedBooking, " +
            "b.vesselProgrammingDetail.id, " +
            "b.bookingNumber, " +
            "b.imo.id, " +
            "b.commodity, " +
            "CASE " +
            "  WHEN v.catOperation.id IN (42994, 42995, 47734, 47735) THEN 43007 " +
            "  ELSE 43004 " +
            "END, " +
            "COALESCE(b.remarkRulesName, '')) " +
            "FROM Booking b " +
            "JOIN b.vesselProgrammingDetail v " +
            "WHERE b.id = :bookingId")
    List<BookingInfoDTO> fetchBookingDetails(@Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE BookingDetail b " +
            "SET b.catSize.id = :sizeCategoryId, " +
            "b.catContainerType.id = :containerTypeId, " +
            "b.reservationQuantity = :reservedQuantity, " +
            "b.maximumLoadRequired = :maxRequiredLoad, " +
            "b.modificationUser.id = :modifiedByUserId, " +
            "b.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE b.id = :bookingDetailId")
    void updateBookingDetail(@Param("sizeCategoryId") Integer sizeCategoryId,
                             @Param("containerTypeId") Integer containerTypeId,
                             @Param("reservedQuantity") Double reservedQuantity,
                             @Param("maxRequiredLoad") Double maxRequiredLoad,
                             @Param("modifiedByUserId") Integer modifiedByUserId,
                             @Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT CASE WHEN COALESCE(c.code, '0') = '1' THEN 1 ELSE 0 END " +
            "FROM Catalog c " +
            "WHERE c.id = :containerTypeId AND c.parentCatalog.id = 164")
    Integer getRefrigeratedLoadStatus(@Param("containerTypeId") Integer containerTypeId);

    @Query("SELECT c.id " +
            "FROM CargoDocument c " +
            "WHERE c.cargoDocument = :bookingNumber " +
            "AND c.vesselProgrammingDetail.id = :shipProgrammingDetailId " +
            "AND c.active = true")
    Integer getDocumentoCargaId(@Param("bookingNumber") String bookingNumber,
                                @Param("shipProgrammingDetailId") Integer shipProgrammingDetailId);

    @Query("SELECT COUNT(dcd.id) " +
            "FROM CargoDocumentDetail dcd " +
            "WHERE dcd.bookingDetail.id = :bookingDetailId AND dcd.active = true")
    Integer getActiveLoadDocumentDetailsCount(@Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT COUNT(dcd.id) " +
            "FROM CargoDocumentDetail dcd " +
            "WHERE dcd.bookingDetail.id = :bookingDetailId " +
            "AND dcd.active = true " +
            "AND dcd.container IS NOT NULL")
    Integer getDocumentoCargaDetalleCount(@Param("bookingDetailId") Integer bookingDetailId);


    @Modifying
    @Query("UPDATE CargoDocumentDetail dcd " +
            "SET dcd.catManifestedSize.id = :sizeCategoryId, " +
            "dcd.catManifestedContainerType.id = :containerTypeId, " +
            "dcd.isDangerousCargo = :isDangerousCargo, " +
            "dcd.isRefrigeratedCargo = :refrigeratedLoad, " +
            "dcd.traceCargoDocumentDetail = 'upd_tipotño_bk', " +
            "dcd.modificationUser.id = :modifiedByUserId, " +
            "dcd.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE dcd.bookingDetail.id = :bookingDetailId AND dcd.active = true")
    void updateCargoDetail1(@Param("sizeCategoryId") Integer sizeCategoryId,
                            @Param("containerTypeId") Integer containerTypeId,
                            @Param("isDangerousCargo") Boolean isDangerousCargo,
                            @Param("refrigeratedLoad") Boolean refrigeratedLoad,
                            @Param("modifiedByUserId") Integer modifiedByUserId,
                            @Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT dcd.id " +
            "FROM CargoDocumentDetail dcd " +
            "WHERE dcd.bookingDetail.id = :bookingDetailId " +
            "AND dcd.container IS NULL " +
            "AND COALESCE(dcd.receivedQuantity, 0) = 0 " +
            "AND dcd.active = true " +
            "ORDER BY dcd.id")
    Integer findDocumentoCargaDetalleId(@Param("bookingDetailId") Integer bookingDetailId);

    @Modifying
    @Query("UPDATE CargoDocumentDetail dcd " +
            "SET dcd.active = false, " +
            "dcd.modificationDate = CURRENT_TIMESTAMP, " +
            "dcd.modificationUser.id = :modifiedByUserId, " +
            "dcd.traceCargoDocumentDetail = 'upd-bk-detalle' " +
            "WHERE dcd.id = :cargoDocumentDetailId " +
            "AND dcd.container IS NULL " +
            "AND COALESCE(dcd.receivedQuantity, 0) = 0 " +
            "AND dcd.active = true")
    void updateCargoDetail(@Param("modifiedByUserId") Integer modifiedByUserId,
                           @Param("cargoDocumentDetailId") Integer cargoDocumentDetailId);

    @Query(value = "SELECT new com.maersk.sd1.sds.dto.ApprovedBookingDetailDTO(" +
            ":#{#detail.cargoDocumentoId}, " +
            "a.producto_id, " +
            ":#{#detail.packagingTypeId}, " +
            "1, " +
            "1, " +
            "0, " +
            ":#{#detail.weightUnitId}, " +
            "1, " +
            ":#{#detail.isDangerousCargo}, " +
            "c.CargaRefrigerada, " +
            ":#{#detail.merchandise}, " +
            ":#{#detail.loadConditionId}, " +
            "0, " +
            "1, " +
            ":#{#detail.bookingCreationSourceId}, " +
            ":#{#detail.modifiedByUserId}, " +
            "CURRENT_TIMESTAMP, " +
            "c.cat_tipo_contenedor_id, " +
            "c.cat_tamano_id, " +
            ":#{#detail.quantityUnitId}, " +
            "c.booking_detalle_id, " +
            "'APROB_BK_03') " +
            "FROM sds.booking a " +
            "JOIN sds.container c ON a.booking_id = c.booking_id " +
            "WHERE a.booking_id = :#{#detail.tempBookingId} " +
            "AND a.booking_id = :#{#detail.bookingId} " +
            "ORDER BY c.doItem", nativeQuery = true)
    List<ApprovedBookingDetailDTO> fetchApprovedBookingDetails(@Param("detail") BookingDetailEditService.ApprovedBookingDetail detail);


    @Modifying
    @Query(value = "INSERT INTO sds.documento_carga_detalle " +
            "(documento_carga_id, producto_id, cat_embalaje_id, cantidad_manifestada, peso_manifestado, " +
            "volumen_manifestado, cat_unidad_medida_peso_id, dice_contener, es_carga_peligrosa, es_carga_refrigerada, " +
            "mercaderia, cat_condicion_carga_id, gateout_empty_liquidado, activo, cat_origen_creacion_id, usuario_registro_id, " +
            "fecha_registro, cat_tipo_contenedor_manifestado_id, cat_tamano_manifestado_id, cat_unidad_medida_cantidad_id, " +
            "booking_detalle_id, trace_doc_carga_detalle) " +
            "VALUES (" +
            ":#{#detalle.documentoCargaId}, " +
            ":#{#detalle.productoId}, " +
            ":#{#detalle.catEmbalajeId}, " +
            ":#{#detalle.cantidadManifestada}, " +
            ":#{#detalle.pesoManifestado}, " +
            ":#{#detalle.volumenManifestado}, " +
            ":#{#detalle.catUnidadMedidaPesoId}, " +
            ":#{#detalle.diceContener}, " +
            ":#{#detalle.esCargaPeligrosa}, " +
            ":#{#detalle.esCargaRefrigerada}, " +
            ":#{#detalle.mercaderia}, " +
            ":#{#detalle.catCondicionCargaId}, " +
            ":#{#detalle.gateoutEmptyLiquidado}, " +
            ":#{#detalle.activo}, " +
            ":#{#detalle.catOrigenCreacionId}, " +
            ":#{#detalle.usuarioRegistroId}, " +
            ":#{#detalle.fechaRegistro}, " +
            ":#{#detalle.catTipoContenedorManifestadoId}, " +
            ":#{#detalle.catTamanoManifestadoId}, " +
            ":#{#detalle.catUnidadMedidaCantidadId}, " +
            ":#{#detalle.bookingDetalleId}, " +
            ":#{#detalle.traceDocCargaDetalle})", nativeQuery = true)
    void insertDocumentoCargaDetalle(@Param("detalle") BookingDetailEditService.DocumentCargo detalle);

    @Query("SELECT new com.maersk.sd1.sds.dto.ApproveBookingTempDoDetail(bd.id,bd.reservationQuantity,bd.catSize.id,bd.catContainerType.id)  FROM BookingDetail bd WHERE bd.booking.id = :bookingId AND bd.active = true")
    List<ApproveBookingTempDoDetail> findApproveBookingDetailsByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT c.id FROM BookingDetail a " +
            "INNER JOIN CargoDocumentDetail b ON a.id = b.bookingDetail.id " +
            "INNER JOIN CargoDocument c ON b.cargoDocument.id = c.id " +
            "WHERE a.booking.id = :bookingId " +
            "AND a.active = true AND b.active = true AND c.active = true")
    Integer findCargoDocumentIdByBookingId(@Param("bookingId") Integer bookingId);

    @Query(value = """
            SELECT new com.maersk.sd1.sds.dto.DODetail(
                b.cargoDocument.id,
                b.id,
                b.container.id,
                COALESCE(b.receivedQuantity, 0),
                b.catManifestedContainerType.id,
                b.catManifestedSize.id,
                b.active,
                b.bookingDetail.id,
                0)
            FROM BookingDetail a
            INNER JOIN CargoDocumentDetail b ON a.id = b.bookingDetail.id
            INNER JOIN CargoDocument c ON b.cargoDocument.id = c.id
            WHERE a.booking.id = :bookingId
              AND a.active = true
              AND b.active = true
              AND c.active = true
            """)
    List<DODetail> findDODetailsByBookingId(@Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE BookingDetail b " +
            "SET b.remarkRulesName = :ediRemarkRulesName, " +
            "    b.modificationUser.id = :userId, " +
            "    b.modificationDate = CURRENT_TIMESTAMP, " +
            "    b.traceBkDetail = :traceDetail " +
            "WHERE b.booking.id = :bookingId " +
            "  AND COALESCE(b.remarkRulesName, '') <> :ediRemarkRulesName " +
            "  AND b.active = true")
    int updateBookingDetailsRemarkRules(
            @Param("ediRemarkRulesName") String ediRemarkRulesName,
            @Param("userId") Integer userId,
            @Param("traceDetail") String traceDetail,
            @Param("bookingId") Integer bookingId
    );

    @Query("SELECT new com.maersk.sd1.sds.dto.MyDetail( " +
            "    bd.catSize.id, " +
            "    bd.catContainerType.id, " +
            "    CAST(SUM(bd.reservationQuantity) AS integer)  " +
            ") " +
            "FROM BookingDetail bd " +
            "WHERE bd.booking.id = :bookingId " +
            "  AND bd.active = true " +
            "GROUP BY bd.catSize.id, bd.catContainerType.id")
    List<MyDetail> findBookingDetailsAggregated(@Param("bookingId") Integer bookingId);

    @Modifying
    @Query("DELETE FROM BookingDetail b WHERE b.booking.id = :bookingId")
    int deleteByBookingId(@Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE BookingDetail b " +
            "SET b.active = false, b.traceBkDetail = CONCAT('del_by_edi ', :ediCoparnId), " +
            "b.modificationUser.id = :userId, b.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE b.booking.id = :bookingId AND b.active = true")
    int updateActiveBookingDetails(@Param("ediCoparnId") String ediCoparnId,
                                   @Param("userId") Integer userId, @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE BookingDetail bd " +
            "SET bd.reservationQuantity = :quantity, " +
            "bd.modificationDate = CURRENT_TIMESTAMP, " +
            "bd.modificationUser.id = :userId, " +
            "bd.traceBkDetail = :traceDetail, " +
            "bd.bookingEdiReference.id = :ediCoparnId " +
            "WHERE bd.id = :bookingDetailId AND bd.active = true")
    int updateReservationQuantity(@Param("quantity") Integer quantity,
                                  @Param("userId") Integer userId,
                                  @Param("traceDetail") String traceDetail,
                                  @Param("ediCoparnId") Integer ediCoparnId,
                                  @Param("bookingDetailId") Integer bookingDetailId);

    @Modifying
    @Query("UPDATE BookingDetail b " +
            "SET b.active = false, " +
            "b.modificationDate = CURRENT_TIMESTAMP, " +
            "b.modificationUser.id = :userId, " +
            "b.traceBkDetail = 'edi_upd_Qty=0', " +
            "b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE COALESCE(b.reservationQuantity, 0) = 0 " +
            "AND COALESCE(b.attendedQuantity, 0) = 0 " +
            "AND b.active = true")
    int updateInactiveBookingDetails(@Param("userId") Integer userId, @Param("ediCoparnId") Integer ediCoparnId);

    @Query("""
            SELECT new com.maersk.sd1.sds.dto.NewBKDetail(
                bd.id AS bookingDetailId,
                bd.reservationQuantity AS reservationQuantity,
                bd.catSize.id AS catSize,
                bd.catContainerType.id AS catContainerType
            )
            FROM BookingDetail bd
            LEFT JOIN CargoDocumentDetail cdd
                ON bd.id = cdd.bookingDetail.id AND cdd.active = TRUE
            WHERE bd.booking.id = :bookingId
            AND bd.active = TRUE
            AND cdd.bookingDetail.id IS NULL
            """)
    List<NewBKDetail> findNewBookingDetailsByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT " +
            " bd.catSize.description AS description, " +
            " CASE WHEN bd.remarkRulesName = 'FLAG_TO_FLEX' THEN 'DC/HC' ELSE bd.catContainerType.description END AS flag, " +
            " bd.reservationQuantity AS resQuantity, " +
            " bd.attendedQuantity AS attendQuantity, " +
            " bd.maximumLoadRequired AS maxRequired, " +
            " bd.id AS id " +
            "FROM BookingDetail bd " +
            "WHERE bd.booking.id = :bookingId " +
            "  AND bd.active = true")
    List<BookingDetailsProcessDTO> listBookingDetalleByBookingId(@Param("bookingId") Integer bookingId);


    @Query("SELECT bd FROM BookingDetail bd WHERE bd.booking.id = :bookingId AND bd.catSize.id = :catSizeId AND (bd.reservationQuantity - bd.attendedQuantity) > 0 AND bd.active = :active")
    List<BookingDetail> findPendingBookingDetailByBookingIdCatSizeIdAndActiveTrue(Integer bookingId, Integer catSizeId, Boolean active);

    @Query("SELECT bd FROM BookingDetail bd WHERE bd.booking.id = :bookingId AND bd.catContainerType.id IN :catTypeIds AND (bd.reservationQuantity - bd.attendedQuantity) > 0 AND bd.active = :active")
    List<BookingDetail> findPendingBookingDetailByBookingIdCatTypeIdsAndActiveTrue(Integer bookingId, List<Integer> catTypeIds, Boolean active);

    @Query("SELECT new com.maersk.sd1.sds.dto.ApproveBookingTempDoDetail(bd.id,bd.reservationQuantity,bd.catSize.id,bd.catContainerType.id)  FROM BookingDetail bd LEFT JOIN CargoDocumentDetail cdd ON bd.id=cdd.bookingDetail.id AND cdd.active=true WHERE bd.booking.id = :bookingId AND cdd.bookingDetail.id IS NULL")
    List<ApproveBookingTempDoDetail> findBookingDetailsByBookingIdAndCargoDocumentId(@Param("bookingId") Integer bookingId);


    @Query("SELECT cdd FROM BookingDetail p " +
            "join CargoDocumentDetail cdd on cdd.bookingDetail.id = p.id " +
            "WHERE  p.id = :bookingDetailId " +
            "AND cdd.container is null " +
            "AND p.active = true AND cdd.active = true " +
            "ORDER BY p.id")
    List<CargoDocumentDetail> findPreAssigmentByBookingDetailId(@Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT COUNT(cdd.id) FROM BookingDetail bd " +
            "join CargoDocumentDetail cdd on bd.id = cdd.bookingDetail.id " +
            "WHERE cdd.bookingDetail.id = :bookingDetailId " +
            "AND cdd.active = true and cdd.container is not null")
    Optional<Integer> getAttendedQuantityByBookingDetailId(Integer bookingDetailId);
    
    @Modifying
    @Query("""
            UPDATE BookingDetail bd
            SET bd.attendedQuantity = bd.attendedQuantity - 1,
            bd.modificationUser.id = :modificationUserId,
            bd.modificationDate = CURRENT_TIMESTAMP,
            bd.traceBkDetail = :trace
            WHERE bd.id = :bookingDetailId
            """)
    void unassignById(Integer bookingDetailId, Integer modificationUserId, String trace);

    @Modifying
    @Query("""
            UPDATE BookingDetail bd
            SET bd.attendedQuantity = bd.attendedQuantity + 1,
            bd.modificationUser.id = :modificationUserId,
            bd.modificationDate = CURRENT_TIMESTAMP,
            bd.traceBkDetail = :trace,
            bd.catContainerType.id = :catContainerTypeId
            WHERE bd.id = :bookingDetailId
            """)
    void assignContainerTypeAndIncreaseAttendedQuantityById(Integer bookingDetailId, Integer modificationUserId, String trace, Integer catContainerTypeId);

    @Query("SELECT bd FROM BookingDetail bd WHERE bd.id = :bookingDetailId AND bd.active = true AND bd.booking.active = true")
    Optional<BookingDetail> findActiveById(Integer bookingDetailId);

    @Query("""
            SELECT CASE WHEN count(bd) > 0 THEN true ELSE false END
            FROM BookingDetail bd
            JOIN bd.booking bkg
            WHERE bkg.id = :bookingId
            AND bd.catSize.id = :equipmentSizeId
            AND bd.catContainerType.id = :equipmentTypeId
            AND bd.reservationQuantity > 0
            AND bd.active = true
            AND bkg.active = true
            """)
    List<BookingDetail> findActiveByBookingEquipmentSizeEquipmentTypeWithAvailability(Pageable pageable, Integer bookingId, Integer equipmentSizeId, Integer equipmentTypeId);

    @Modifying
    @Query("""
            UPDATE BookingDetail bd
            SET bd.attendedQuantity = bd.attendedQuantity - 1,
            bd.modificationUser.id = :modificationUserId,
            bd.modificationDate = CURRENT_TIMESTAMP,
            bd.traceBkDetail = :trace
            WHERE bd.id = :bookingDetailId
            """)
    void substractAttendedQuantityByBookingDetailId(Integer bookingDetailId, Integer modificationUserId, String trace);

    @Modifying
    @Query("""
            UPDATE BookingDetail bd
            SET bd.attendedQuantity = bd.attendedQuantity + 1,
            bd.modificationUser.id = :modificationUserId,
            bd.modificationDate = CURRENT_TIMESTAMP,
            bd.traceBkDetail = :trace
            WHERE bd.id = :bookingDetailId
            """)
    void increaseAttendedQuantityById(Integer bookingDetailId, Integer modificationUserId, String trace);

    @Modifying
    @Query("""
    UPDATE BookingDetail bd
    SET bd.active = false,
        bd.traceBkDetail = LEFT(CONCAT('delxrel ', :ediCoparnId), 20),
        bd.modificationUser.id = :userModificationId,
        bd.modificationDate = :modificationDate
    WHERE bd.booking.id = :bookingId
    AND bd.active = true
    """)
    void deactivateBookingDetails(@Param("bookingId") Integer bookingId,
                                  @Param("ediCoparnId") Integer ediCoparnId,
                                  @Param("userModificationId") Integer userModificationId,
                                  @Param("modificationDate") LocalDateTime modificationDate);

    @Query("SELECT COUNT(bd) FROM BookingDetail bd WHERE bd.booking.id = :bookingId AND bd.attendedQuantity > 0")
    Integer findIfBookingDetailAttended(@Param("bookingId") Integer bookingId);

    @Query("SELECT bd.catSize.id, bd.catContainerType.id, SUM(bd.reservationQuantity) " +
            "FROM BookingDetail bd " +
            "WHERE bd.booking.id = :bookingId " +
            "GROUP BY bd.catSize.id, bd.catContainerType.id")
    List<Object[]> findBookingDetailsGroupedBySizeAndType(@Param("bookingId") Integer bookingId);

    @Query("select b from BookingDetail b where b.id = :id and b.catSize.id > :cntDimenId and b.catContainerType.id = :cntTipoId and b.active= true order by registrationDate ")
    Optional<BookingDetail> findByIdAndCatSizeAndCatContainerTypeAndActiveTrueOrderByRegistrationDate(@Param("id") Integer id, @Param("cntDimenId") Integer cntDimenId, @Param("cntTipoId") Integer cntTipoId);

    Boolean existsByBookingIdAndAttendedQuantityGreaterThanAndActive(Integer bookingId, int attendedQuantity, boolean active);

    @Query("SELECT bd FROM BookingDetail bd WHERE bd.booking = :booking")
    List<BookingDetail> findByBooking(@Param("booking") Booking booking);

    @Query(value = "select count(cdd.id) from BookingDetail b " +
            "inner join CargoDocumentDetail cdd on cdd.bookingDetail.id = b.id " +
            "where b.id = :bookingDetalleId " +
            "and cdd.container is not null " +
            "and cdd.active = true and b.active = true")
    Optional<Integer> countCargoDocumentDetailByIdAndContainerIsNotNull(@Param("bookingDetalleId") Integer bookingDetalleId);
    
    @Query("SELECT COALESCE(SUM(bd.attendedQuantity), 0) FROM BookingDetail bd WHERE bd.booking.id = :bookingId AND bd.active = true")
    Integer sumAttendedByBooking(@Param("bookingId") Integer bookingId);
           
    @Query("SELECT bd FROM BookingDetail bd WHERE bd.booking.id = :bookingId")
    List<BookingDetail> findByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT bd FROM BookingDetail bd "
            + "JOIN FETCH bd.booking b "
            + "WHERE bd.active = true AND b.active = true AND bd.id = :bookingDetailId")
    Optional<BookingDetail> findBookingDetailWithBooking(@Param("bookingDetailId") Integer bookingDetailId);

    @Query(value = "SELECT t.bookingBlId AS bookingBlId, t.sizeContainerId AS sizeContainerId, " +
            "       CASE WHEN t.remarkRule = 'FLAG_TO_FLEX' THEN :isTypeContainerDry ELSE t.originalTypeContainerId END AS typeContainerId, " +
            "       t.totalQuantity AS totalQuantity, t.remarkRule AS remarkRule " +
            "FROM ( " +
            "    SELECT bkdetx.booking_id AS bookingBlId, " +
            "           bkdetx.cat_tamano_id AS sizeContainerId, " +
            "           bkdetx.cat_tipo_contenedor_id AS originalTypeContainerId, " +
            "           SUM(bkdetx.cantidad_reserva) AS totalQuantity, " +
            "           ISNULL(bkdetx.remark_rules_name, '') AS remarkRule " +
            "    FROM sds.booking_detalle AS bkdetx WITH (NOLOCK) " +
            "    WHERE bkdetx.activo = 1 AND bkdetx.booking_id IN :emptyDocumentIds " +
            "    GROUP BY bkdetx.booking_id, bkdetx.cat_tamano_id, bkdetx.cat_tipo_contenedor_id, ISNULL(bkdetx.remark_rules_name, '') " +
            ") t", nativeQuery = true)
    List<DocumentationEmptyListTb01Projection> getDocumentationEmptyListTb01(@Param("isTypeContainerDry") Integer isTypeContainerDry,
                                                                             @Param("emptyDocumentIds") List<Integer> emptyDocumentIds);
    @Query("""
            SELECT bd FROM BookingDetail bd 
            WHERE bd.booking.id = :bookingId
            AND bd.catSize.id = :containerSizeId
            AND bd.catContainerType.id IN :containerTypeIdList
            AND bd.remarkRulesName = :remarkRules
            AND bd.active = true
            AND bd.booking.active = true
            """)
    List<BookingDetail> findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListAndRemarkRule(Integer bookingId, Integer containerSizeId, List<Integer> containerTypeIdList, String remarkRules);

    @Query("""
            SELECT bd FROM BookingDetail bd 
            WHERE bd.booking.id = :bookingId
            AND bd.catSize.id = :containerSizeId
            AND bd.catContainerType.id IN :containerTypeIdList
            AND (bd.remarkRulesName IS NULL OR bd.remarkRulesName = '')
            AND bd.active = true
            AND bd.booking.active = true
            """)
    List<BookingDetail> findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListWithNoRemarkRule(Integer bookingId, Integer containerSizeId, List<Integer> containerTypeIdList);

    @Modifying
    @Query("""
            UPDATE BookingDetail bd
            SET bd.attendedQuantity = :attendedQuantity,
            bd.modificationUser.id = :userRegistrationId,
            bd.modificationDate = CURRENT_TIMESTAMP,
            bd.traceBkDetail = :trace,
            bd.catContainerType.id = :containerTypeId
            WHERE bd.id = :bookingDetailId
            """)
    void updateAttendedQuantity(Integer bookingDetailId, Integer attendedQuantity, String trace, Integer userRegistrationId, Integer containerTypeId);
}