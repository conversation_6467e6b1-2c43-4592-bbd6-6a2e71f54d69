package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.MenuActionId;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "menu_accion", schema = "seg")
public class MenuAction {
    @EmbeddedId
    private MenuActionId id;

    @MapsId("menuId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "menu_id", nullable = false)
    private Menu menu;

    @MapsId("typeActionId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_accion_id", nullable = false)
    private Catalog catActionType;

    @NotNull
    @Column(name = "defecto_activo", nullable = false)
    private Character defectActive;

}