package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "reporte", schema = "ges")
public class Report {
    @Id
    @Column(name = "reporte_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "menu_id", nullable = false)
    private Menu menu;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre", nullable = false, length = 100)
    private String name;

    @NotNull
    @Lob
    @Column(name = "descripcion", nullable = false)
    private String description;

    @NotNull
    @Lob
    @Column(name = "parametros", nullable = false)
    private String parameters;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "estado", nullable = false)
    private Boolean status = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 100)
    @NotNull
    @ColumnDefault("''")
    @Column(name = "nombre_store", nullable = false, length = 100)
    private String nameStore;

    @Size(max = 50)
    @Column(name = "id", length = 50)
    private String id1;

    @Nationalized
    @Lob
    @Column(name = "columns")
    private String columns;

    public Report(Integer id) {
        this.id = id;
    }

}