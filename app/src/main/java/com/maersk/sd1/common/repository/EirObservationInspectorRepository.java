package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.EirObservationInspector;
import com.maersk.sd1.sde.dto.EirObservationInspectorProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EirObservationInspectorRepository extends JpaRepository<EirObservationInspector, Integer> {
    @Query(value = """
            SELECT	A.cat_observacion_eir_id as cat_observacion_eir_id,
                ROW_NUMBER() OVER(ORDER BY  [sds].[fn_CatalogoTraducidoDes](a.cat_observacion_eir_id,1)) as row,
                [sds].[fn_CatalogoTraducidoDes](a.cat_observacion_eir_id,:idiomaId)+
                iif(REPLACE(ISNULL(a.observacion_eir_otros,''),CHAR(10),' ')='','',': '+REPLACE(ISNULL(a.observacion_eir_otros,''),CHAR(10),' ')) as observacion_descripcion_secos
            FROM	[sde].[eir_observacion_inspector] AS A (NOLOCK)
            WHERE	eir_id = :eirId AND isnull(A.for_reefer,0) = :forReefer AND A.activo = 1
            ORDER BY 2
            """, nativeQuery = true)
    List<Object[]> findObservationsForEir(@Param("eirId") Integer eirId, @Param("idiomaId") Integer idiomaId, @Param("forReefer") Boolean forReefer);

    @Query(value = """
                SELECT 
                    ROW_NUMBER() OVER(ORDER BY sds.fn_CatalogoTraducidoDes(a.cat_observacion_eir_id,1)) as row,
                    sds.fn_CatalogoTraducidoDes(a.cat_observacion_eir_id, :languageId) +
                    IIF(REPLACE(ISNULL(a.observacion_eir_otros,''), CHAR(10), ' ') = '', '', ': ' + REPLACE(ISNULL(a.observacion_eir_otros,''), CHAR(10), ' ')) 
                    AS observacionDescripcion
                FROM sde.eir_observacion_inspector AS a (NOLOCK)
                WHERE a.eir_id = :eirId 
                AND ISNULL(a.for_reefer, 0) = :forReefer
                AND a.activo = 1
                ORDER BY 2
            """, nativeQuery = true)
    List<EirObservationInspectorProjection> findObservationsByEirIdAndType(
            @Param("eirId") Integer eirId,
            @Param("languageId") Integer languageId,
            @Param("forReefer") Integer forReefer
    );

    List<EirObservationInspector> findAllByEirIdAndActive(@Param("eirId") Integer eirId, @Param("active") Boolean active);

    @Query(value = """
            SELECT	A.cat_observacion_eir_id as cat_observacion_eir_id,
                [sds].[fn_CatalogoTraducidoDes](A.cat_observacion_eir_id,:idiomaId) as observacion_descripcion,
                REPLACE(ISNULL(a.observacion_eir_otros,''),CHAR(10),' ') as otros_descripcion
            FROM	[sde].[eir_observacion_inspector] AS A (NOLOCK)
            WHERE	eir_id = :eirId AND A.activo = 1
            ORDER BY 1
            """, nativeQuery = true)
    List<Object[]> findObservationsByEirId(@Param("eirId") Integer eirId, @Param("idiomaId") Integer idiomaId);

    @Query("SELECT o FROM EirObservationInspector o " +
            "WHERE o.eir.id = :eirId AND o.active = true " +
            "ORDER BY o.catObservationEIR.id")
    List<EirObservationInspector> findActiveByEirIdOrderByCatalogId(@Param("eirId") Integer eirId);
           
    @Modifying
    @Query("delete from EirObservationInspector e where e.eir.id = :eirId and e.catObservationEIR.id not in :catObservationEIRNotin")
    void deleteByEirIdAndCatObservationEIRNotin(@Param("eirId") Integer eirId, @Param("catObservationEIRNotin") List<String> catObservationEIRNotin);

    @Modifying
    @Query("delete from EirObservationInspector e where e.eir.id = :eirId and e.catObservationEIR.id = :catalogId")
    void deleteByEirIdAndCatObservation(@Param("eirId") Integer eirId, @Param("catalogId") Integer catalogId);

    @Query("select e from EirObservationInspector e where e.eir.id = :eirId and e.catObservationEIR.id = :catalogId ")
    List<EirObservationInspector> findByEirIdAndCatObservationEIRAndActiveTrue(@Param("eirId")Integer eirId,@Param("catalogId") Integer catalogId);
}