package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.dto.CatalogFindDTO;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.ges.dto.SearchReportOutput;
import com.maersk.sd1.seg.dto.CatalogOptionDto;
import com.maersk.sd1.sde.dto.MercplusComponentListOutput;
import com.maersk.sd1.sde.dto.MercplusDamageLocationListItemDto;
import com.maersk.sd1.sde.dto.MercplusRepairMethodListOutput;
import com.maersk.sd1.sdf.dto.CatalogDTO;
import com.maersk.sd1.seg.dto.MenuConfigurationsOutput;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CatalogRepository extends JpaRepository<Catalog, Integer>, JpaSpecificationExecutor<Catalog> {

    Catalog findByAlias(String alias);

    @Query("SELECT c.id FROM Catalog c WHERE c.alias = :alias")
    Integer findIdByAlias(@Param("alias") String alias);

    Boolean existsByAlias(String alias);

    Boolean existsByAliasIn(List<String> aliases);

    @Query(value = "SELECT cat.alias, cat.id FROM Catalog cat WHERE cat.alias IN :aliases")
    List<Object[]> findIdsByAliases(@Param("aliases") List<String> aliases);

    @Query(value = "SELECT c.id, c.variable2 FROM Catalog c WHERE c.id IN :catalogsIds")
    List<Object[]>findVariable2ByIds(@Param("catalogsIds") List<Integer> catalogsIds);

    @Query(value = "SELECT c.variable1 FROM Catalog c WHERE c.alias = :alias")
    String findVariable1ByAlias(String alias);

    @Query("SELECT new com.maersk.sd1.common.dto.CatalogFindDTO(c.id, c.alias) FROM Catalog c WHERE c.alias IN :aliases")
    List<CatalogFindDTO> findIdsByAliasesIn(@Param("aliases") List<String> aliases);

    @Query("SELECT c.description FROM Catalog c WHERE c.id = :catalogId")
    String findDescriptionByCatalogId(@Param("catalogId") Integer catalogId);

    @Query("SELECT c.longDescription FROM Catalog c WHERE c.id = :catalogId")
    String findLongDescriptionByCatalogId(@Param("catalogId") Integer catalogId);

    @Query("SELECT c.id FROM Catalog c " +
            "JOIN c.parentCatalog p " +
            "WHERE p.alias = 'sd1_movement_type' " +
            "AND c.description = :type_movement")
    String findCatTypeMovementId(@Param("type_movement") String typeMovement);

    @Query("SELECT c.id FROM Catalog c " +
            "JOIN c.parentCatalog p " +
            "WHERE p.alias = 'sd1_container_content' " +
            "AND c.description = :empty_full_process")
    String getEmptyFullProcessId(@Param("empty_full_process") String emptyFullProcess);

    @Query("SELECT c.id FROM Catalog c WHERE c.alias = :alias AND c.status = :status")
    Integer findCatalogIdByAliasAndStatus(@Param("alias") String alias, @Param("status") Boolean status);

    @Query("SELECT c.longDescription " +
            "FROM Catalog c " +
            "WHERE c.id = :catalogId")
    String findDescriptionById(@Param("catalogId") Integer catalogId);

    @Query("SELECT c.id FROM Catalog c WHERE c.alias = :alias")
    Integer findIdByAliasName(@Param("alias") String alias);

    @Query("SELECT new com.maersk.sd1.seg.dto.MenuConfigurationsOutput$DataItems(c.id, c.longDescription) FROM Catalog c WHERE c.parentCatalog.id = 10251 ORDER BY c.id ASC")
    List<MenuConfigurationsOutput.DataItems> findMenuConfigurations();

    @Query("SELECT COUNT(c) > 0 FROM Catalog c WHERE c.description = :descripcion AND c.parentCatalog.id = :catalogoPadreId")
    Boolean existsByDescriptionAndParentCatalogId(@Param("descripcion") String descripcion, @Param("catalogoPadreId") Integer catalogoPadreId);

    @Query("SELECT c FROM Catalog c " +
            "WHERE c.status = true " +
            "  AND c.parentCatalog.id = (SELECT c2.id FROM Catalog c2 WHERE c2.alias = 'sd1_mercplus_component') " +
            "  AND (:component IS NULL OR CONCAT(c.description, ' - ', c.longDescription) LIKE %:component%) " +
            "ORDER BY c.description ASC, c.longDescription ASC")
    List<Catalog> findCatalogsByComponent(@Param("component") String component, Pageable pageable);

    @Query("""
            SELECT c
            FROM Catalog c
            WHERE c.status = true
              AND c.parentCatalog.alias = :alias
              AND c.variable1 = :typeContainer
              AND (:damageLocation IS NULL OR CONCAT(c.description, ' - ', c.longDescription) LIKE %:damageLocation%)
            ORDER BY c.description ASC, c.longDescription ASC
            """)
    List<Catalog> findByFiltersLimitTen(
            @Param("alias") String alias,
            @Param("typeContainer") String typeContainer,
            @Param("damageLocation") String damageLocation,
            Pageable pageable
    );

    @Query("SELECT c FROM Catalog c " +
            "WHERE c.status = true " +
            "  AND c.parentCatalog.alias = 'sd1_mercplus_damage_type' " +
            "  AND (:damageType IS NULL OR CONCAT(c.description, ' - ', c.longDescription) LIKE CONCAT('%', :damageType, '%')) " +
            "ORDER BY c.description ASC, c.longDescription ASC")
    List<Catalog> findDamageTypes(@Param("damageType") String damageType, Pageable pageable);

    @Query("SELECT CASE WHEN COALESCE(c.code, '0') = '1' THEN true ELSE false END " +
            "FROM Catalog c " +
            "WHERE c.id = :containerTypeCodeId AND c.parentCatalog.id = :parentCatalogId")
    Boolean isRefrigeratedLoad(@Param("containerTypeCodeId") Integer containerTypeCodeId, @Param("parentCatalogId") Integer parentCatalogId);

    @Query("SELECT c.code " +
            "FROM Catalog c " +
            "WHERE c.id = :catalogId")
    String findCodeByCatalogId(@Param("catalogId") Integer catalogId);

    @Query("SELECT CASE WHEN c.code IS NULL OR c.code = '0' THEN true ELSE false END FROM Catalog c WHERE c.id = :catalogId AND c.parentCatalog.id = 164")
    boolean isRefrigeratedContainer(@Param("catalogId") Integer catalogId);

    @Query("SELECT new com.maersk.sd1.sde.dto.MercplusComponentListOutput(" +
            "c.id, CONCAT(c.description, ' - ', c.longDescription)) " +
            "FROM Catalog c " +
            "WHERE c.status = true " +
            "  AND c.parentCatalog.id = (SELECT p.id FROM Catalog p WHERE p.alias = 'sd1_mercplus_component') " +
            "ORDER BY c.description ASC, c.longDescription ASC")
    List<MercplusComponentListOutput> findMercplusComponents();

    @Query("SELECT new com.maersk.sd1.sde.dto.MercplusRepairMethodListOutput(" +
            "c.id, CONCAT(c.description, ' - ', c.longDescription)) " +
            "FROM Catalog c " +
            "WHERE c.status = true " +
            "  AND c.parentCatalog.alias = 'sd1_mercplus_repair_method' " +
            "ORDER BY c.description ASC, c.longDescription ASC")
    List<MercplusRepairMethodListOutput> findAllRepairMethods();

    @Query("SELECT new com.maersk.sd1.sde.dto.MercplusDamageLocationListItemDto(" +
            " c.id, CONCAT(c.description, ' - ', c.longDescription)) " +
            "FROM Catalog c " +
            "WHERE c.status = true " +
            "  AND c.parentCatalog.id = (SELECT c2.id FROM Catalog c2 WHERE c2.alias = 'sd1_mercplus_damage_location') " +
            "  AND c.variable1 = :typeContainer " +
            "ORDER BY c.description ASC, c.longDescription ASC")
    List<MercplusDamageLocationListItemDto> findDamageLocationsList(@Param("typeContainer") String typeContainer);

    List<Catalog> findByStatusTrueAndParentCatalogIdOrderByDescriptionAscLongDescriptionAsc(Integer parentCatalogId);

    @Query("SELECT c FROM Catalog c WHERE c.parentCatalog.id = :parentId AND c.description = :description AND c.status = true")
    Optional<Catalog> findByParentAndDescriptionActive(@Param("parentId") Integer parentId,
                                                       @Param("description") String description);

    @Query("SELECT c FROM Catalog c " +
            "LEFT JOIN c.parentCatalog p " +
            "WHERE (:catalogId IS NULL OR c.id = :catalogId OR (p IS NOT NULL AND p.id = :catalogId)) " +
            "AND (:catalogosId IS NULL OR c.id IN :catalogosId OR (p IS NOT NULL AND p.id IN :catalogosId)) " +
            "AND (:aliasList IS NULL OR c.alias IN :aliasList OR (p IS NOT NULL AND p.alias IN :aliasList))")
    List<Catalog> findByFilters(
            @Param("catalogId") Integer catalogId,
            @Param("catalogosId") List<Integer> catalogosId,
            @Param("aliasList") List<String> aliasList);

    @Query("SELECT c FROM Catalog c " +
            "WHERE c.status = TRUE " +
            "  AND c.parentCatalog.id = (SELECT cp.id FROM Catalog cp WHERE cp.alias = 'sd1_mercplus_repair_method') " +
            "  AND (:repairMethod IS NULL OR CAST(FUNCTION('concat', c.description, ' - ', c.longDescription) AS string) LIKE CONCAT('%', :repairMethod, '%')) " +
            "ORDER BY c.description ASC, c.longDescription ASC")
    List<Catalog> findRepairMethods(@Param("repairMethod") String repairMethod, Pageable pageable);

    // Check if alias exists for a different catalog
    boolean existsByAliasAndIdNot(String alias, Integer id);

    // Retrieve catalogs by IDs
    List<Catalog> findByIdIn(List<Integer> ids);

    // For editing child catalogs, we check if these aliases exist for catalogs not in a given set of IDs
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN TRUE ELSE FALSE END FROM Catalog c WHERE c.alias IN :aliases AND c.id NOT IN :catalogIds")
    boolean existsAliasesOutsideCatalogIds(@Param("aliases") List<String> aliases, @Param("catalogIds") List<Integer> catalogIds);

    // A custom projection or fetch minimal fields for the parent
    @Query("SELECT c FROM Catalog c WHERE c.id = :catalogId")
    Catalog findParentCatalog(@Param("catalogId") Integer catalogId);

    @Query(value = "select c from Catalog c " +
            "where c.id = :containerType")
    Optional<Catalog> findByContainerTypeAndParentId(@Param("containerType")Integer containerType);

    @Query(value = "SELECT sds.fn_CatalogoTraducidoDesLarga(:catalogId,:languageId)", nativeQuery = true)
    String findTranslatedLongDesc(@Param("catalogId") Integer catalogId, @Param("languageId") Integer languageId);

    @Query(value = "SELECT sds.fn_CatalogoTraducidoDes(:catalogId,:languageId)", nativeQuery = true)
    String findTranslatedShortDesc(@Param("catalogId") Integer catalogId, @Param("languageId") Integer languageId);

    @Query("SELECT c FROM Catalog c WHERE c.parentCatalog.id = :catalogId")
    List<Catalog> findChildrenByParentId(@Param("catalogId") Integer catalogId);


    @Query("select new com.maersk.sd1.ges.dto.SearchReportOutput(cat.id, cat.description) " +
            "from Catalog cat " +
            "where cat.parentCatalog.id = 30613 " +
            "  and lower(cat.description) like lower(concat('%', :search, '%')) " +
            "order by cat.description asc")
    List<SearchReportOutput> findOplTipoProducto(@Param("search") String search);
           
    @Query(value = "SELECT sds.fn_CatalogoTraducidoDesLarga(:cat_move_type_id,:language_id)", nativeQuery = true)
    String fnCatalogoTraducidoDesLarga(@Param("cat_move_type_id") Integer catMoveTypeId, @Param("language_id") Integer languageId);

    @Query("SELECT new com.maersk.sd1.seg.dto.CatalogOptionDto(c.id, c.longDescription) "
            + "FROM Catalog c "
            + "WHERE c.parentCatalog.id = :parentCatalogId "
            + "ORDER BY c.id ASC")
    List<CatalogOptionDto> findCatalogOptionsByParentCatalogId(@Param("parentCatalogId") Integer parentCatalogId);

    @Query("SELECT new com.maersk.sd1.sdf.dto.CatalogDTO(" +
            "c.id, a1.id, a2.id, a2.variable3, a2.code) " +
            "FROM IsoCode c " +
            "JOIN Catalog a1 ON c.catSize.id = a1.id " +
            "JOIN Catalog a2 ON c.catContainerType.id = a2.id " +
            "WHERE c.id IN :isoCodes")
    List<CatalogDTO> findUpdatedContainerDetails(@Param("isoCodes") List<Integer> isoCodes);

    @Query(value = "SELECT sds.fn_obtener_tipo_reefer_default(:container)", nativeQuery = true)
    Integer getDefaultReeferType(@Param("container") String container);

    Optional<Catalog> findByAliasAndStatusTrue(String alias);

    @Query("select c.description from Catalog c join c.businessUnit b where b.id = :businessUnitId")
    List<String> findCatalogDescriptionsByBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);


    List<Catalog> findByParentCatalog_IdOrderByIdAsc(Integer parentCatalogId);

    @Modifying
    @Query("DELETE FROM Catalog c WHERE c.businessUnit.id = :unidadNegocioId")
    void deleteCatalogByBusinessUnit(@Param("unidadNegocioId") Integer unidadNegocioId);


    @Query("SELECT c.id FROM Catalog c WHERE c.alias = 'sd1_aclost_disabled'")
    Integer findDisabledLogStatus();

    @Query("SELECT c.id FROM Catalog c WHERE c.alias = :alias AND c.status = true")
    Integer findCatalogIdByAlias(@Param("alias") String alias);

    @Query("SELECT c FROM Catalog c WHERE c.alias = :alias AND c.status = :status")
    Catalog findByAliasAndStatus(@Param("alias") String alias, @Param("status") Boolean status);

    @Query("SELECT c FROM Catalog c WHERE c.alias = :alias")
    Optional<Catalog> findByAliasOrNull(@Param("alias") String alias);

    @Query("SELECT c.alias FROM Catalog c WHERE c.id = :id")
    String findAliasByCatalogId(@Param("id") Integer id);

    @Query("""
    SELECT c.id
    FROM Catalog c
    JOIN c.parentCatalog parent
    WHERE parent.alias = 'sd1_movement_type' AND c.description = :catTypeMovementAlias
    """)
    List<Integer> findCatalogIdByDescription(@Param("catTypeMovementAlias") String catTypeMovementAlias);

    @Query("SELECT c FROM Catalog c WHERE c.id IN :catalogIds")
    List<Catalog> findByIdList(List<Integer> catalogIds);

    @Query("SELECT c FROM Catalog c WHERE c.alias = :alias")
    Optional<Catalog> findByAliasOptional(@Param("alias") String alias);
           
    @Query(value = "SELECT ges.fn_CatalogTranslationDescLong(:operation_type_id,:language_id)", nativeQuery = true)
    String findOperationTypeTranslation(@Param("operation_type_id") Long operationTypeId, @Param("language_id") Integer languageId);

    @Query("""
    SELECT c.description
    FROM Catalog c
    WHERE c.id = :id
    """)
    String findShortDescriptionById(@Param("id") Integer id);

    @Query(value = "SELECT c.variable2 FROM Catalog c WHERE c.alias = :alias AND c.status = :status")
    String findVariable2ByAliasAndStatus(@Param("alias") String alias, @Param("status") Boolean status);

    @Query("SELECT c FROM Catalog c WHERE c.id IN :catalogIds")
    List<Catalog> findByIds(@Param("catalogIds") List<Integer> catalogIds);

    @Query("SELECT c FROM Catalog c WHERE c.description = :descripcion AND c.parentCatalog.id = :catalogoPadreId")
    Optional<Catalog> findByDescriptionAndParentCatalogId(@Param("descripcion") String descripcion, @Param("catalogoPadreId") Integer catalogoPadreId);
    
    @Query("SELECT c.id FROM Catalog c WHERE c.id IN :catalogIds AND c.status = true")
    List<Integer> findActiveCatalogByIds(@Param("catalogIds") List<Integer> catalogIds);

    @Query("SELECT c FROM Catalog c WHERE c.id = :catalogId AND c.parentCatalog.id = :parentCatalogId")
    Optional<Catalog> findByIdAndParentCatalogId(Integer catalogId, Integer parentCatalogId);
}