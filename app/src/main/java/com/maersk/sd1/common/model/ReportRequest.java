package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Report;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "reporte_solicitud", schema = "ges")
public class ReportRequest {
    @Id
    @Column(name = "reporte_solicitud_id", nullable = false)
    private Integer id;

    @Size(max = 100)
    @Nationalized
    @Column(name = "estado", length = 100)
    private String status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reporte_id")
    private Report report;

    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro")
    private LocalDateTime registrationDate;

    @ColumnDefault("getdate()")
    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Nationalized
    @Lob
    @Column(name = "parametros")
    private String parameters;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User user;

    @Nationalized
    @Lob
    @Column(name = "url")
    private String url;

    @Nationalized
    @Lob
    @Column(name = "error")
    private String error;

}