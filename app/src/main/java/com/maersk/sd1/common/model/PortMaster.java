package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "PuertosSUNAT", schema = "ges")
public class PortMaster {

    @EmbeddedId
    private PortMasterEntityPropertyPK id;

    @Size(max = 10)
    @Column(name = "CodigoActual", length = 10)
    private String code;

    @Size(max = 250)
    @Column(name = "Descripcion", length = 250)
    private String description;

    @Size(max = 10)
    @Column(name = "Pais", length = 10)
    private String country;

    @Size(max = 250)
    @Column(name = "DescripcionPais", length = 250)
    private String countryDescription;

    @Size(max = 50)
    @Column(name = "Vigencia", length = 50)
    private String validity;

    @Size(max = 50)
    @Column(name = "TipoPuerto", length = 50)
    private String portType;

    @Size(max = 10)
    @Column(name = "CodigoPuerto", length = 10)
    private String portCode;

}

