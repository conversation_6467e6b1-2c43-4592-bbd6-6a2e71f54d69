package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "tl_transportation_event", schema = "sds", indexes = {
        @Index(name = "nci_wi_tl_transportation_event_F512E1E9F7A795490C94850229B3402C", columnList = "event_id, event_type, originator_id, originator_location"),
        @Index(name = "nci_wi_tl_transportation_event_67F1315350DB6E2A66278F1E05C51574", columnList = "active, event_type, originator_location")
})
public class TlTransportationEvent {
    @Id
    @Column(name = "tl_transportation_event_id", nullable = false)
    private Integer id;

    @Size(max = 20)
    @NotNull
    @Column(name = "event_id", nullable = false, length = 20)
    private String eventId;

    @NotNull
    @Column(name = "event_type", nullable = false)
    private Short eventType;

    @Size(max = 10)
    @NotNull
    @Column(name = "originator_location", nullable = false, length = 10)
    private String originatorLocation;

    @Size(max = 200)
    @Column(name = "originator_name", length = 200)
    private String originatorName;

    @Size(max = 20)
    @NotNull
    @Column(name = "originator_id", nullable = false, length = 20)
    private String originatorId;

    @Size(max = 30)
    @NotNull
    @Column(name = "document", nullable = false, length = 30)
    private String document;

    @Size(max = 20)
    @NotNull
    @Column(name = "container", nullable = false, length = 20)
    private String container;

    @Column(name = "event_date")
    private LocalDateTime eventDate;

    @Size(max = 10)
    @Column(name = "location_type", length = 10)
    private String locationType;

    @Size(max = 200)
    @Column(name = "location_value", length = 200)
    private String locationValue;

    @Size(max = 20)
    @Column(name = "transport_phase", length = 20)
    private String transportPhase;

    @Size(max = 10)
    @Column(name = "flag_fulled", length = 10)
    private String flagFulled;

    @Size(max = 20)
    @Column(name = "vehicle_id", length = 20)
    private String vehicleId; //IT LOOKS LIKE A NUMBER PLATE

    @Size(max = 50)
    @Column(name = "transaction_id", length = 50)
    private String transactionId;

    @Size(max = 2000)
    @Column(name = "transaction_message", length = 2000)
    private String transactionMessage;

    @Column(name = "process_date")
    private LocalDateTime processDate;

    @Size(max = 20)
    @Column(name = "terminal", length = 20)
    private String terminal;

    @Size(max = 10)
    @Column(name = "un_locode", length = 10)
    private String locodeBU;

    @NotNull
    @ColumnDefault("'0'")
    @Column(name = "active", nullable = false)
    private Character active;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

}