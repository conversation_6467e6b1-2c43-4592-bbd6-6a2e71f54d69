package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.CompanyRole;
import com.maersk.sd1.ges.dto.CompanySearchOutput;
import com.maersk.sd1.ges.dto.CompanySearchV2Output;
import com.maersk.sd1.ges.dto.SearchReportOutput;
import com.maersk.sd1.sds.dto.GetCompaniesProgrammingShipProcessDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CompanyRoleRepository extends JpaRepository<CompanyRole, Integer> {
    @Query("""
        SELECT new com.maersk.sd1.ges.dto.CompanySearchV2Output(
            c.id,
            c.document,
            CONCAT(c.document, ' - ', c.legalName)
        )
        FROM CompanyRole cr
        JOIN cr.company c
        JOIN cr.catRoleType cat
        WHERE ((:companyIdsIsNotNull = true) OR c.status = true)
          AND ((:companyIdsIsNull = true) OR c.id IN :companyIds)
          AND (((:flagNull = true) AND c.businessUnit IS NULL) OR (c.businessUnit.id = :businessUnitId))
          AND cat.alias IN :companyRoles
          AND ((:companyName IS NULL) OR (CONCAT(c.document, ' ', c.legalName) LIKE CONCAT('%', :companyName, '%')))
        ORDER BY c.document ASC
    """)
    Page<CompanySearchV2Output> searchCompanies(
            @Param("companyIdsIsNotNull") boolean companyIdsIsNotNull,
            @Param("companyIdsIsNull") boolean companyIdsIsNull,
            @Param("companyIds") List<Integer> companyIds,
            @Param("flagNull") Boolean flagNull,
            @Param("businessUnitId") Integer businessUnitId,
            @Param("companyRoles") List<String> companyRoles,
            @Param("companyName") String companyName,
            Pageable pageable
    );

    @Query("SELECT new com.maersk.sd1.ges.dto.CompanySearchOutput(" +
            " c.id, c.document, " +
            " CONCAT(c.document, ' - ', c.legalName, CASE WHEN c.suspended = TRUE THEN ' (Suspended)' ELSE '' END)) " +
            " FROM CompanyRole cr " +
            " JOIN cr.company c " +
            " JOIN cr.catRoleType cat " +
            " WHERE c.status = TRUE " +
            " AND ((:flagNull = TRUE AND c.businessUnit IS NULL) OR c.businessUnit.id = :businessUnitId) " +
            " AND cat.alias IN :companyRoles " +
            " AND (:companyName IS NULL OR CONCAT(c.document, ' ', c.legalName) LIKE CONCAT('%', :companyName, '%')) " +
            " ORDER BY c.document ASC")
    Page<CompanySearchOutput> findCompanies(@Param("businessUnitId") Integer businessUnitId,
                                                              @Param("flagNull") Boolean flagNull,
                                                              @Param("companyRoles") List<String> companyRoles,
                                                              @Param("companyName") String companyName,
                                                              Pageable pageable);

    @Query("SELECT CASE WHEN COUNT(cr) = 0 THEN true ELSE false END " +
            "FROM CompanyRole cr " +
            "WHERE cr.company.id = :companyId AND cr.catRoleType.id = :roleTypeId")
    boolean doesNotExistByCompanyAndRole(@Param("companyId") Integer companyId, @Param("roleTypeId") Integer roleTypeId);

    @Query("SELECT COUNT(er) > 0 FROM CompanyRole er WHERE er.company.id = :companyId AND er.catRoleType.id = :roleTypeId")
    boolean existsByCompanyIdAndRoleTypeId(@Param("companyId") Integer companyId, @Param("roleTypeId") Integer roleTypeId);

    List<CompanyRole> findByCompanyId(Integer empresaId);


    @Query("SELECT DISTINCT cr.company " +
           "FROM CompanyRole cr " +
                   "WHERE cr.catRoleType.id = 43155 " +
                   "  AND cr.company.status = true " +
                   "  AND (cr.company.businessUnit.id = :businessUnitId OR cr.company.businessUnit IS NULL) " +
                   "ORDER BY cr.company.legalName")
    List<Company> findTransportersByBusinessUnit(@Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT new com.maersk.sd1.sds.dto.GetCompaniesProgrammingShipProcessDTO(" +
            "c.id, c.document, c.legalName, c.commercialName, c.address, c.longitude, c.latitude, c.status, c.registrationDate, c.modificationDate, c.suspended, c.phone, c.mail, c.abbreviation, cr.catRoleType.id) " +
            "FROM CompanyRole cr " +
            "JOIN cr.company c " +
            "WHERE cr.catRoleType.id IN (43156, 43158) " +
            "  AND c.businessUnit.id = :businessUnitId")
    List<GetCompaniesProgrammingShipProcessDTO> findCompaniesByBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);

    @Query("select distinct new com.maersk.sd1.ges.dto.SearchReportOutput(c.id, c.legalName) " +
            "from CompanyRole r join r.company c " +
            "where c.businessUnit.id = 1 " +
            "  and lower(c.legalName) like lower(concat('%', :search, '%')) " +
            "order by c.legalName asc")
    List<SearchReportOutput> findEmpresaClientePeru(@Param("search") String search);

    @Query("select distinct new com.maersk.sd1.ges.dto.SearchReportOutput(c.id, c.legalName) " +
            "from CompanyRole r join r.company c " +
            "where c.businessUnit.id = :unitId " +
            "  and lower(c.legalName) like lower(concat('%', :search, '%')) " +
            "order by c.legalName asc")
    List<SearchReportOutput> findEmpresaClienteByUnitId(@Param("unitId") Integer unitId,
                                                        @Param("search") String search);


    @Query("select new com.maersk.sd1.ges.dto.SearchReportOutput(c.id, c.legalName) " +
            "from CompanyRole r join r.company c " +
            "where c.businessUnit.id = :unitId " +
            "  and lower(c.legalName) like lower(concat('%', :search, '%')) " +
            "order by c.legalName asc")
    List<SearchReportOutput> findEmpresaProveedorByUnitId(@Param("unitId") Integer unitId,
                                                          @Param("search") String search);


    @Query("select new com.maersk.sd1.ges.dto.SearchReportOutput(c.id, c.legalName) " +
            "from CompanyRole r join r.company c " +
            "where r.catRoleType.id = 30595 " +
            "  and c.businessUnit.id = :unitId " +
            "  and lower(c.legalName) like lower(concat('%', :search, '%')) " +
            "order by c.legalName asc")
    List<SearchReportOutput> findFauListaCliente(@Param("unitId") Integer unitId,
                                                 @Param("search") String search);

    @Modifying
    @Transactional
    @Query("DELETE FROM CompanyRole cr WHERE cr.company.id = :companyId")
    void deleteAllByCompanyId(@Param("companyId") Integer companyId);

    @Modifying
    @Query("DELETE FROM CompanyRole cr WHERE cr.company.id = :companyId")
    void deleteByCompanyId(@Param("companyId") Integer companyId);


}