package com.maersk.sd1.common.repository;


import com.maersk.sd1.common.model.Vessel;
import com.maersk.sd1.sds.dto.ShipListRowDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

public interface ShipListRepository extends PagingAndSortingRepository<Vessel, Integer> {

    @Query("SELECT new com.maersk.sd1.sds.dto.ShipListRowDTO(" +
            " v.id, " +
            " v.ship, " +
            " v.callSign, " +
            " v.imoNumber, " +
            " v.active, " +
            " v.registrationDate, " +
            " v.modificationDate, " +
            " v.name, " +
            " v.registrationUser.id, " +
            " v.modificationUser.id, " +
            " v.registrationUser.names, " +
            " CONCAT(v.registrationUser.firstLastName, ' ', COALESCE(v.registrationUser.secondLastName, '')), " +
            " v.modificationUser.names, " +
            " CONCAT(v.modificationUser.firstLastName, ' ', COALESCE(v.modificationUser.secondLastName, '')) " +
            ") " +
            "FROM Vessel v " +
            "LEFT JOIN v.registrationUser rUser " +
            "LEFT JOIN v.modificationUser mUser " +
            "WHERE (:naveId IS NULL OR v.id = :naveId) " +
            "AND (:nave IS NULL OR v.ship LIKE CONCAT('%', :nave, '%')) " +
            "AND (:callSign IS NULL OR v.callSign LIKE CONCAT('%', :callSign, '%')) " +
            "AND (:imoNumber IS NULL OR v.imoNumber LIKE CONCAT('%', :imoNumber, '%')) " +
            "AND (:active IS NULL OR v.active = :active) " +
            "AND (:name IS NULL OR v.name LIKE CONCAT('%', :name, '%')) ")
    Page<ShipListRowDTO> findByFilters(@Param("naveId") Integer naveId,
                                       @Param("nave") String nave,
                                       @Param("callSign") String callSign,
                                       @Param("imoNumber") String imoNumber,
                                       @Param("active") Boolean active,
                                       @Param("name") String name,
                                       Pageable pageable);
}

