package com.maersk.sd1.common.integration;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Data
@Builder
public class YardGateInInput {

    @SerializedName("user_id")
    private String userId;

    @SerializedName("operation_type")
    private String operationType;

    @SerializedName("business_unit_id")
    private Integer businessUnitId;

    @SerializedName("containers")
    private ArrayList<YardContainer> containers;

    public String toJSON(){

        Gson gson = new Gson();

        return gson.toJson(this);

    }

    public String toJSON(String reference){

        Map<String, Object> pathField = new HashMap<String, Object>();

        pathField.put("F", this);

        Map<String, Object> pathbase = new HashMap<String, Object>();

        pathbase.put(reference, pathField);

        Gson gson = new Gson();

        return gson.toJson(pathbase);

    }
}
