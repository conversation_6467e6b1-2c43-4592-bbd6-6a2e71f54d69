package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Data
@Table(name = "seteo_edi_coparn", schema = "sds")
public class BookingEdiSetting {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seteo_edi_coparn_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "linea_naviera_id")
    private ShippingLine shippingLine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_canal_recepcion_coparn_id")
    private Catalog catCanalRecepcionCoparn;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_modo_procesar_coparn_id")
    private Catalog catModoProcesarCoparn;

    @Size(max = 250)
    @Column(name = "edi_coparn_descripcion", length = 250)
    private String bkEdiDescription;

    @Size(max = 100)
    @Column(name = "azure_id", length = 100)
    private String azureId;

    @Size(max = 100)
    @Column(name = "sftp_coparn_id", length = 100)
    private String bkEdiSftpId;

    @Size(max = 100)
    @Column(name = "ftp_coparn_id", length = 100)
    private String bkEdiFtpId;

    @Size(max = 200)
    @Column(name = "carpeta_coparn_ruta", length = 200)
    private String bkEdiFolderRoute;

    @Size(max = 10)
    @Column(name = "extension_archivo_descargar", length = 10)
    private String downloadFileExtension;

    @Size(max = 200)
    @Column(name = "ruta_mover_edi", length = 200)
    private String edi_move_route;

    @NotNull
    @Column(name = "permitir_crear_prog_nave_automatico", nullable = false)
    private Boolean allowCreateAutomaticVesselProgramming = false;

    @NotNull
    @Column(name = "permitir_crear_cliente_automatico", nullable = false)
    private Boolean allowCreateAutomaticCustomer = false;

    @NotNull
    @Column(name = "es_historico", nullable = false)
    private Boolean isHistorical = false;

    @Column(name = "fecha_debaja")
    private LocalDateTime deactivationDate;

    @Size(max = 200)
    @Column(name = "motivo_debaja", length = 200)
    private String deactivationReason;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_id")
    private BusinessUnit businessUnit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_bkedi_message_type_id")
    private Catalog catBkEdiMessageType;

    @Size(max = 100)
    @Column(name = "filename_mask", length = 100)
    private String filenameMask;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_owner_edi_booking_id")
    private Catalog catOwnerEdiBooking;

    public BookingEdiSetting(
            Integer id,
            Integer shippingLineId,
            Integer catCanalRecepcionCoparnId,
            Integer catModoProcesarCoparnId,
            String azureId,
            String bkEdiSftpId,
            String bkEdiFtpId,
            String bkEdiFolderRoute,
            String downloadFileExtension,
            String edi_move_route,
            LocalDateTime registrationDate,
            String filenameMask
    ) {
        this.id = id;
        this.shippingLine = new ShippingLine();
        this.shippingLine.setId(shippingLineId);
        this.catCanalRecepcionCoparn = new Catalog();
        this.catCanalRecepcionCoparn.setId(catCanalRecepcionCoparnId);
        this.catModoProcesarCoparn = new Catalog();
        this.catModoProcesarCoparn.setId(catModoProcesarCoparnId);
        this.azureId = azureId;
        this.bkEdiSftpId = bkEdiSftpId;
        this.bkEdiFtpId = bkEdiFtpId;
        this.bkEdiFolderRoute = bkEdiFolderRoute;
        this.downloadFileExtension = downloadFileExtension;
        this.edi_move_route = edi_move_route;
        this.registrationDate = registrationDate;
        this.filenameMask = filenameMask;
    }
}