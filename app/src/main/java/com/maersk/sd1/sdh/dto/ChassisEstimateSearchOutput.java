package com.maersk.sd1.sdh.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ChassisEstimateSearchOutput {

    @JsonProperty("chassis_estimate_id")
    private Integer chassisEstimateId;

    @JsonProperty("chassis_estimate_inspection_date")
    private LocalDateTime chassisEstimateInspectionDate;

    @JsonProperty("cat_chaestim_status_id")
    private Integer catChaestimStatusId;

    @JsonProperty("cat_chaestim_status_name")
    private String catChaestimStatusName;

    @JsonProperty("inspector")
    private String inspector;

    public ChassisEstimateSearchOutput(Integer chassisEstimateId,
                                       LocalDateTime chassisEstimateInspectionDate,
                                       Integer catChaestimStatusId,
                                       String catChaestimStatusName,
                                       String inspector) {
        this.chassisEstimateId = chassisEstimateId;
        this.chassisEstimateInspectionDate = chassisEstimateInspectionDate;
        this.catChaestimStatusId = catChaestimStatusId;
        this.catChaestimStatusName = catChaestimStatusName;
        this.inspector = inspector;
    }

    // Empty constructor for JPA projection compatibility
    public ChassisEstimateSearchOutput() {
    }
}
