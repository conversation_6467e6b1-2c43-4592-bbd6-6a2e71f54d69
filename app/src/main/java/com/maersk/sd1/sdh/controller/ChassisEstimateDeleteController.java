package com.maersk.sd1.sdh.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdh.dto.ChassisEstimateDeleteInput;
import com.maersk.sd1.sdh.dto.ChassisEstimateDeleteOutput;
import com.maersk.sd1.sdh.service.ChassisEstimateDeleteService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDH/module/sdh/SDHChassisEstimateServiceImp")
@RequiredArgsConstructor
public class ChassisEstimateDeleteController {

    private static final Logger logger = LogManager.getLogger(ChassisEstimateDeleteController.class);
    private final ChassisEstimateDeleteService chassisEstimateDeleteService;

    @PostMapping("/sdhemrEstimateChassisDelete")
    public ResponseEntity<ResponseController<ChassisEstimateDeleteOutput>> deleteChassisEstimate(
            @RequestBody @Valid ChassisEstimateDeleteInput.Root request) {
        try {
            logger.info("Request received deleteChassisEstimate: {}", request);

            var input = request.getPrefix().getInput();

            ChassisEstimateDeleteOutput output = chassisEstimateDeleteService.deleteChassisEstimate(
                    input.getChassisEstimateId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception ex) {
            logger.error("An error occurred while deleting the chassis estimate.", ex);
            ChassisEstimateDeleteOutput errorOutput = new ChassisEstimateDeleteOutput();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(ex.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}

