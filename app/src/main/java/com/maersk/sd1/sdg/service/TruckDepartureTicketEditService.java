package com.maersk.sd1.sdg.service;


import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.EirNotification;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.EirNotificationRepository;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureTicketEditInput;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureTicketEditOutput;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Service
public class TruckDepartureTicketEditService {


    private final CatalogRepository catalogRepository;
    private final EirNotificationRepository eirNotificationRepository;
    private final TruckDepartureTicketNotificationService ticketNotificationService;

    public TruckDepartureTicketEditService(CatalogRepository catalogRepository, EirNotificationRepository eirNotificationRepository,
                                           TruckDepartureTicketNotificationService ticketNotificationService) {
        this.catalogRepository = catalogRepository;
        this.eirNotificationRepository = eirNotificationRepository;
        this.ticketNotificationService = ticketNotificationService;
    }


    public SdgTruckDepartureTicketEditOutput editTicket(SdgTruckDepartureTicketEditInput input) {

        if (input == null) {
            return null;
        }
        SdgTruckDepartureTicketEditOutput response = null;

        Catalog catEirNotificationProcess = catalogRepository.findByAlias(Parameter.EIR_NOTIFICATION_PROCESSING);

        Optional<EirNotification> eirNotificationOptional = eirNotificationRepository.findById(input.getEirNotificationId());
        if (eirNotificationOptional.isPresent()) {
            EirNotification eirNotification = eirNotificationOptional.get();
            eirNotification.setAzureStorageUrl(input.getAzureStorageUrl());

            eirNotification.setStatus(catEirNotificationProcess);

            eirNotificationRepository.save(eirNotification);


            ticketNotificationService.processTicketEditNotification(input.getEirNotificationId());
            response = new SdgTruckDepartureTicketEditOutput();

            response.setResultId(1);
            response.setResultMessage("OK - " + eirNotification.getEir().getId());
        }
        return response;
    }
}
