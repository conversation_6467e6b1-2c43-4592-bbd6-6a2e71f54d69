package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.sdg.controller.dto.GateOutGeneralAssignmentChassisRegisterInput;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralAssignmentChassisRegisterOutput;
import com.maersk.sd1.sdg.service.GateOutGeneralAssignmentChassisRegisterService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("/ModuleSDG/module/sdg/SDGGateOutServiceImp")
public class GateOutGeneralAssignmentChassisRegisterController {

    private static final Logger logger = LogManager.getLogger(GateOutGeneralAssignmentChassisRegisterController.class.getName());

    private final GateOutGeneralAssignmentChassisRegisterService service;

    @Autowired
    public GateOutGeneralAssignmentChassisRegisterController(GateOutGeneralAssignmentChassisRegisterService service) {
        this.service = service;
    }

    public ResponseEntity<ResponseController<GateOutGeneralAssignmentChassisRegisterOutput>> gateOutGeneralAssignmentChassisRegisterService(@RequestBody GateOutGeneralAssignmentChassisRegisterInput.Root request) {
        try {
            return service.gateOutGeneralAssignmentChassisRegisterService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}