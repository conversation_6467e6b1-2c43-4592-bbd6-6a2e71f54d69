package com.maersk.sd1.sdg.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


public class GateInGeneralEquipmentAppointmentValidateInput {
    @Data
    public static class Input {

        @JsonProperty("equipment_number")
        private String equipmentNumber;

        @JsonProperty("sub_business_unit_local_id")
        private Integer subBusinessUnitLocalId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}
