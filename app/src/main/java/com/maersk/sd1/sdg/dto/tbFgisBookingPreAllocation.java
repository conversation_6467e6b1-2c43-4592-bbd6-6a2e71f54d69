package com.maersk.sd1.sdg.dto;

import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
public class tbFgisBookingPreAllocation {
    private Date fgisDate;
    private Integer gateInEirId;
    private Integer containerId;

    // Constructor
    public tbFgisBookingPreAllocation(Date fgisDate, Integer gateInEirId, Integer containerId) {
        this.fgisDate = fgisDate;
        this.gateInEirId = gateInEirId;
        this.containerId = containerId;
    }

    // Getters and setters
    public Date getFgisDate() {
        return fgisDate;
    }

    public void setFgisDate(Date fgisDate) {
        this.fgisDate = fgisDate;
    }

    public Integer getGateInEirId() {
        return gateInEirId;
    }

    public void setGateInEirId(Integer gateInEirId) {
        this.gateInEirId = gateInEirId;
    }

    public Integer getContainerId() {
        return containerId;
    }

    public void setContainerId(Integer containerId) {
        this.containerId = containerId;
    }
}
