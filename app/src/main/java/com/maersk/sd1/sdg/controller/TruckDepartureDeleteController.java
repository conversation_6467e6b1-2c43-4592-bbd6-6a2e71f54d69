package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.dto.ResponseEirDeleteBeforeYard;
import com.maersk.sd1.sdg.dto.ResponseTruckDepartureDelete;
import com.maersk.sd1.sdg.dto.TruckDepartureDeleteInput;
import com.maersk.sd1.sdg.service.EirDeleteBeforeYardService;
import com.maersk.sd1.sdg.service.TruckDepartureDeleteService;
import com.maersk.sd1.sds.controller.dto.EirDeleteInput;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/SDGtruckDepartureServiceImp")
public class TruckDepartureDeleteController {

    private final TruckDepartureDeleteService truckDepartureDeleteService;

    private final EirDeleteBeforeYardService eirDeleteBeforeYardService;

    @PostMapping("/sdgtruckDepartureDelete")
    public ResponseEntity<ResponseController<ResponseTruckDepartureDelete>> truckDepartureDelete(@RequestBody TruckDepartureDeleteInput.Root ppo) throws Exception {
        //BEGIN LOGIC CURRENT SERVICE-------------------------------------------------------------------------------

        if(ppo == null || ppo.getPrefix() == null || ppo.getPrefix().getInput() == null){
            return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
        }

        TruckDepartureDeleteInput.Input input = ppo.getPrefix().getInput();

        EirDeleteInput.Input eirDeleteInput = new EirDeleteInput.Input();
        eirDeleteInput.setEirId(input.getEirId());
        eirDeleteInput.setUsuarioModificacionId(input.getUsuarioModificacionId());
        eirDeleteInput.setIdiomaId(input.getIdiomaId());

        ResponseEirDeleteBeforeYard responseEirDeleteBeforeYard = eirDeleteBeforeYardService.eirDeleteBeforeYard(eirDeleteInput);
        ResponseTruckDepartureDelete responseTruckDepartureDelete = new ResponseTruckDepartureDelete();
        boolean flagExecuteDeparture = false;
        boolean flagRequirePlanApprove = false;

        if (responseEirDeleteBeforeYard.getResponseResult() == 1) {
            if (responseEirDeleteBeforeYard.getResponseFlagRequirePlanApprove() == 1) {
                flagRequirePlanApprove = true;
            }
            flagExecuteDeparture = true;
        } else {
            String myResponse = responseEirDeleteBeforeYard.getResponseMessage();
            responseTruckDepartureDelete.setResponseMessage(myResponse);
        }

        if (flagExecuteDeparture) {
            responseTruckDepartureDelete = truckDepartureDeleteService.truckDepartureDelete(input);
        }
        return ResponseEntity.ok(new ResponseController<>(responseTruckDepartureDelete));
    }
}
