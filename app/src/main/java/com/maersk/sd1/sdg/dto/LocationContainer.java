package com.maersk.sd1.sdg.dto;

import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.Cell;
import com.maersk.sd1.common.model.Level;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LocationContainer {
    private Block blockId;
    private Cell cellId;
    private Level levelId;
    private Integer locationContainerId;
}
