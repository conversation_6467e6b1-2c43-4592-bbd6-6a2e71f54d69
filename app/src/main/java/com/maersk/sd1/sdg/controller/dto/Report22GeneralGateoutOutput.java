package com.maersk.sd1.sdg.controller.dto;

import lombok.*;
import lombok.extern.jackson.Jacksonized;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Jacksonized
public class Report22GeneralGateoutOutput {

    @JsonProperty("totalCount")
    private Integer totalCount;

    @JsonProperty("data")
    private List<SingleData> dataList;

    @Data
    @Builder
    public static class SingleData {

        @JsonProperty("MoveType")
        private String moveType;

        @JsonProperty("Depot")
        private String depot;

        @JsonProperty("ContainerEirNumber")
        private Integer containerEirNumber;

        @JsonProperty("EquipmentNumber")
        private String equipmentNumber;

        @JsonProperty("EquipmentCategory")
        private String equipmentCategory;

        @JsonProperty("EquipmentSizeType")
        private String equipmentSizeType;

        @JsonProperty("IsoCode")
        private String isoCode;

        @JsonProperty("EquipmentGrade")
        private String equipmentGrade;

        @JsonProperty("AccelerateCode")
        private String accelerateCode;

        @JsonProperty("ShippingLineOrChassisOwner")
        private String shippingLineOrChassisOwner;

        @JsonProperty("ShipperName")
        private String shipperName;

        @JsonProperty("ConsigneeName")
        private String consigneeName;

        @JsonProperty("GateInDepot")
        private String gateInDepot;

        @JsonProperty("GateOutTruckArrival")
        private String gateOutTruckArrival;

        @JsonProperty("GateOutTruckDeparture")
        private String gateOutTruckDeparture;

        @JsonProperty("OperationType")
        private String operationType;

        @JsonProperty("DocumentType")
        private String documentType;

        @JsonProperty("DocumentNumber")
        private String documentNumber;

        @JsonProperty("Commodity")
        private String commodity;

        @JsonProperty("DwellTime")
        private Integer dwellTime;

        @JsonProperty("StructureConditionDelivered")
        private String structureConditionDelivered;

        @JsonProperty("MachineryConditionDelivered")
        private String machineryConditionDelivered;

        @JsonProperty("TruckingCompany")
        private String truckingCompany;

        @JsonProperty("Scac")
        private String scac;

        @JsonProperty("TruckDriver")
        private String truckDriver;

        @JsonProperty("TruckIdentifier")
        private String truckIdentifier;

        @JsonProperty("AttachedChassisNumber")
        private String attachedChassisNumber;

        @JsonProperty("ChassisEirNumber")
        private String chassisEirNumber;

        @JsonProperty("ChassisPickup")
        private String chassisPickup;

        @JsonProperty("ImoInformation")
        private String imoInformation;

        @JsonProperty("EquipmentRestriction")
        private String equipmentRestriction;

        @JsonProperty("Seals")
        private String seals;

        @JsonProperty("Remark")
        private String remark;

        @JsonProperty("RegisterUser")
        private String registerUser;

        @JsonProperty("RegisterDate")
        private String registerDate;

        @JsonProperty("TwrNumber")
        private String twrNumber;
     }
}
