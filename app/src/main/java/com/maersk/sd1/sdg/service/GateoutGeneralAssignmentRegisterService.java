
package com.maersk.sd1.sdg.service;

import com.google.gson.Gson;
import com.maersk.sd1.common.dto.LinesWithSubLinesDTO;
import com.maersk.sd1.common.dto.SystemRuleDTO;
import com.maersk.sd1.common.exception.SD1ControlledException;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.controller.dto.*;
import com.maersk.sd1.sdg.dto.GateOutGeneralAssignmentRegisterBKDetDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.maersk.sd1.common.Parameter.*;

@Service
public class GateoutGeneralAssignmentRegisterService {

    private static final Logger logger = LogManager.getLogger(GateoutGeneralAssignmentRegisterService.class);

    /*THIS LIST SHOULD HOLD ALL THE CATALOG ALIASES USED FOR FETCHING RECORDS FROM THE ges.catalog TABLE IN THE SP BELOW*/
    private List<String> catalogAliases = List.of(
            CATALOG_TYPE_CONTAINER_DRY_ALIAS,
            CATALOG_TYPE_CONTAINER_HC_ALIAS,
            CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS,
            CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
            CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS,
            IS_DOCUMENT_TYPE_BK,
            IS_DOCUMENT_TYPE_BL,
            CATALOG_DOCUMENT_CREATION_ORIGIN_GATE_OUT_AUTOMATIC_ALIAS,
            CATALOG_MEASURE_WEIGHT_KG_ALIAS,
            CATALOG_TRK_PLAN_IN_PROCESS,
            CATALOG_CHASSIS_IN_PROGRESS,
            CATALOG_CHASSIS_IN_PENDING_ALIAS,
            CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS,
            IS_TELEX_FORMAT,
            GRADE_NO_CLASS,
            CATALOG_CONTAINER_TYPE
    );

    private final GESCatalogService catalogService;
    private final ContainerRepository containerRepository;
    private final EirRepository eirRepository;
    private final MessageLanguageService messagelanguageService;
    private final GateoutGeneralAssignmentContainerFindService gateoutGeneralAssignmentContainerFindService;
    private final StockEmptyRepository stockEmptyRepository;
    private final ContainerPreassignmentRepository containerPreassignmentRepository;
    private final BookingRepository bookingRepository;
    private final BookingDetailRepository bookingDetailRepository;
    private final SystemRuleRepository systemRuleRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final StockFullRepository stockFullRepository;
    private final EirChassisRepository eirChassisRepository;
    private final ChassisRepository chassisRepository;
    private final ChassisDocumentRepository chassisDocumentRepository;
    private final ChassisBookingDocumentRepository chassisBookingDocumentRepository;
    private final StockChassisRepository stockChassisRepository;
    private final CatalogRepository catalogRepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final TransportPlanningDetailRepository transportPlanningDetailRepository;
    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final InspectionGateRepository inspectionGateRepository;
    private final AttachmentRepository attachmentRepository;
    private final InspectionGatePhotoRepository inspectionGatePhotoRepository;
    private final ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    private final GateTransmissionSettingRepository gateTransmissionSettingRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;
    private final GateTransmissionRepository gateTransmissionRepository;
    private final LogRepository logRepository;

    @Autowired
    public GateoutGeneralAssignmentRegisterService(GESCatalogService catalogService,
                                                   ContainerRepository containerRepository,
                                                   EirRepository eirRepository, MessageLanguageService messagelanguageService, GateoutGeneralAssignmentContainerFindService gateoutGeneralAssignmentContainerFindService,
                                                   StockEmptyRepository stockEmptyRepository,
                                                   ContainerPreassignmentRepository containerPreassignmentRepository,
                                                   BookingRepository bookingRepository,
                                                   BookingDetailRepository bookingDetailRepository,
                                                   SystemRuleRepository systemRuleRepository,
                                                   CargoDocumentDetailRepository cargoDocumentDetailRepository,
                                                   StockFullRepository stockFullRepository,
                                                   EirChassisRepository eirChassisRepository,
                                                   ChassisRepository chassisRepository,
                                                   ChassisDocumentRepository chassisDocumentRepository,
                                                   ChassisBookingDocumentRepository chassisBookingDocumentRepository,
                                                   StockChassisRepository stockChassisRepository,
                                                   CatalogRepository catalogRepository,
                                                   VesselProgrammingContainerRepository vesselProgrammingContainerRepository,
                                                   TransportPlanningDetailRepository transportPlanningDetailRepository,
                                                   EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository,
                                                   InspectionGateRepository inspectionGateRepository,
                                                   AttachmentRepository attachmentRepository,
                                                   InspectionGatePhotoRepository inspectionGatePhotoRepository,
                                                   ChassisDocumentDetailRepository chassisDocumentDetailRepository,
                                                   GateTransmissionSettingRepository gateTransmissionSettingRepository,
                                                   BusinessUnitRepository businessUnitRepository,
                                                   GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository,
                                                   GateTransmissionRepository gateTransmissionRepository,
                                                   LogRepository logRepository) {
        this.catalogService = catalogService;
        this.containerRepository = containerRepository;
        this.eirRepository = eirRepository;
        this.messagelanguageService = messagelanguageService;
        this.gateoutGeneralAssignmentContainerFindService = gateoutGeneralAssignmentContainerFindService;
        this.stockEmptyRepository = stockEmptyRepository;
        this.containerPreassignmentRepository = containerPreassignmentRepository;
        this.bookingRepository = bookingRepository;
        this.bookingDetailRepository = bookingDetailRepository;
        this.systemRuleRepository = systemRuleRepository;
        this.cargoDocumentDetailRepository = cargoDocumentDetailRepository;
        this.stockFullRepository = stockFullRepository;
        this.eirChassisRepository = eirChassisRepository;
        this.chassisRepository = chassisRepository;
        this.chassisDocumentRepository = chassisDocumentRepository;
        this.chassisBookingDocumentRepository = chassisBookingDocumentRepository;
        this.stockChassisRepository = stockChassisRepository;
        this.catalogRepository = catalogRepository;
        this.vesselProgrammingContainerRepository = vesselProgrammingContainerRepository;
        this.transportPlanningDetailRepository = transportPlanningDetailRepository;
        this.eirDocumentCargoDetailRepository = eirDocumentCargoDetailRepository;
        this.inspectionGateRepository = inspectionGateRepository;
        this.attachmentRepository = attachmentRepository;
        this.inspectionGatePhotoRepository = inspectionGatePhotoRepository;
        this.chassisDocumentDetailRepository = chassisDocumentDetailRepository;
        this.gateTransmissionSettingRepository = gateTransmissionSettingRepository;
        this.businessUnitRepository = businessUnitRepository;
        this.gateTransmissionLocalSettingRepository = gateTransmissionLocalSettingRepository;
        this.gateTransmissionRepository = gateTransmissionRepository;
        this.logRepository = logRepository;
    }

    @Transactional
    public GateOutGeneralAssignmentRegisterOutput execute(GateOutGeneralAssignmentRegisterInput.Input input) throws Exception {
        GateOutGeneralAssignmentRegisterOutput output = this.assignGateOut(input);
        return output;
    }

    public GateOutGeneralAssignmentRegisterOutput executeNonTransactional(GateOutGeneralAssignmentRegisterInput.Input input) throws Exception {
        GateOutGeneralAssignmentRegisterOutput output = this.assignGateOut(input);
        return output;
    }

    private GateOutGeneralAssignmentRegisterOutput assignGateOut(GateOutGeneralAssignmentRegisterInput.Input input) throws Exception {

        GateOutGeneralAssignmentRegisterOutput output = GateOutGeneralAssignmentRegisterOutput.builder().build();

        Integer tare = null;
        Integer catEquipMeasureTareId = null;
        Integer payload = null;
        Integer catEquipMeasurePayloadId = null;
        LocalDateTime manufacture = null;
        Integer catMarcaMotorId = null;
        Integer gradeId = null;
        Integer reeferTypeId = null;
        Integer shippingLineBkId = null;
        Integer cntSizeISO = null;
        Integer cntTypeISO = null;
        Integer shippingLineBL = null;
        Integer containerSizeId = null;
        String containerNumber = null;
        String isReefer = null;
        Integer codigoISOId = null;
        Integer imoId = null;
        BigDecimal netWeight = BigDecimal.ZERO;
        Integer catReceivedWeightMeasureId = null;
        Boolean dangerousCargo = false;
        Boolean refrigeratedCargo = false;
        Boolean inStock = false;
        Integer flagAssignment = null;
        Integer bookingId = null;
        Integer eirChassisId = null;
        Integer documentoCargaGofId = null;
        Integer subBusinessUnitId = null;
        Integer documentChassisGoId = null;
        Integer bookingDetalleId = null;
        String documentoCargaReferencia = null;
        Integer catCargoDocumentTypeId = null;
        Integer containerEirId = null;
        Integer preasignacionContenedorId = null;
        Integer programacionNaveDetalleId = null;
        Integer inspeccionGateId = null;
        Integer documentChassisDetailId = null;
        Integer catChassisTypeId = null;
        Integer bookingChassisId = null;
        String chassisNumber = null;
        String documentChassisNumber = null;
        Boolean boxWithDamage = false;
        Boolean boxDamaged = false;
        Boolean machineryWithDamage = false;
        Boolean machineryDamaged = false;
        Integer reviewControl = null;
        LocalDateTime reviewDate = null;
        Integer allocationControl = null;
        LocalDateTime goutInspeccionDate = null;
        Integer allocationControlLight = null;
        LocalDateTime allocationDateLight = null;
        String chassisAllocationControl = null;
        Integer documentoCargaDetalleId = null;
        Integer documentoCargaDetalleIdTemp = null;
        Integer containerDummyId = null;
        Integer containerNotApplica = null;
        String bkRemarkRule = null;

        Integer shippingLineAux = null;
        Integer eirIdGateIn = null;
        final Integer catEmptyFullId;
        Integer catProcedenciaId = null;
        Integer containerTypeId = null;

        Pageable pageable = PageRequest.of(0, 1);
        List<GateOutGeneralAssignmentRegisterBKDetDTO> bkDets = new ArrayList<>();
        Gson gson = new Gson();

        HashMap<String, Integer> catalogsIds = catalogService.findIdsByAliases(this.catalogAliases);
        List<Container> dummyContainers = containerRepository.findByContainerNumbers(List.of("NO-CNT", "NOT APPLICA"));
        containerDummyId = dummyContainers.stream().filter(cnt -> cnt.getContainerNumber().equals("NO-CNT")).map(Container::getId).findFirst().orElse(null);
        containerNotApplica = dummyContainers.stream().filter(cnt -> cnt.getContainerNumber().equals("NOT APPLICA")).map(Container::getId).findFirst().orElse(null);
        List<Integer> containerCategoryIdList = List.of(catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS), catalogsIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS));

        if (dummyContainers.stream().anyMatch(cnt -> cnt.getId().equals(input.getContainerId()))) {
            input.setContainerId(null);
            input.setSeal1(input.getSeal1().trim());
            input.setSeal2(input.getSeal2().trim());
            input.setSeal3(input.getSeal3().trim());
            input.setSeal4(input.getSeal4().trim());
        }

        Optional<Eir> eir = eirRepository.findById(input.getEirId());
        Optional<Booking> booking = eir.map(Eir::getBookingGout);

        bookingId = eir.map(Eir::getBookingGout).map(Booking::getId).orElse(null);
        // for empty
        subBusinessUnitId = eir.map(Eir::getSubBusinessUnit).map(BusinessUnit::getId).orElse(null);
        catProcedenciaId = eir.map(Eir::getCatOrigin).map(Catalog::getId).orElse(null);
        catEmptyFullId = eir.map(Eir::getCatEmptyFull).map(Catalog::getId).orElse(null);
        eirChassisId = eir.map(Eir::getEirChassis).map(EirChassis::getId).orElse(null);
        // for chassis
        documentoCargaGofId = eir.map(Eir::getDocumentCargoGof).map(CargoDocument::getId).orElse(null);
        containerEirId = eir.map(Eir::getContainer).map(Container::getId).orElse(null);

        //BRANCH 1: Invalid input or missing data
        if (subBusinessUnitId == null || !eir.isPresent()
                || (containerDummyId.equals(containerEirId) && input.getContainerId() == null && input.getChassisId() == null)
                || (catalogsIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId) && containerDummyId.equals(containerEirId) && input.getDocumentCargaDetalleId() == null)) {
            return GateOutGeneralAssignmentRegisterOutput.builder()
                    .resultState(2)
                    .resultMessage(messagelanguageService.getMessage("GENERAL", 21, input.getLanguageId()))
                    .build();
        }

        //BRANCH 2: Assigning container related values
        if (containerCategoryIdList.stream().anyMatch(cat -> cat.equals(catEmptyFullId))
                && (bookingId != null || documentoCargaGofId != null) && containerDummyId.equals(containerEirId)) {
            Optional<Container> container = containerRepository.findById(input.getContainerId());
            containerNumber = container.map(Container::getContainerNumber).orElse(null);
            containerSizeId = container.map(Container::getCatSize).map(Catalog::getId).orElse(null);
            containerTypeId = container.map(Container::getCatContainerType).map(Catalog::getId).orElse(null);
            tare = container.map(Container::getContainerTare).orElse(null);
            catEquipMeasureTareId = container.map(Container::getCatEquipMeasureTare).map(Catalog::getId).orElse(catalogsIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
            payload = container.map(Container::getMaximunPayload).orElse(null);
            catEquipMeasurePayloadId = container.map(Container::getCatEquipMeasurePayload).map(Catalog::getId).orElse(catalogsIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
            codigoISOId = container.map(Container::getIsoCode).map(IsoCode::getId).orElse(null);
            gradeId = container.map(Container::getCatGrade).map(Catalog::getId).orElse(null);
            reeferTypeId = container.map(Container::getCatReeferType).map(Catalog::getId).orElse(null);
            catMarcaMotorId = container.map(Container::getCatEngineBrand).map(Catalog::getId).orElse(null);
            manufacture = container.map(Container::getManufactureDate).orElse(null);

            //BRANCH 2.1: Assigning default class for the container
            if (gradeId == null) gradeId = catalogsIds.get(GRADE_NO_CLASS);
            SdggateoutGeneralAssignmentContainerFindInput.Root findRoot = new SdggateoutGeneralAssignmentContainerFindInput.Root();
            SdggateoutGeneralAssignmentContainerFindInput.Prefix prefix = new SdggateoutGeneralAssignmentContainerFindInput.Prefix();
            SdggateoutGeneralAssignmentContainerFindInput.Input findInput = SdggateoutGeneralAssignmentContainerFindInput.Input.builder()
                    .eirId(input.getEirId())
                    .containerNumber(containerNumber)
                    .listContainerDate(false)
                    .languageId(input.getLanguageId())
                    .confirmContainer(1)
                    .build();

            prefix.setInput(findInput);
            findRoot.setSdg(prefix);
            SdggateoutGeneralAssignmentContainerFindOutput containerFindValidation = gateoutGeneralAssignmentContainerFindService.execute(findRoot);

            //BRANCH 2.2: Container not in stock
            if (!Integer.valueOf(1).equals(containerFindValidation.getResult().getFirst().getResultState())) {
                return GateOutGeneralAssignmentRegisterOutput.builder()
                        .resultState(2)
                        .resultMessage(containerFindValidation.getResult().getFirst().getResultMessage())
                        .build();
            }
        }

        //BRANCH 3: Empty container operation
        if (catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId)
                && bookingId != null && containerDummyId.equals(containerEirId)) {
            Optional<StockEmpty> activeGateInEmptyStock = stockEmptyRepository.findByContainerAndBusinessUnitAndInStockAndActive(input.getContainerId(), subBusinessUnitId, true, true);
            boxWithDamage = activeGateInEmptyStock.map(StockEmpty::getGateInEir).map(Eir::getStructureWithDamage).orElse(null);
            machineryWithDamage = activeGateInEmptyStock.map(StockEmpty::getGateInEir).map(Eir::getMachineryWithDamage).orElse(null);
            eirIdGateIn = activeGateInEmptyStock.map(StockEmpty::getGateInEir).map(Eir::getId).orElse(null);

            //VALIDATE PREASSIGNATION (17/204/2024) AND MISSING DISPATCH
            //(1° option | short)
            Optional<ContainerPreassignment> containerPreassignment = containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionOne(pageable, bookingId, input.getContainerId(), catalogsIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).stream().findFirst();

            //BRANCH 3.1: Container preassignment not found with the first method
            if (containerPreassignment.isEmpty()) {
                //(2° option | long)
                containerPreassignment = containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionTwo(pageable, bookingId, input.getContainerId(), catalogsIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).stream().findFirst();
            }
            preasignacionContenedorId = containerPreassignment.map(ContainerPreassignment::getId).orElse(null);
            shippingLineBkId = containerPreassignment.map(ContainerPreassignment::getBookingDetail).map(BookingDetail::getBooking).map(Booking::getShippingLine).map(ShippingLine::getId).orElse(null);
            programacionNaveDetalleId = containerPreassignment.map(ContainerPreassignment::getBookingDetail).map(BookingDetail::getBooking).map(Booking::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(null);
            imoId = containerPreassignment.map(ContainerPreassignment::getBookingDetail).map(BookingDetail::getBooking).map(Booking::getImo).map(Imo::getId).orElse(null);
            bookingDetalleId = containerPreassignment.map(ContainerPreassignment::getBookingDetail).map(BookingDetail::getId).orElse(null);

            //BRANCH 3.2: Container preassignment not found with the second method
            if (preasignacionContenedorId == null) {
                bkRemarkRule = eir.map(Eir::getBookingGout).map(Booking::getRemarkRulesName).orElse("");
                Integer finalContainerTypeId = containerTypeId;
                List<BookingDetail> bookingDetails = new ArrayList<>();

                //BRANCH 3.2.1: Booking remark rule blank or container not HC or DC
                if (bkRemarkRule.isBlank()
                        || ("FLAG_TO_FLEX".equals(bkRemarkRule)
                        && List.of(catalogsIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS), catalogsIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).stream().noneMatch(cat -> cat.equals(finalContainerTypeId)))) {
                    bookingDetails = bookingDetailRepository.findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListWithNoRemarkRule(bookingId, containerSizeId, List.of(containerTypeId));
                    bkDets.addAll(bookingDetails.stream()
                            .map(bd -> GateOutGeneralAssignmentRegisterBKDetDTO.builder()
                                    .booking_detalle_id(bd.getId())
                                    .linea_naviera_id(booking.map(Booking::getShippingLine).map(ShippingLine::getId).orElse(null))
                                    .assigned(0)
                                    .required(bd.getReservationQuantity())
                                    .pending(0)
                                    .empresa_cliente_id(booking.map(Booking::getClientCompany).map(Company::getId).orElse(null))
                                    .programacion_nave_detalle_id(booking.map(Booking::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(null))
                                    .imo_id(booking.map(Booking::getImo).map(Imo::getId).orElse(null))
                                    .build()).toList());
                }

                //BRANCH 3.2.2: Booking remark rule FLAG_TO_FLEX and container is HC or DC
                if ("FLAG_TO_FLEX".equals(bkRemarkRule)
                        && List.of(catalogsIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS), catalogsIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).stream().anyMatch(cat -> cat.equals(finalContainerTypeId))) {
                    bookingDetails = bookingDetailRepository.findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListAndRemarkRule(bookingId, containerSizeId, List.of(catalogsIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS), catalogsIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)), "FLAG_TO_FLEX");
                    bkDets.addAll(bookingDetails.stream()
                            .map(bd -> GateOutGeneralAssignmentRegisterBKDetDTO.builder()
                                    .booking_detalle_id(bd.getId())
                                    .linea_naviera_id(booking.map(Booking::getShippingLine).map(ShippingLine::getId).orElse(null))
                                    .assigned(0)
                                    .required(bd.getReservationQuantity())
                                    .pending(0)
                                    .empresa_cliente_id(booking.map(Booking::getClientCompany).map(Company::getId).orElse(null))
                                    .programacion_nave_detalle_id(booking.map(Booking::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(null))
                                    .imo_id(booking.map(Booking::getImo).map(Imo::getId).orElse(null))
                                    .build()).toList());
                }

                List<ContainerPreassignment> containerPreassignments = containerPreassignmentRepository.findAllByBookingDetailIdAndActive(bookingDetails.stream().map(BookingDetail::getId).toList(), true);
                List<BookingDetail> finalBookingDetails = bookingDetails;
                bkDets.forEach(bk -> {
                    bk.setAssigned(containerPreassignments.stream()
                            .filter(cp -> finalBookingDetails.stream()
                                    .filter(bd -> bd.getId().equals(cp.getBookingDetail().getId()))
                                    .map(BookingDetail::getId)
                                    .findFirst().orElse(0).equals(bk.getBooking_detalle_id())).toList().size());
                    bk.setPending(bk.getRequired() - bk.getAssigned());
                });
                bkDets = bkDets.stream().filter(bk -> bk.getPending() > 0).toList();

                Optional<GateOutGeneralAssignmentRegisterBKDetDTO> lastDetail = bkDets.reversed().stream().findFirst();
                shippingLineBkId = lastDetail.map(GateOutGeneralAssignmentRegisterBKDetDTO::getLinea_naviera_id).orElse(null);
                programacionNaveDetalleId = lastDetail.map(GateOutGeneralAssignmentRegisterBKDetDTO::getProgramacion_nave_detalle_id).orElse(null);
                imoId = lastDetail.map(GateOutGeneralAssignmentRegisterBKDetDTO::getImo_id).orElse(null);
                bookingDetalleId = lastDetail.map(GateOutGeneralAssignmentRegisterBKDetDTO::getBooking_detalle_id).orElse(null);
            }

            dangerousCargo = Optional.ofNullable(imoId).isPresent();

            String linesWithSubLinesSR = systemRuleRepository.findRuleByIdAndActiveTrue("sds_subshippingline_equivalence");
            List<LinesWithSubLinesDTO> subLines = Arrays.stream(gson.fromJson(linesWithSubLinesSR, LinesWithSubLinesDTO[].class)).toList();
            Integer finalShippingLineBkId = shippingLineBkId;
            shippingLineAux = subLines.stream()
                    .filter(sl -> sl.getSubLineId().equals(finalShippingLineBkId))
                    .findFirst().map(LinesWithSubLinesDTO::getLineMainId).orElse(null);

            //BRANCH 3.3: Container not in stock
            if (!Boolean.TRUE.equals(stockEmptyRepository.existsByContainerIdAndSubBusinessUnitIdAndInStockAndActive(input.getContainerId(), subBusinessUnitId, true, true))) {
                return GateOutGeneralAssignmentRegisterOutput.builder()
                        .resultState(2)
                        .resultMessage(messagelanguageService.getMessage("PRC_GATE_OUT_EMPTY_LIGHT", 26, input.getLanguageId())) //CHECK IF THE CONTAINER IS IN STOCK OR ALREADY HAS A GATEOUT EIR
                        .build();
            }
        }

        //BRANCH 4: Full container operation
        if (catalogsIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId)
                && documentoCargaGofId != null && containerDummyId.equals(containerEirId)) {
            Optional<CargoDocumentDetail> fullCargoDocumentDetail = cargoDocumentDetailRepository.findByIdAndWithActiveCargoDocumentAndVesselProgramming(documentoCargaDetalleId);
            netWeight = fullCargoDocumentDetail.map(CargoDocumentDetail::getReceivedWeight).orElse(null);
            catReceivedWeightMeasureId = fullCargoDocumentDetail.map(CargoDocumentDetail::getCatReceivedWeightMeasure).map(Catalog::getId).orElse(catalogsIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
            catCargoDocumentTypeId = fullCargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCatCargoDocumentType).map(Catalog::getId).orElse(catalogsIds.get(IS_DOCUMENT_TYPE_BL));
            programacionNaveDetalleId = fullCargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(null);

            if (!Boolean.TRUE.equals(stockFullRepository.existsByContainerIdAndSubBusinessUnitIdAndInStockAndActive(input.getContainerId(), subBusinessUnitId, true, true))) {
                return GateOutGeneralAssignmentRegisterOutput.builder()
                        .resultState(2)
                        .resultMessage(messagelanguageService.getMessage("PRC_GATE_OUT_FULL_LIGHT", 26, input.getLanguageId())) //CHECK IF THE CONTAINER IS IN STOCK OR ALREADY HAS A GATEOUT EIR
                        .build();
            }

        }

        //BRANCH 5: Chassis operation
        if (eirChassisId != null && input.getChassisId() != null) {
            Optional<EirChassis> eirChassis = eirChassisRepository.findById(eirChassisId);
            documentChassisGoId = eirChassis.map(EirChassis::getChassisDocumentGo).map(ChassisDocument::getId).orElse(null);
            documentChassisNumber = eirChassis.map(EirChassis::getChassisDocumentGo).map(ChassisDocument::getDocumentChassisNumber).orElse(null);
            chassisAllocationControl = eirChassis.map(EirChassis::getChassisAllocationControl).orElse("0");

            //BRANCH 5.1: Chassis without allocation control
            if ("0".equals(chassisAllocationControl)) {
                Optional<Chassis> chassis = chassisRepository.findById(input.getChassisId());
                catChassisTypeId = chassis.map(Chassis::getCatChassisType).map(Catalog::getId).orElse(null);
                chassisNumber = chassis.map(Chassis::getChassisNumber).orElse(null);

                Optional<ChassisBookingDocument> chassisBookingDocument = chassisBookingDocumentRepository.findFirstByIdAndChassisTypeIdAndCatStatusIdAndActiveWithNoChassis(pageable, documentChassisGoId, catChassisTypeId, catalogsIds.get(CATALOG_CHASSIS_IN_PENDING_ALIAS), true).stream().findFirst();
                documentChassisDetailId = chassisBookingDocument.map(ChassisBookingDocument::getChassisDocument).map(ChassisDocument::getId).orElse(null);
                bookingChassisId = chassisBookingDocument.map(ChassisBookingDocument::getId).orElse(null);

                //BRANCH 5.1.1: Chassis booking document not found
                if (documentChassisDetailId == null || bookingChassisId == null) {
                    return GateOutGeneralAssignmentRegisterOutput.builder()
                            .resultState(2)
                            .resultMessage(messagelanguageService.getMessage("PRC_GO_GENERAL", 16, input.getLanguageId())) //THERE'S NO CHASSIS BOOKING DOCUMENT AVAILABLE
                            .build();
                    //BRANCH 5.1.2: Chassis not in stock
                } else if (!Boolean.TRUE.equals(stockChassisRepository.existsByChassisIdAndInStockAndSubBusinessUnitIdAndActive(input.getChassisId(), true, subBusinessUnitId, true))) {
                    return GateOutGeneralAssignmentRegisterOutput.builder()
                            .resultState(2)
                            .resultMessage(messagelanguageService.getMessage("PRC_GO_GENERAL", 15, input.getLanguageId())) //CHASSIS NOT IN STOCK
                            .build();
                }
            }
        }

        //BRANCH 6: Transactional operations and container to assign
        if (containerCategoryIdList.stream().anyMatch(cat -> cat.equals(catEmptyFullId))
                && (bookingId != null || documentoCargaGofId != null)
                && containerDummyId.equals(containerEirId)) { //TO ASSIGN

            //BRANCH 6.1: Empty container
            if (catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId)) {
                documentoCargaDetalleId = null;
                documentoCargaDetalleIdTemp = null;
                refrigeratedCargo = false;

                //BRANCH 6.1.1: Container preassignment found
                if (preasignacionContenedorId != null) { //GET DATA
                    Optional<ContainerPreassignment> containerPreassignment = containerPreassignmentRepository.findById(preasignacionContenedorId);
                    documentoCargaDetalleId = containerPreassignment.map(ContainerPreassignment::getCargoDocumentDetail).map(CargoDocumentDetail::getId).orElse(null);
                    documentoCargaReferencia = containerPreassignment.map(ContainerPreassignment::getCargoDocumentDetail).map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCargoDocument).orElse(null);
                    catCargoDocumentTypeId = catalogsIds.get(IS_DOCUMENT_TYPE_BK);
                    documentoCargaDetalleIdTemp = documentoCargaDetalleId;
                } else { //BRANCH 6.1.2: Container preassignment not found - Preassign
                    Optional<CargoDocumentDetail> cargoDocumentDetail = cargoDocumentDetailRepository.findFirstByBookingDetailIdAndActiveWithNoContainer(pageable, bookingDetalleId, true).stream().findFirst();
                    documentoCargaDetalleId = cargoDocumentDetail.map(CargoDocumentDetail::getId).orElse(null);
                    documentoCargaReferencia = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCargoDocument).orElse(null);
                    catCargoDocumentTypeId = catalogsIds.get(IS_DOCUMENT_TYPE_BK);

                    //BRANCH 6.1.2.1: Container preassignment not found - No booking available
                    if (documentoCargaDetalleId == null) {
                        return GateOutGeneralAssignmentRegisterOutput.builder()
                                .resultState(2)
                                .resultMessage(messagelanguageService.getMessage("PRC_GATE_OUT_EMPTY_LIGHT", 24, input.getLanguageId())) //NO BOOKING AVAILABLE
                                .build();
                    } else { //BRANCH 6.1.2.2: Container preassignment registration
                        documentoCargaDetalleIdTemp = documentoCargaDetalleId;
                        Optional<Catalog> containerTypeCatalog = catalogRepository.findByIdAndParentCatalogId(containerTypeId, catalogsIds.get(CATALOG_CONTAINER_TYPE));
                        refrigeratedCargo = containerTypeCatalog.map(Catalog::getCode).orElse("").equals("1");

                        containerPreassignmentRepository.save(ContainerPreassignment.builder()
                                .bookingDetail(BookingDetail.builder().id(bookingDetalleId).build())
                                .container(Container.builder().id(input.getContainerId()).build())
                                .preassignmentDate(LocalDateTime.now())
                                .catOriginPreassignment(Catalog.builder().id(catalogsIds.get(CATALOG_DOCUMENT_CREATION_ORIGIN_GATE_OUT_AUTOMATIC_ALIAS)).build())
                                .active(true)
                                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                                .registrationDate(LocalDateTime.now())
                                .tracePreassignment("asig_gral_gout_v2")
                                .cargoDocumentDetail(CargoDocumentDetail.builder().id(documentoCargaDetalleId).build())
                                .build());

                        cargoDocumentDetailRepository.gateOutContainerAssign(documentoCargaDetalleId, input.getContainerId(), tare, containerNumber, "asig_gral_gout_v2", input.getUserRegistrationId(), containerTypeId);

                        //BRANCH 6.1.2.2.1: No vessel programming container found
                        if (!Boolean.TRUE.equals(vesselProgrammingContainerRepository.existsByContainerIdAndVesselProgrammingDetailId(input.getContainerId(), programacionNaveDetalleId))) {
                            vesselProgrammingContainerRepository.save(VesselProgrammingContainer.builder()
                                    .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(programacionNaveDetalleId).build())
                                    .container(Container.builder().id(input.getContainerId()).build())
                                    .catManifestedContainerType(Catalog.builder().id(containerTypeId).build())
                                    .catManifestedSize(Catalog.builder().id(containerSizeId).build())
                                    .manifestedSeal1(input.getSeal1())
                                    .manifestedSeal2(input.getSeal2())
                                    .manifestedSeal3(input.getSeal3())
                                    .manifestedSeal4(input.getSeal4())
                                    .indirectDelivery(true)
                                    .active(true)
                                    .catCreationOrigin(Catalog.builder().id(catalogsIds.get(CATALOG_DOCUMENT_CREATION_ORIGIN_GATE_OUT_AUTOMATIC_ALIAS)).build())
                                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                                    .registrationDate(LocalDateTime.now())
                                    .isDangerousCargo(dangerousCargo)
                                    .isRefrigeratedCargo(refrigeratedCargo)
                                    .emptyGateInSettled(false)
                                    .traceProgVesCnt("asig_gral_gout_v2")
                                    .build());
                        }

                        Integer qtyAttended = cargoDocumentDetailRepository.findByBookingDetailIdsAndContainorIdsAndActiveTrue(List.of(bookingDetalleId), List.of()).size();
                        bookingDetailRepository.updateAttendedQuantity(bookingDetalleId, qtyAttended, "asig_gral_gout_v2", input.getUserRegistrationId(), containerTypeId);
                    }
                }

                //TODO: CHECK IF THIS CODE IS NECESSARY SINCE THERE ARE NOT GOING TO BE RESULTS
                if (documentoCargaDetalleIdTemp == null) {
                    Optional<CargoDocumentDetail> cargoDocumentDetail = cargoDocumentDetailRepository.findFirstByBookingIdAndContainerIdAndActive(pageable, bookingId, input.getContainerId(), true).stream().findFirst();
                    documentoCargaDetalleId = cargoDocumentDetail.map(CargoDocumentDetail::getId).orElse(null);
                    documentoCargaReferencia = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCargoDocument).orElse(null);
                    catCargoDocumentTypeId = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCatCargoDocumentType).map(Catalog::getId).orElse(catalogsIds.get(IS_DOCUMENT_TYPE_BK));
                }
            }

            //BRANCH 6.2: Full container
            if (catalogsIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId)) {
                cargoDocumentDetailRepository.updateDispatchedQUantity(documentoCargaDetalleId, 1, 0, input.getUserRegistrationId(), "asig_gral_gout_v2");

                //BRANCH 6.2.1: No planning detail found
                if (input.getPlanningDetailId() != null) {
                    transportPlanningDetailRepository.updateCatStateTrkPlanning(input.getPlanningDetailId(), catalogsIds.get(CATALOG_TRK_PLAN_IN_PROCESS));
                }
            }

            //BRANCH 6.3: EIR update
            if (containerCategoryIdList.stream().anyMatch(cat -> cat.equals(catEmptyFullId)) && containerDummyId.equals(containerEirId)) {
                allocationDateLight = LocalDateTime.now();
                Eir actualEir = eir.get();
                actualEir.setContainer(Container.builder().id(input.getContainerId()).build());
                actualEir.setShippingLine(Optional.ofNullable(shippingLineBkId).map(id -> ShippingLine.builder().id(id).build()).orElse(actualEir.getShippingLine()));
                actualEir.setTaraCnt(tare);
                actualEir.setCatMeasureTare(Catalog.builder().id(catEquipMeasureTareId).build());
                actualEir.setCargoMaximumCnt(payload);
                actualEir.setCatMeasurePayload(Catalog.builder().id(catEquipMeasurePayloadId).build());
                actualEir.setIsoCode(IsoCode.builder().id(codigoISOId).build());
                actualEir.setCatContainerType(Catalog.builder().id(containerTypeId).build());
                actualEir.setCatSizeCnt(Catalog.builder().id(containerSizeId).build());
                actualEir.setCatClassCnt(Catalog.builder().id(gradeId).build());
                actualEir.setCatTypeReefer(Optional.ofNullable(reeferTypeId).map(id -> Catalog.builder().id(id).build()).orElse(null));
                actualEir.setCatEngineBrand(Optional.ofNullable(catMarcaMotorId).map(id -> Catalog.builder().id(id).build()).orElse(null));
                actualEir.setDateManufacture(manufacture);
                actualEir.setStructureWithDamage(boxWithDamage);
                actualEir.setMachineryWithDamage(machineryWithDamage);
                actualEir.setAssignmentLighDatet(allocationDateLight);
                actualEir.setControlAssignmentLight(Short.valueOf("1"));
                actualEir.setTraceEir("asig_gral_gout_v2");
                actualEir.setModificationDate(LocalDateTime.now());
                actualEir.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                actualEir.setWeightGoods(netWeight);
                actualEir.setCatMeasureWeight(Optional.ofNullable(catReceivedWeightMeasureId).map(id -> Catalog.builder().id(id).build()).orElse(null));
                actualEir.setTransportPlanningDetailFull(Optional.ofNullable(input.getPlanningDetailId()).map(id -> TransportPlanningDetail.builder().id(id).build()).orElse(null));
                eirRepository.save(actualEir);

                eirDocumentCargoDetailRepository.save(EirDocumentCargoDetail.builder()
                        .eir(actualEir)
                        .cargoDocumentDetail(CargoDocumentDetail.builder().id(documentoCargaDetalleId).build())
                        .active(true)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .documentCargoReference(documentoCargaReferencia)
                        .catCargoDocumentType(Catalog.builder().id(catCargoDocumentTypeId).build())
                        .traceEirDocDetail("asig_gral_gout_v2")
                        .build());


            }
        }

        //BRANCH 7: Second EIR update
        if (containerCategoryIdList.stream().anyMatch(cat -> cat.equals(catEmptyFullId))) {
            Eir actualEir = eir.get();
            actualEir.setSeal1(input.getSeal1());
            actualEir.setSeal2(input.getSeal2());
            actualEir.setSeal3(input.getSeal3());
            actualEir.setSeal4(input.getSeal4());
            actualEir.setAssignmentLighDatet(allocationDateLight);
            actualEir.setControlAssignmentLight(Short.valueOf("1"));
            actualEir.setTraceEir("asig_gral_gout_v2");
            actualEir.setModificationDate(LocalDateTime.now());
            actualEir.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
            actualEir.setObservation(Optional.ofNullable(input.getComments()).map(String::trim).orElse(""));
            eirRepository.save(actualEir);

            InspectionGate newInspectionGate = inspectionGateRepository.saveAndFlush(InspectionGate.builder()
                    .eir(actualEir)
                    .originalContainer(Optional.ofNullable(input.getContainerId()).map(id -> Container.builder().id(input.getContainerId()).build()).orElse(Container.builder().id(containerEirId).build()))
                    .assignmentDate(LocalDateTime.now())
                    .assignmentUser(User.builder().id(input.getUserRegistrationId()).build())
                    .approvedInspection("0")
                    .active(true)
                    .registrationDate(LocalDateTime.now())
                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                    .build());
            inspeccionGateId = newInspectionGate.getId();

            List<GateOutGeneralRegisterPicture> photos = Arrays.stream(gson.fromJson(input.getPhotos(), GateOutGeneralRegisterPicture[].class)).toList();
            List<Attachment> attachmentsToSave = photos.stream().map(ph -> Attachment.builder()
                    .name(ph.getName())
                    .format(ph.getFormat())
                    .weight(Integer.valueOf(ph.getWeight()))
                    .location(ph.getLocation())
                    .status(true)
                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                    .registrationDate(LocalDateTime.now())
                    .id1(ph.getId())
                    .url(ph.getUrl())
                    .build()).toList();

            List<Attachment> savedAttachments = attachmentRepository.saveAllAndFlush(attachmentsToSave);
            Integer finalInspeccionGateId = inspeccionGateId;
            List<InspectionGatePhoto> inspectionGatePhotosToSave = savedAttachments.stream().map(at -> InspectionGatePhoto.builder()
                    .inspeccionGate(InspectionGate.builder().id(finalInspeccionGateId).build())
                    .attachment(at)
                    .task("ASI")
                    .active(true)
                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                    .registrationDate(LocalDateTime.now())
                    .build()).toList();
            inspectionGatePhotoRepository.saveAll(inspectionGatePhotosToSave);
        }

        //BRANCH 8: Chassis update
        if (input.getChassisId() != null && eirChassisId != null) {
            eirChassisRepository.updateEirChassisDetail(eirChassisId, documentChassisDetailId, input.getChassisId(), "1", "asig_gral_gout_v2");
            chassisDocumentDetailRepository.updateChassisDetails(documentChassisDetailId, input.getChassisId(), catalogsIds.get(CATALOG_CHASSIS_IN_PROGRESS), input.getUserRegistrationId(), "asig_gral_gout_v2");

            Integer chaQtyAttended = chassisDocumentDetailRepository.findByBookingChassisIdAndActiveWithNotNullDocumentChassisDetail(bookingChassisId, true).size();
            chassisBookingDocumentRepository.updateQuantityAttendedAndOtherFields(bookingChassisId, chaQtyAttended, input.getUserRegistrationId());
        }


        eirRepository.flush();
        flagAssignment = Integer.valueOf(eirRepository.findById(input.getEirId()).map(Eir::getControlAssignmentLight).orElse(Short.valueOf("0")));
        //BRANCH 9: Confirmation
        if (!Integer.valueOf(1).equals(flagAssignment)) {
            return GateOutGeneralAssignmentRegisterOutput.builder()
                    .resultState(2)
                    .resultMessage(messagelanguageService.getMessage("PRC_GATE_OUT_EMPTY_LIGHT", 25, input.getLanguageId()))
                    .build();
        } else { //BRANCH 10: Success result
            output.setResultState(1);
            output.setResultMessage(containerNotApplica.equals(containerEirId) ?
                    messagelanguageService.getMessage("GENERAL", 10, input.getLanguageId()) :
                    messagelanguageService.getMessage("PRC_GO_GENERAL", 14, input.getLanguageId()));
        }

        //BRANCH 11: sdy integration
        if (input.getSystemRuleId() != null
                && catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId)
                && input.getContainerId() != null) {
            String subBusinessUnitAlias = eir.map(Eir::getSubBusinessUnit).map(BusinessUnit::getBusinesUnitAlias).orElse(null);
            Optional<SystemRule> systemRule = Optional.ofNullable(systemRuleRepository.findByAliasAndActiveTrue(input.getSystemRuleId()));
            List<SystemRuleDTO> integrationItems = Arrays.stream(gson.fromJson(systemRule.map(SystemRule::getRule).orElse(""), SystemRuleDTO[].class)).toList();
            String typeProductIntegration = integrationItems.stream().filter(ii -> ii.getSubBusinessUnitLocalAlias().equals(subBusinessUnitAlias)).findFirst().map(SystemRuleDTO::getTypeProductIntegration).orElse(null);

            JSONArray integrationJsonArray = new JSONArray();
            integrationJsonArray.put(new JSONObject()
                            .put("type_product_integration", typeProductIntegration)
                            .put("containerNumber", containerNumber)
                    //.put("integration_data", null)
            );
            output.setIntegrationData(integrationJsonArray.toString());
        }

        output.setResultState(1);
        return output;
    }

    //PUT A MESSAGE ON THE EDI CODECO QUEUE
    public void updateDamageCode(GateOutGeneralAssignmentRegisterInput.Input input) {
        try {
            Integer shippingLineAux = null;
            Integer shippingLineBkId = null;
            List<GateOutGeneralAssignmentRegisterBKDetDTO> bkDets = new ArrayList<>();

            Gson gson = new Gson();
            Pageable pageable = PageRequest.of(0, 1);

            HashMap<String, Integer> catalogsIds = catalogService.findIdsByAliases(this.catalogAliases);
            Optional<Eir> eir = eirRepository.findById(input.getEirId());
            Optional<Booking> booking = eir.map(Eir::getBookingGout);
            Integer subBusinessUnitId = eir.map(Eir::getSubBusinessUnit).map(BusinessUnit::getId).orElse(null);
            Integer catEmptyFullId = eir.map(Eir::getCatEmptyFull).map(Catalog::getId).orElse(null);
            Integer catProcedenciaId = eir.map(Eir::getCatOrigin).map(Catalog::getId).orElse(null);
            Integer bookingId = eir.map(Eir::getBookingGout).map(Booking::getId).orElse(null);

            Optional<StockEmpty> activeGateInEmptyStock = stockEmptyRepository.findByContainerAndBusinessUnitAndInStockAndActive(input.getContainerId(), subBusinessUnitId, true, true);
            Integer eirIdGateIn = activeGateInEmptyStock.map(StockEmpty::getGateInEir).map(Eir::getId).orElse(null);
            Optional<Container> container = containerRepository.findById(input.getContainerId());
            Integer containerTypeId = container.map(Container::getCatContainerType).map(Catalog::getId).orElse(null);
            Integer containerSizeId = container.map(Container::getCatSize).map(Catalog::getId).orElse(null);

            Optional<ContainerPreassignment> containerPreassignment = containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionOne(pageable, bookingId, input.getContainerId(), catalogsIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).stream().findFirst();
            if (containerPreassignment.isEmpty()) {
                containerPreassignment = containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionTwo(pageable, bookingId, input.getContainerId(), catalogsIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).stream().findFirst();
            }
            shippingLineBkId = containerPreassignment.map(ContainerPreassignment::getBookingDetail).map(BookingDetail::getBooking).map(Booking::getShippingLine).map(ShippingLine::getId).orElse(null);
            if (containerPreassignment.isEmpty()) {
                String bkRemarkRule = eir.map(Eir::getBookingGout).map(Booking::getRemarkRulesName).orElse("");
                Integer finalContainerTypeId = containerTypeId;
                List<BookingDetail> bookingDetails = new ArrayList<>();

                if (bkRemarkRule.isBlank()
                        || ("FLAG_TO_FLEX".equals(bkRemarkRule)
                        && List.of(catalogsIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS), catalogsIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).stream().noneMatch(cat -> cat.equals(finalContainerTypeId)))) {
                    bookingDetails = bookingDetailRepository.findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListWithNoRemarkRule(bookingId, containerSizeId, List.of(containerTypeId));
                    bkDets.addAll(bookingDetails.stream()
                            .map(bk -> GateOutGeneralAssignmentRegisterBKDetDTO.builder()
                                    .booking_detalle_id(bk.getId())
                                    .linea_naviera_id(booking.map(Booking::getShippingLine).map(ShippingLine::getId).orElse(null))
                                    .assigned(0)
                                    .required(bk.getReservationQuantity())
                                    .pending(0)
                                    .empresa_cliente_id(booking.map(Booking::getClientCompany).map(Company::getId).orElse(null))
                                    .programacion_nave_detalle_id(booking.map(Booking::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(null))
                                    .imo_id(booking.map(Booking::getImo).map(Imo::getId).orElse(null))
                                    .build()).toList());
                }

                if ("FLAG_TO_FLEX".equals(bkRemarkRule)
                        && List.of(catalogsIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS), catalogsIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).stream().anyMatch(cat -> cat.equals(finalContainerTypeId))) {
                    bookingDetails = bookingDetailRepository.findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListAndRemarkRule(bookingId, containerSizeId, List.of(catalogsIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS), catalogsIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)), "FLAG_TO_FLEX");
                    bkDets.addAll(bookingDetails.stream()
                            .map(bk -> GateOutGeneralAssignmentRegisterBKDetDTO.builder()
                                    .booking_detalle_id(bk.getId())
                                    .linea_naviera_id(booking.map(Booking::getShippingLine).map(ShippingLine::getId).orElse(null))
                                    .assigned(0)
                                    .required(bk.getReservationQuantity())
                                    .pending(0)
                                    .empresa_cliente_id(booking.map(Booking::getClientCompany).map(Company::getId).orElse(null))
                                    .programacion_nave_detalle_id(booking.map(Booking::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(null))
                                    .imo_id(booking.map(Booking::getImo).map(Imo::getId).orElse(null))
                                    .build()).toList());
                }

                List<ContainerPreassignment> containerPreassignments = containerPreassignmentRepository.findAllByBookingDetailIdAndActive(bookingDetails.stream().map(BookingDetail::getId).toList(), true);
                List<BookingDetail> finalBookingDetails = bookingDetails;
                bkDets.forEach(bk -> {
                    bk.setAssigned(containerPreassignments.stream()
                            .filter(cp -> finalBookingDetails.stream()
                                    .filter(bd -> bd.getId().equals(cp.getBookingDetail()))
                                    .map(BookingDetail::getId)
                                    .findFirst().orElse(0).equals(bk.getBooking_detalle_id())).toList().size());
                    bk.setPending(bk.getRequired() - bk.getAssigned());
                });
                bkDets = bkDets.stream().filter(bk -> bk.getPending() > 0).toList();

                GateOutGeneralAssignmentRegisterBKDetDTO lastDetail = bkDets.getLast();
                shippingLineBkId = lastDetail.getLinea_naviera_id();
            }

            String linesWithSubLinesSR = systemRuleRepository.findRuleByIdAndActiveTrue("sds_subshippingline_equivalence");
            List<LinesWithSubLinesDTO> subLines = Arrays.stream(gson.fromJson(linesWithSubLinesSR, LinesWithSubLinesDTO[].class)).toList();
            Integer finalShippingLineBkId = shippingLineBkId;
            shippingLineAux = subLines.stream()
                    .filter(sl -> sl.getSubLineId().equals(finalShippingLineBkId))
                    .findFirst().map(LinesWithSubLinesDTO::getLineMainId).orElse(null);

            Optional<GateTransmissionLocalSetting> gateTransmissionLocalSetting = gateTransmissionLocalSettingRepository.findByShippingLineIdAndBusinessUnitsAndCatStatusActivityFormatAndActive(shippingLineAux, subBusinessUnitId, input.getSubBusinessUnitLocalId(), true, catalogsIds.get(IS_TELEX_FORMAT), true);

            if (catalogsIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId) && shippingLineAux != null && eirIdGateIn != null
                    && catalogsIds.get(CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS).equals(catProcedenciaId)
                    && gateTransmissionLocalSetting.isPresent()) {
                Short isReefer = Short.valueOf(catalogRepository.findByIdAndParentCatalogId(containerTypeId, catalogsIds.get(CATALOG_CONTAINER_TYPE)).map(Catalog::getCode).orElse("0"));
                Integer seteoEdiCodeco = gateTransmissionLocalSetting.map(GateTransmissionLocalSetting::getGateTransmissionSetting).map(GateTransmissionSetting::getId).orElse(0);
                String annotation = "[GRAL G.Out " + input.getEirId() + "] ";

                if (seteoEdiCodeco > 0) {
                    gateTransmissionRepository.telexUpdateDamageManual(eirIdGateIn, seteoEdiCodeco, "N", input.getUserRegistrationId(), isReefer, Short.valueOf("0"), annotation);
                }
            }
        } catch (Exception e) {
            logger.error("Error occurred while updating damage code", e);
            logRepository.save(Log.builder()
                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                    .origin(this.getClass().getName())
                    .line(e.getStackTrace()[0].getLineNumber())
                    .errorNumber(1)
                    .message(e.getMessage())
                    .registrationDate(LocalDateTime.now())
                    .build());
        }
    }
}
