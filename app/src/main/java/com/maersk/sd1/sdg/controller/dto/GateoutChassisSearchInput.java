package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class GateoutChassisSearchInput {

    @Data
    public static class Input {
        
        @JsonProperty("sub_business_unit_id")
        private int subBusinessUnitId;

        @JsonProperty("document_chassis_number")
        private String documentChassisNumber;

        @JsonProperty("language_id")
        private int languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix sdg;

        public Input getInput() {
            return sdg != null ? sdg.getInput() : null;
        }
    }

}