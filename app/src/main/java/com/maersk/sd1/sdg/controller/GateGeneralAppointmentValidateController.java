package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.sdg.controller.dto.GateGeneralAppointmentValidateInput;
import com.maersk.sd1.sdg.controller.dto.GateGeneralAppointmentValidateOutput;
import com.maersk.sd1.sdg.service.GateGeneralAppointmentValidateService;

import com.maersk.sd1.common.controller.dto.ResponseController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("/ModuleSDG/module/sdg/SDGGateOutServiceImp")
public class GateGeneralAppointmentValidateController {
    private static final Logger logger = LogManager.getLogger(GateGeneralAppointmentValidateController.class.getName());

    private final GateGeneralAppointmentValidateService service;

    @Autowired
    public GateGeneralAppointmentValidateController(GateGeneralAppointmentValidateService service) {
        this.service = service;
    }

    @PostMapping("/sdggateGeneralAppointmentValidate")
    public ResponseEntity<ResponseController<GateGeneralAppointmentValidateOutput>> gateGeneralAppointmentValidate(@RequestBody GateGeneralAppointmentValidateInput.Root request)
    {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null
                    || request.getPrefix().getInput().getTypeProcess() == null || request.getPrefix().getInput().getSubBusinessUnitLocalId() == null ) {
                return ResponseEntity.ok(new ResponseController<>(Constants.INVALID_INPUT));
            }
            return service.gateGeneralAppointmentValidateService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request."));
        }
    }
}

