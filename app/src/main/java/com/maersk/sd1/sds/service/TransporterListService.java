package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.sds.controller.dto.TransporterListOutput;
import com.maersk.sd1.common.repository.CompanyRoleRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class TransporterListService {

    private static final Logger logger = LogManager.getLogger(TransporterListService.class);

    private final CompanyRoleRepository companyRoleRepository;

    public TransporterListService(CompanyRoleRepository companyRoleRepository) {
        this.companyRoleRepository = companyRoleRepository;
    }

    @Transactional(readOnly = true)
    public List<TransporterListOutput> getTransporters(Integer businessUnitId, Integer viewType) {
        logger.info("Fetching transporters for businessUnitId: {} with viewType: {}", businessUnitId, viewType);
        try {
            List<Company> companies = companyRoleRepository.findTransportersByBusinessUnit(businessUnitId);
            TransporterListOutput transporterListOutput = new TransporterListOutput();

            List<TransporterListOutput> detailList = companies.stream().map(c -> {
                TransporterListOutput detail = new TransporterListOutput();
                detail.setCompanyId(c.getId());
                if (viewType != null && viewType == 1) {
                    detail.setDocument(c.getDocument());
                    detail.setLegalName(c.getLegalName());
                } else {
                    String combined = c.getDocument() + " - " + c.getLegalName();
                    detail.setCompany(combined);
                }
                return detail;
            }).toList();

            return detailList;
        } catch (Exception ex) {
            logger.error("Error while fetching transporters", ex);
            throw ex;
        }
    }
}