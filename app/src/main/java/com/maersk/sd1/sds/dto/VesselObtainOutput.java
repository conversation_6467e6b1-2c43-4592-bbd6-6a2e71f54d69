package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class VesselObtainOutput {

    @JsonProperty("nave_id")
    private Integer naveId;

    @JsonProperty("nave")
    private String ship;

    @JsonProperty("call_sign")
    private String callSign;

    @JsonProperty("imo_number")
    private String imoNumber;

    @JsonProperty("activo")
    private Boolean active;

    @JsonProperty("fecha_registro")
    private LocalDateTime registrationDate;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime modificationDate;

    @JsonProperty("nombre")
    private String name;
}
