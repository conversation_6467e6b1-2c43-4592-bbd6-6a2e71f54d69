package com.maersk.sd1.sds.service;


import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Vessel;
import com.maersk.sd1.common.model.VesselProgramming;
import com.maersk.sd1.sds.dto.VesselProgrammingUpdateOutput;
import com.maersk.sd1.common.repository.VesselProgrammingRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class VesselProgrammingUpdateService {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingUpdateService.class);

    private final VesselProgrammingRepository vesselProgrammingRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    public VesselProgrammingUpdateService(VesselProgrammingRepository vesselProgrammingRepository,
                                         MessageLanguageRepository messageLanguageRepository) {
        this.vesselProgrammingRepository = vesselProgrammingRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public VesselProgrammingUpdateOutput updateVesselProgramming(Integer programacionNaveId,
                                                                 Integer newVesselId,
                                                                 String newVoyage,
                                                                 LocalDateTime newEta,
                                                                 LocalDateTime newEtd,
                                                                 Long shippingAgencyCompanyId,
                                                                 Long opePortCompanyId,
                                                                 Boolean newActive,
                                                                 Long userId,
                                                                 Integer languageId) {
        VesselProgrammingUpdateOutput output = new VesselProgrammingUpdateOutput();
        try {
            Optional<VesselProgramming> existingOpt = vesselProgrammingRepository.findById(programacionNaveId);
            if (existingOpt.isEmpty()) {
                output.setRespEstado(2);
                String errorMsg = "Record with id " + programacionNaveId + " not found.";
                logger.error(errorMsg);
                output.setRespMensaje(errorMsg);
                return output;
            }

            VesselProgramming existing = existingOpt.get();
            Long subUnidadNegocioId;
            if (existing.getSubBusinessUnit() != null) {
                if (existing.getSubBusinessUnit().getId() != null) {
                    subUnidadNegocioId = Long.valueOf(existing.getSubBusinessUnit().getId());
                } else {
                    subUnidadNegocioId = 0L;
                }
            } else {
                subUnidadNegocioId = 0L;
            }

            String oldVoyage = existing.getVoyage();
            Integer oldVesselId = existing.getVessel() != null ? existing.getVessel().getId() : null;

            boolean voyageChanged = (oldVoyage != null && !oldVoyage.equalsIgnoreCase(newVoyage))
                    || (oldVoyage == null && newVoyage != null);
            boolean vesselChanged = (oldVesselId != null && !oldVesselId.equals(newVesselId))
                    || (oldVesselId == null && newVesselId != null);

            if (voyageChanged || vesselChanged) {
                Optional<VesselProgramming> conflict = vesselProgrammingRepository.findConflict(
                        subUnidadNegocioId,
                        newVesselId,
                        newVoyage
                );
                if (conflict.isPresent()) {
                    output.setRespEstado(2);
                    String msg = messageLanguageRepository.fnTranslatedMessage("INS_PROGRAMACION_NAVE", 1, languageId);
                    msg = msg.replace("{IDX}", conflict.get().getId().toString());
                    output.setRespMensaje(msg);
                    return output;
                }
            }

            Vessel newVessel = new Vessel();
            newVessel.setId(newVesselId);
            existing.setVessel(newVessel);

            existing.setVoyage(newVoyage);
            if (newEta != null) {
                existing.setEtaDate(newEta);
            }
            if (newEtd != null) {
                existing.setEtdDate(newEtd);
            }

            if (shippingAgencyCompanyId != null) {
                Company shippingAgency = new Company();
                shippingAgency.setId(Math.toIntExact(shippingAgencyCompanyId));
                existing.setShippingAgencyCompany(shippingAgency);
            } else {
                existing.setShippingAgencyCompany(null);
            }

            if (opePortCompanyId != null) {
                Company opePortCompany = new Company();
                opePortCompany.setId(Math.toIntExact(opePortCompanyId));
                existing.setOpePortCompany(opePortCompany);
            } else {
                existing.setOpePortCompany(null);
            }

            existing.setActive(newActive);

            if (userId != null) {
                User modificationUser = new User();
                modificationUser.setId(Math.toIntExact(userId));
                existing.setModificationUser(modificationUser);
            } else {
                existing.setModificationUser(null);
            }

            existing.setModificationDate(LocalDateTime.now());

            vesselProgrammingRepository.save(existing);

            // success
            output.setRespEstado(1);
            String successMsg = messageLanguageRepository.fnTranslatedMessage("UPD_PROGRAMACION_NAVE", 1, languageId);
            output.setRespMensaje(successMsg);
            return output;
        } catch (Exception e) {
            logger.error("Error in updateVesselProgramming", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return output;
        }
    }
}

