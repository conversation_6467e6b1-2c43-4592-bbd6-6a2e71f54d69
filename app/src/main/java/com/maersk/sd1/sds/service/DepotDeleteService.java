package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.DepotRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sds.controller.dto.DepotDeleteOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class DepotDeleteService {

    private static final Logger logger = LogManager.getLogger(DepotDeleteService.class);

    private final DepotRepository depotRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    public DepotDeleteService(DepotRepository depotRepository, MessageLanguageRepository messageLanguageRepository) {
        this.depotRepository = depotRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public DepotDeleteOutput deleteDepot(Integer depositId, Integer userModificationId, Integer languageId) {
        DepotDeleteOutput output = new DepotDeleteOutput();
        try {
            depotRepository.deactivateDepot(depositId, userModificationId, LocalDateTime.now());
            Integer messageCode = 7;
            String successMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", messageCode, languageId);
            output.setRespStatus(1);
            output.setRespMessage(successMessage);
        } catch (Exception e) {
            logger.error("Error deleting depot", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}