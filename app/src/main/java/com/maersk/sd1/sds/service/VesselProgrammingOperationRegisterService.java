package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.model.VesselProgrammingCutoff;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingOperationRegisterInputDTO;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingOperationRegisterOutputDTO;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@Transactional
public class VesselProgrammingOperationRegisterService {
    private final UserRepository userRepository;
    private final CatalogRepository catalogRepository;
    private final VesselProgrammingRepository vesselProgrammingRepository;

    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;
    private final ShippingLineRepository shippingLineRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public VesselProgrammingOperationRegisterService(
            VesselProgrammingDetailRepository vesselProgrammingDetailRepository,
            VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository,
            ShippingLineRepository shippingLineRepository,
            MessageLanguageRepository messageLanguageRepository,
            VesselProgrammingRepository vesselProgrammingRepository,
            CatalogRepository catalogRepository,
            UserRepository userRepository) {
        this.vesselProgrammingDetailRepository = vesselProgrammingDetailRepository;
        this.vesselProgrammingCutoffRepository = vesselProgrammingCutoffRepository;
        this.shippingLineRepository = shippingLineRepository;
        this.messageLanguageRepository = messageLanguageRepository;
        this.vesselProgrammingRepository = vesselProgrammingRepository;
        this.catalogRepository = catalogRepository;
        this.userRepository = userRepository;
    }

    @Transactional
    public VesselProgrammingOperationRegisterOutputDTO registerVesselProgrammingOperation(VesselProgrammingOperationRegisterInputDTO.Input input) {
        try {
            List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffRecords = cleanAndFilterCutoffRecords(input.getCutoffRetiroVacios());
            Optional<VesselProgrammingDetail> detailOpt = vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(input.getVesselProgrammingId(), input.getCatOperationId());

            if (detailOpt.isPresent() && Boolean.TRUE.equals(detailOpt.get().getActive())) {
                return buildOutput(2, messageLanguageRepository.fnTranslatedMessage("INS_PROGRAMACION_NAVE_OPE", 1, input.getLanguageId()), 0);
            }

            VesselProgrammingDetail detail = detailOpt.orElseGet(VesselProgrammingDetail::new);
            if (detailOpt.isPresent()) {
                updateExistingDetail(detail, input, cutoffRecords);
            } else {
                createNewDetail(detail, input, cutoffRecords);
            }

            return buildOutput(1, messageLanguageRepository.fnTranslatedMessage("INS_PROGRAMACION_NAVE_OPE", 2, input.getLanguageId()), detail.getId());
        } catch (Exception e) {
            log.error("Error in registerVesselProgrammingOperation", e);
            return buildOutput(0, e.getMessage(), 0);
        }
    }

    List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cleanAndFilterCutoffRecords(List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffRetiroVacios) {
        List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffRecords = new ArrayList<>();
        if (cutoffRetiroVacios != null && !cutoffRetiroVacios.isEmpty()) {
            for (var cutoff : cutoffRetiroVacios) {
                cutoffRecords.add(getCutoffRetiroVacio(cutoff));
            }
            List<Integer> lineIds = cutoffRecords.stream().map(VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio::getShippingLineId).filter(Objects::nonNull).toList();
            List<Integer> existingIds = shippingLineRepository.findByIdInAndActiveTrue(lineIds).stream().map(ShippingLine::getId).toList();
            cutoffRecords.removeIf(c -> c.getShippingLineId() != null && !existingIds.contains(c.getShippingLineId()));
        }
        return cutoffRecords;
    }

    private void updateExistingDetail(VesselProgrammingDetail detail, VesselProgrammingOperationRegisterInputDTO.Input input, List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffRecords) {
        vesselProgrammingCutoffRepository.disableActiveCutoffsByDetail(detail.getId(), input.getUserRegistrationId());
        updateDetailFields(detail, input);
        vesselProgrammingDetailRepository.save(detail);
        insertCutoffs(detail, cutoffRecords, input.getUserRegistrationId());
    }

    private void createNewDetail(VesselProgrammingDetail detail, VesselProgrammingOperationRegisterInputDTO.Input input, List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffRecords) {
        setNewDetailFields(detail, input);
        vesselProgrammingDetailRepository.save(detail);
        insertCutoffs(detail, cutoffRecords, input.getUserRegistrationId());
    }

    private void updateDetailFields(VesselProgrammingDetail detail, VesselProgrammingOperationRegisterInputDTO.Input input) {
        detail.setManifestYear(input.getManifestYear());
        detail.setManifestNumber(input.getManifestNumber());
        detail.setBeginningOperation(input.getBeginningOperation());
        detail.setEndingOperation(input.getEndingOperation());
        detail.setActive(true);
        detail.setManifestCustomsDate(input.getManifestCustomsDate());
        detail.setBeginningReturnAppointment(input.getBeginningReturnAppointment());
        detail.setExpoEcuAppointmentsBegginingDate(input.getExpoEcuAppointmentsBegginingDate());
        detail.setDryPortCutOffDate(input.getDryPortCutoffDate());
        detail.setReeferPortCutoffDate(input.getReeferPortCutoffDate());
        detail.setDryyDepositCutoffDate(input.getDryyDepositCutoffDate());
        detail.setReeferDepositCutoffDate(input.getReeferDepositCutoffDate());
        detail.setModificationUser(new User(input.getUserRegistrationId()));
        detail.setModificationDate(LocalDateTime.now());
    }

    private void setNewDetailFields(VesselProgrammingDetail detail, VesselProgrammingOperationRegisterInputDTO.Input input) {
        detail.setVesselProgramming(vesselProgrammingRepository.getReferenceById(input.getVesselProgrammingId()));
        detail.setCatOperation(catalogRepository.getReferenceById(input.getCatOperationId()));
        detail.setCatCreationOrigin(catalogRepository.getReferenceById(Parameter.CAT_ORIGIN));
        updateDetailFields(detail, input);
        detail.setRegistrationUser(userRepository.getReferenceById(input.getUserRegistrationId()));
        detail.setRegistrationDate(LocalDateTime.now());
    }

    private void insertCutoffs(VesselProgrammingDetail detail, List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffRecords, Integer userRegistrationId) {
        for (var c : cutoffRecords) {
            VesselProgrammingCutoff cutoffEntity = new VesselProgrammingCutoff();
            cutoffEntity.setVesselProgrammingDetail(detail);
            if (c.getShippingLineId() != null) {
                cutoffEntity.setShippingLine(new ShippingLine(c.getShippingLineId()));
            }
            cutoffEntity.setDateCutoffRetreatEmptyDry(parseLocalDateTime(c.getRetiroDry()));
            cutoffEntity.setDateCutoffRetreatEmptyReefer(parseLocalDateTime(c.getRetiroReefer()));
            cutoffEntity.setActive(true);
            cutoffEntity.setRegistrationUser(new User(userRegistrationId));
            cutoffEntity.setRegistrationDate(LocalDateTime.now());
            vesselProgrammingCutoffRepository.save(cutoffEntity);
        }
    }

    private VesselProgrammingOperationRegisterOutputDTO buildOutput(int respEstado, String respMensaje, int respNewId) {
        VesselProgrammingOperationRegisterOutputDTO output = new VesselProgrammingOperationRegisterOutputDTO();
        output.setRespEstado(respEstado);
        output.setRespMensaje(respMensaje);
        output.setRespNewId(respNewId);
        return output;
    }

    private static VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio getCutoffRetiroVacio(VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio cutoff) {
        Integer lineId = (cutoff.getShippingLineId() != null && cutoff.getShippingLineId() == 0) ? null : cutoff.getShippingLineId();
        String retiroDry = (cutoff.getRetiroDry() != null && cutoff.getRetiroDry().isEmpty()) ? null : cutoff.getRetiroDry();
        String retiroReefer = (cutoff.getRetiroReefer() != null && cutoff.getRetiroReefer().isEmpty()) ? null : cutoff.getRetiroReefer();

        VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio cleaned = new VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio();
        cleaned.setShippingLineId(lineId);
        cleaned.setRetiroDry(retiroDry);
        cleaned.setRetiroReefer(retiroReefer);
        return cleaned;
    }

    private static final List<DateTimeFormatter> FORMATTERS = List.of(
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    );

    static LocalDateTime parseLocalDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            log.warn("Invalid date-time input: null or empty");
            return null;
        }

        dateTimeStr = dateTimeStr.trim();

        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                return LocalDateTime.parse(dateTimeStr, formatter);
            } catch (DateTimeParseException e) {
                log.debug("Failed to parse '{}' with format '{}'", dateTimeStr, formatter);
            }
        }

        log.error("Unable to parse date-time: '{}'. Supported formats: {}", dateTimeStr, FORMATTERS);
        return null;
    }
}
