package com.maersk.sd1.sds.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sds.controller.dto.BlManualRegisterDetailDTO;
import com.maersk.sd1.sds.controller.dto.BlManualRegisterImoDto;
import com.maersk.sd1.sds.controller.dto.BlManualRegisterInput;
import com.maersk.sd1.sds.controller.dto.BlManualRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Logger;

import static com.maersk.sd1.common.Parameter.*;

@RequiredArgsConstructor
@Service
public class BlManualRegisterService {

    private final Logger logger = Logger.getLogger(BlManualRegisterService.class.getName());

    private final MessageLanguageService messageLanguageService;
    private final GESCatalogService catalogService;
    private final ParameterSpLogRepository parameterSpLogRepository;
    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final VesselProgrammingRepository vesselProgrammingRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final CatalogRepository catalogRepository;
    private final ContainerRepository containerRepository;
    private final CompanyRepository companyRepository;
    private final DepotRepository depotRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final IsoCodeRepository isoCodeRepository;
    private final DemurrageReceptionSettingBURepository demurrageReceptionSettingBURepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final DemurrageReceptionSettingRepository demurrageReceptionSettingRepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final VesselProgrammingContainerImoRepository vesselProgrammingContainerImoRepository;
    private final DemurrageReceptionRepository demurrageReceptionRepository;
    private final ImoRepository imoRepository;
    private final UserRepository userRepository;

    private static List<String> catalogAliases = List.of(
            DISCHARGE_FULL,
            CATALOG_DEPOT_OPER_MTY_DISCHARGE,
            CATALOG_CONTAINER_PACKAGING,
            CATALOG_MEASURE_WEIGHT_KG_ALIAS,
            CATALOG_FCL_EMPTY_MEASUREMENT_UNIT,
            CATALOG_LCL_BB_MEASUREMENT_UNIT,
            CATALOG_PRIMARY_CARGO_ORIGIN,
            IS_DOCUMENT_ACTIVE,
            CATALOG_SEAWAY_TRANSPORT,
            CATALOG_BL_MANUAL_CREATION_ORIGIN,
            CATALOG_BL_AUTOMATIC_CREATION_ORIGIN,
            CONDITION_FCL,
            CONDITION_MTY,
            CATALOG_RECEPTION_ORIGIN_VARIOUS,
            CATALOG_CREATION_FROM_REGISTER_BL
    );

    private static String datePattern = "yyyyMMdd";
    private static String blLoadError = "PRC_CARGA_BL";

    @Transactional
    public BlManualRegisterOutput blManualRegister(BlManualRegisterInput.Input input) throws Exception {

        BlManualRegisterOutput output = new BlManualRegisterOutput();
        output.setRespEstado(0);
        output.setRespMensaje("");

        Integer productoFCLLCLBB = 1;
        Integer productoMTY = 2;
        Integer programacionNaveId;
        Integer notificanteId;
        String notificanteDetalle;

        HashMap<String, Integer> catalogIds = catalogService.findIdsByAliases(catalogAliases);

        Gson gson = new Gson();
        Type equipmentListType = new TypeToken<ArrayList<BlManualRegisterDetailDTO>>() {
        }.getType();
        Pageable pageable = PageRequest.of(0, 1);
        List<BlManualRegisterDetailDTO> details = new ArrayList<>();
        List<Container> containers = new ArrayList<>();

        BusinessUnit businessUnitClass = businessUnitRepository.findById(input.getUnidadNegocioId())
                .orElseThrow(() -> new RuntimeException("Business Unit not found"));

        BusinessUnit subBusinessUnitClass = businessUnitRepository.findById(input.getSubUnidadNegocioId())
                .orElseThrow(() -> new RuntimeException("Sub Business Unit not found"));

        parameterSpLogRepository.save(
                ParameterSpLog.builder()
                        .nameSp("bl_manual_registrar")
                        .type(1)
                        .reference("input_sp")
                        .parameter1(input.getProgramacionNaveDetalleId())
                        .textParameter1(input.getNumeroBl())
                        .value1(input.getContenedoresJson())
                        .registrationDate(LocalDateTime.now())
                        .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                        .businessUnit(businessUnitClass)  // Set the fetched BusinessUnit here
                        .subBusinessUnit(BusinessUnit.builder().id(input.getSubUnidadNegocioId()).build())
                        .build()
        );

        if (input.getContenedoresJson() != null) {
            details = gson.fromJson(input.getContenedoresJson(), equipmentListType);
            details.forEach(it -> {
                it.setBl(input.getNumeroBl());
                it.setMarcasNumeros("");
                it.setMercaderia("");
                it.setCantidad(BigDecimal.ONE);
                it.setVolumen(BigDecimal.ONE);
                it.setDiceContener(1);
                it.setPrecinto1(null);
                it.setPrecinto2(null);
                it.setPrecinto3(null);
                it.setPrecinto4(null);
            });
        }

        if (input.getContenedoresJson() != null && (details.isEmpty() || details.stream().anyMatch(it -> it.getContenedor() == null))) {
            output.setRespMensaje(messageLanguageService.getMessage(blLoadError, 19, input.getIdiomaId()));
        } else {
            input.setNumeroBl(input.getNumeroBl().toUpperCase());

            //MINIMUN VALIDATIONS
            if (input.getNumeroBl().isBlank())
                output.setRespMensaje(messageLanguageService.getMessage(blLoadError, 1, input.getIdiomaId()));
            if (input.getLineaNavieraId() == null)
                output.setRespMensaje(output.getRespMensaje() + messageLanguageService.getMessage(blLoadError, 3, input.getIdiomaId()));
            if (input.getProgramacionNaveDetalleId() == null)
                output.setRespMensaje(output.getRespMensaje() + messageLanguageService.getMessage(blLoadError, 20, input.getIdiomaId()));
            if (input.getPuertoEmbarqueId() == null)
                output.setRespMensaje(output.getRespMensaje() + messageLanguageService.getMessage(blLoadError, 21, input.getIdiomaId()));
            if (input.getPuertoDescargaId() == null)
                output.setRespMensaje(output.getRespMensaje() + messageLanguageService.getMessage(blLoadError, 22, input.getIdiomaId()));

            if (input.getContenedoresJson() != null &&
                    (details.stream().anyMatch(it -> (it.getFechaMemo() != null && !it.getFechaMemo().isBlank()) && it.getFechaMemo().length() != 8)) ||
                    (details.stream().anyMatch(it -> (it.getFechaMemo() != null && !it.getFechaMemo().isBlank()) && !validateDateString(it.getFechaMemo()))) ||
                    (details.stream().anyMatch(it -> (it.getFechaMemo() != null && !it.getFechaMemo().isBlank()) && validateDateString(it.getFechaMemo()) && LocalDate.parse(it.getFechaMemo(), DateTimeFormatter.ofPattern(datePattern)).isBefore(LocalDate.MIN)))
            ) {
                output.setRespMensaje(output.getRespMensaje() + messageLanguageService.getMessage(blLoadError, 23, input.getIdiomaId()));
            }

            if (!output.getRespMensaje().isBlank()) {
                output.setRespMensaje(messageLanguageService.getMessage(blLoadError, 5, input.getIdiomaId()) + " " + output.getRespMensaje());
            } else {
                Optional<VesselProgrammingDetail> vesselProgrammingDetail = vesselProgrammingDetailRepository.findById(input.getProgramacionNaveDetalleId());
                programacionNaveId = vesselProgrammingDetail.map(VesselProgrammingDetail::getVesselProgramming).map(VesselProgramming::getId).orElse(null);

                if (input.getContenedoresJson() != null) {
                    List<Integer> catalogSizesAndTypesIds = new ArrayList<>();
                    catalogSizesAndTypesIds.addAll(details.stream().map(it -> it.getCatTamanoId()).distinct().toList());
                    catalogSizesAndTypesIds.addAll(details.stream().map(dt -> dt.getCatTipoContenedorId()).distinct().toList());
                    List<Catalog> catalogList = catalogRepository.findByIdList(catalogSizesAndTypesIds);
                    List<String> containerNumbersList = details.stream().map(it -> it.getContenedor()).distinct().toList();
                    containers = containerRepository.findByContainerNumbers(containerNumbersList);

                    for (BlManualRegisterDetailDTO detail : details) {
                        //IF IT HAS A MEMO DATE IT SHOULDN'T BE FCL, IT SHOULD BE THE ONE THE USER CHOOSE
                        if ((detail.getFechaMemo() != null && !detail.getFechaMemo().isBlank()) && catalogIds.get(CONDITION_FCL).equals(detail.getCatCondicionId())) {
                            detail.setCatCondicionId(catalogIds.get(CONDITION_FCL));
                        }

                        //VALIDATE THE FORMAT OF THE MEMO DATE FIELD
                        if ((detail.getFechaMemo() != null && detail.getFechaMemo().length() != 8) ||
                                (detail.getFechaMemo() != null && !validateDateString(detail.getFechaMemo())) ||
                                (detail.getFechaMemo() != null && LocalDate.parse(detail.getFechaMemo(), DateTimeFormatter.ofPattern(datePattern)).isBefore(LocalDate.MIN))) {
                            detail.setFechaMemo(null);
                        }

                        detail.setCatOperacionId(catalogIds.get(catalogIds.get(CONDITION_MTY).equals(detail.getCatCondicionId()) ? CATALOG_DEPOT_OPER_MTY_DISCHARGE : DISCHARGE_FULL));
                        detail.setCntLineaNavieraId(input.getLineaNavieraId());

                        //PACKAGING FOR CONTAINERIZED CARGO
                        detail.setCatEmbalajeId(catalogIds.get(CATALOG_CONTAINER_PACKAGING));

                        //MEASUREMENT UNIT BASED ON THE CARGO CONDITION
                        detail.setCatUnidadMedidaCantidadId(catalogIds.get(List.of(catalogIds.get(CONDITION_FCL), catalogIds.get(CONDITION_MTY)).stream().anyMatch(it -> it.equals(detail.getCatCondicionId())) ? CATALOG_FCL_EMPTY_MEASUREMENT_UNIT : CATALOG_LCL_BB_MEASUREMENT_UNIT));
                        detail.setProductoId(catalogIds.get(CONDITION_MTY).equals(detail.getCatCondicionId()) ? productoMTY : productoFCLLCLBB);

                        detail.setDimension(catalogList.stream().filter(it -> it.getId().equals(detail.getCatTamanoId())).findFirst().map(Catalog::getDescription).orElse(null));
                        detail.setTipocnt(catalogList.stream().filter(it -> it.getId().equals(detail.getCatTipoContenedorId())).findFirst().map(Catalog::getDescription).orElse(null));
                        detail.setEsCntReefer("1".equals(catalogList.stream().filter(it -> it.getId().equals(detail.getCatTipoContenedorId())).findFirst().map(Catalog::getCode).orElse(null)));
                        detail.setCntFamiliaId(catalogList.stream().filter(it -> it.getId().equals(detail.getCatTipoContenedorId())).findFirst().map(Catalog::getVariable3).orElse(null));

                        //GET CONTAINER ID
                        Optional<Container> currentContainer = containers.stream().filter(cnt -> cnt.getContainerNumber().equalsIgnoreCase(detail.getContenedor())).findFirst();
                        detail.setContenedorId(currentContainer.map(Container::getId).orElse(null));
                        detail.setCodigoIsoId(currentContainer.map(Container::getIsoCode).map(IsoCode::getId).orElse(null));

                        if (currentContainer.isPresent() && Boolean.FALSE.equals(currentContainer.get().getActive())) {
                            currentContainer.get().setActive(true);
                            containerRepository.save(currentContainer.get());
                        }
                    }

                    //JAS 19/07/2023
                    if (details.stream().anyMatch(dt -> dt.getContenedorId() == null) && input.getPassRestriction() == null) {
                        String containersList = String.join(", ", details.stream().map(dt -> dt.getContenedor()).toList());
                        output.setRespEstado(3);
                        output.setRespMensaje(messageLanguageService.getMessage("GENERAL_GATE_IN", 3, input.getIdiomaId(), Map.of("{CNTX}", containersList)));
                        //WARNING! CONTAINERS {CNTX} NOT FOUND IN THE DATABASE. Do you want to record this unit as an Empty container transaction?
                        return output;
                    }

                    //CHECK IF A VESSEL PROGRAMMING DETAIL EXISTS FOR THE DISCHARGE EMPTY DETAIL
                    if (details.stream().anyMatch(dt -> catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE).equals(dt.getCatOperacionId())) &&
                            !vesselProgrammingDetailRepository.existsActiveByVesselProgrammingAndOperationCatalog(programacionNaveId, catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE))) {
                        vesselProgrammingDetailRepository.save(
                                VesselProgrammingDetail.builder()
                                        .vesselProgramming(vesselProgrammingRepository.getReferenceById(programacionNaveId))
                                        .catOperation(Catalog.builder().id(catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE)).build())
                                        .manifestYear(String.valueOf(LocalDateTime.now().getYear()))
                                        .manifestNumber(null)
                                        .registrationUser(User.builder().id(1).build())
                                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_BL_MANUAL_CREATION_ORIGIN)).build())
                                        .build()
                        );
                        vesselProgrammingDetailRepository.flush(); //TO ENSURE PERSISTENCE
                    }

                    //CHECK IF A VESSEL PROGRAMMING DETAIL EXISTS FOR THE DISCHARGE FULL DETAIL
                    if (details.stream().anyMatch(dt -> catalogIds.get(DISCHARGE_FULL).equals(dt.getCatOperacionId()))
                            && !vesselProgrammingDetailRepository.existsActiveByVesselProgrammingAndOperationCatalog(programacionNaveId, catalogIds.get(DISCHARGE_FULL))) {
                        vesselProgrammingDetailRepository.save(
                                VesselProgrammingDetail.builder()
                                        .vesselProgramming(vesselProgrammingRepository.getReferenceById(programacionNaveId))
                                        .catOperation(Catalog.builder().id(catalogIds.get(DISCHARGE_FULL)).build())
                                        .manifestYear(String.valueOf(LocalDateTime.now().getYear()))
                                        .manifestNumber(null)
                                        .registrationUser(User.builder().id(1).build())
                                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_BL_MANUAL_CREATION_ORIGIN)).build())
                                        .registrationDate(LocalDateTime.now())
                                        .active(true)
                                        .build()
                        );
                        vesselProgrammingDetailRepository.flush(); //TO ENSURE PERSISTENCE
                    }


                    //ASSIGN THE CARGO DOCUMENT, VESSEL PROGRAMMING DETAILS AND SHIPPING LINE IDS FOR EACH DETAIL
                    // List<CargoDocument> cargoDocuments = cargoDocumentRepository.findByCargoDocuments(details.stream().map(dt -> dt.getBl().toUpperCase()).distinct().toList(), programacionNaveId, input.getUnidadNegocioId(), input.getSubUnidadNegocioId());
                    List<CargoDocument> cargoDocuments = cargoDocumentRepository.findByCargoDocumentsList(
                            details.stream().map(dt -> dt.getBl().toUpperCase()).distinct().toList(),
                            programacionNaveId,
                            businessUnitClass,  // Pass the BusinessUnit object instead of its ID
                            BusinessUnit.builder().id(input.getSubUnidadNegocioId()).build()
                    );

                    for (BlManualRegisterDetailDTO detail : details) {
                        Optional<CargoDocument> currentCargoDocument = cargoDocuments.stream().filter(cd -> cd.getCargoDocument().equalsIgnoreCase(detail.getBl())).findFirst();
                        if (currentCargoDocument.isPresent() && currentCargoDocument.map(CargoDocument::getVesselProgrammingDetail).map(VesselProgrammingDetail::getCatOperation).map(Catalog::getId).orElse(0).equals(detail.getCatOperacionId())) {
                            detail.setDocumentoCargaId(currentCargoDocument.map(CargoDocument::getId).orElse(null));
                            detail.setProgramacionNaveDetalleId(currentCargoDocument.map(CargoDocument::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(null));
                            detail.setCntLineaNavieraId(currentCargoDocument.map(CargoDocument::getShippingLine).map(ShippingLine::getId).orElse(null));
                        }
                    }

                    input.setProgramacionNaveDetalleId(null);

                    if (details.stream().anyMatch(dt -> dt.getProgramacionNaveDetalleId() == null && catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE).equals(dt.getCatOperacionId()))) {
                        Optional<VesselProgrammingDetail> newVesselProgrammingDetail = vesselProgrammingDetailRepository.findActiveByVesselProgrammingAndCatOperation(pageable, programacionNaveId, catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE)).stream().findFirst();
                        input.setProgramacionNaveDetalleId(newVesselProgrammingDetail.map(VesselProgrammingDetail::getId).orElse(null));

                        for (BlManualRegisterDetailDTO detail : details) {
                            if (detail.getProgramacionNaveDetalleId() == null && catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE).equals(detail.getCatOperacionId())) {
                                detail.setProgramacionNaveDetalleId(input.getProgramacionNaveDetalleId());
                            }
                        }
                    }

                    if (details.stream().anyMatch(dt -> dt.getProgramacionNaveDetalleId() == null && catalogIds.get(DISCHARGE_FULL).equals(dt.getCatOperacionId()))) {
                        Optional<VesselProgrammingDetail> newFullVesselProgrammingDetail = vesselProgrammingDetailRepository.findActiveByVesselProgrammingAndCatOperation(pageable, programacionNaveId, catalogIds.get(DISCHARGE_FULL)).stream().findFirst();
                        input.setProgramacionNaveDetalleId(newFullVesselProgrammingDetail.map(VesselProgrammingDetail::getId).orElse(null));

                        for (BlManualRegisterDetailDTO detail : details) {
                            if (detail.getProgramacionNaveDetalleId() == null && catalogIds.get(DISCHARGE_FULL).equals(detail.getCatOperacionId())) {
                                detail.setProgramacionNaveDetalleId(input.getProgramacionNaveDetalleId());
                            }
                        }
                    }
                }

                if (input.getEmpresaEmbarcadorId() != null && input.getEmbarcadorDetalle() == null) {
                    input.setEmbarcadorDetalle(companyRepository.findById(input.getEmpresaEmbarcadorId()).map(Company::getLegalName).orElse(null));
                }

                if (input.getEmpresaConsignatarioId() != null && input.getConsignatarioDetalle() == null) {
                    input.setConsignatarioDetalle(companyRepository.findById(input.getEmpresaConsignatarioId()).map(Company::getLegalName).orElse(null));
                }

                notificanteId = input.getEmpresaConsignatarioId();
                notificanteDetalle = input.getConsignatarioDetalle();

                //IF THERE'S NO INPUT DEPOT GET THE DEFAULT ONE
                if (input.getDepositoVacioId() == null) {
                    input.setDepositoVacioId(depotRepository.findDefaultActiveBySubBusinessUnit(pageable, input.getSubUnidadNegocioId()).stream().findFirst().map(Depot::getId).orElse(null));
                }

                if (input.getContenedoresJson() != null) {
                    List<CargoDocumentDetail> cargoDocumentDetails = cargoDocumentDetailRepository.findByDocumentoCargaIdIn(details.stream().filter(dt -> dt.getDocumentoCargaId() != null).map(dt -> dt.getDocumentoCargaId()).distinct().toList());

                    List<String> isoCodesList = List.of("22R1", "22G1", "2263", "2260", "22U1", "22T1", "45G1", "42G1", "42R1", "45R1", "4534", "4536", "45P3", "4060", "42U1", "40TG", "45U1", "2000", "4000");
                    List<IsoCode> isoCodes = isoCodeRepository.findAllByIsoCode(isoCodesList);
                    isoCodes.addAll(isoCodeRepository.findAllById(details.stream().filter(dt -> dt.getCodigoIsoId() != null).map(BlManualRegisterDetailDTO::getCodigoIsoId).distinct().toList()));

                    for (BlManualRegisterDetailDTO detail : details) {
                        //GET THE CARGO DOCUMENT ID BASED ON THE CONTAINER
                        Optional<CargoDocumentDetail> currentCargoDocumentDetail = cargoDocumentDetails.stream()
                                .filter(cdd -> detail.getDocumentoCargaId().equals(cdd.getCargoDocument().getId())
                                        && Optional.ofNullable(detail.getContenedorId()).orElse(0).equals(Optional.ofNullable(cdd.getContainer()).map(Container::getId).orElse(-1))).findFirst();

                        if (currentCargoDocumentDetail.isPresent()) {
                            detail.setDocumentoCargaDetalleId(currentCargoDocumentDetail.get().getId());
                        }

                        //SET DEFAULT VALUES
                        detail.setEsRefrigerado(false);
                        detail.setEsPeligroso(false);

                        if (detail.getCodigoIsoId() == null) {
                            if ("20".equals(detail.getDimension())) {
                                detail.setCodigoIsoId(switch (detail.getTipocnt()) {
                                    case "RF" ->
                                            isoCodes.stream().filter(ic -> "22R1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "DC" ->
                                            isoCodes.stream().filter(ic -> "22G1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "FR", "FL" ->
                                            isoCodes.stream().filter(ic -> "2263".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "PL" ->
                                            isoCodes.stream().filter(ic -> "2260".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "OT" ->
                                            isoCodes.stream().filter(ic -> "22U1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "TK" ->
                                            isoCodes.stream().filter(ic -> "22T1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    //IF THE ISO CODE IS NULL A GENERIC ONE IS ASSIGNED SINCE THIS FIELD CANNOT BE NULL
                                    default ->
                                            isoCodes.stream().filter(ic -> "2000".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                });
                            } else if ("40".equals(detail.getDimension())) {
                                detail.setCodigoIsoId(switch (detail.getTipocnt()) {
                                    case "HC" ->
                                            isoCodes.stream().filter(ic -> "45G1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "DC" ->
                                            isoCodes.stream().filter(ic -> "42G1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "RF" ->
                                            isoCodes.stream().filter(ic -> "42R1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "RH" ->
                                            isoCodes.stream().filter(ic -> "45R1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "RA" ->
                                            isoCodes.stream().filter(ic -> "4534".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "RE" ->
                                            isoCodes.stream().filter(ic -> "4536".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "FL", "FR" ->
                                            isoCodes.stream().filter(ic -> "45P3".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "PL" ->
                                            isoCodes.stream().filter(ic -> "4060".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "OT" ->
                                            isoCodes.stream().filter(ic -> "42U1".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    case "TK" ->
                                            isoCodes.stream().filter(ic -> "40TG".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                    //IF THE ISO CODE IS NULL A GENERIC ONE IS ASSIGNED SINCE THIS FIELD CANNOT BE NULL
                                    default ->
                                            isoCodes.stream().filter(ic -> "4000".equals(ic.getIsoCode())).findFirst().map(IsoCode::getId).orElse(null);
                                });
                            }
                        }

                        //GET REFERENTIAL TARE FOR EACH CONTAINER
                        detail.setTaraReferencial(isoCodes.stream().filter(ic -> ic.getId().equals(detail.getCodigoIsoId()) && Boolean.TRUE.equals(ic.getActive())).findFirst().map(IsoCode::getTare).orElse(null));

                        //GET DEFAULT REEFER TYPE FOR REEFER CONTAINERS
                        if (detail.getContenedor() != null && detail.getEsCntReefer()) {
                            detail.setCatTipoReeferId(containerRepository.fnGetDefaultReeferType(detail.getContenedor()));
                        }

                        detail.setLineaNavieraMemo(detail.getCntLineaNavieraId());

                        // MERGE LINES
                        if (Integer.valueOf(4106).equals(detail.getLineaNavieraMemo())) { // SEA -> MSL
                            detail.setLineaNavieraMemo(4104);
                        }

                        if (Integer.valueOf(4107).equals(detail.getLineaNavieraMemo()) || Integer.valueOf(4108).equals(detail.getLineaNavieraMemo())) { // ALI || CLI -> HSD
                            detail.setLineaNavieraMemo(4102);
                        }
                    }

                    //GET DEMURRAGE CONFIGURATION ID FOR EACH DETAIL
                    List<DemurrageReceptionSettingBU> demurrageReceptionSettingBUS = demurrageReceptionSettingBURepository.findAllByShippingLine(details.stream().filter(dt -> dt.getLineaNavieraMemo() != null).map(dt -> dt.getLineaNavieraMemo()).distinct().toList(), input.getSubUnidadNegocioId());
                    for (BlManualRegisterDetailDTO detail : details) {
                        detail.setSeteoRecepcionSobrestadiaId(demurrageReceptionSettingBUS.stream().filter(drs -> drs.getActive() && Optional.ofNullable(drs.getShippingLine()).map(ShippingLine::getId).orElse(0).equals(detail.getLineaNavieraMemo())).findFirst().map(DemurrageReceptionSettingBU::getId).orElse(null));
                    }

                    //AUTOMATIC INSERTION, ONLY IF IT'S THE FIRST TIME FOR THIS SUB BUSINESS UNIT, REQUIRES VALIDATION FROM IN THE UI
                    if (details.stream().anyMatch(dt -> dt.getSeteoRecepcionSobrestadiaId() == null)
                            && details.stream().noneMatch(dt -> demurrageReceptionSettingBUS.stream().anyMatch(drs -> Optional.ofNullable(drs.getShippingLine()).map(ShippingLine::getId).orElse(0).equals(dt.getLineaNavieraMemo())))) {
                        Integer catOrigenRecepcionId;
                        Integer seteoRecepcionSobreestadiaId;
                        String nameUn = businessUnitRepository.findById(input.getUnidadNegocioId()).map(BusinessUnit::getName).orElse(null);

                        Optional<DemurrageReceptionSetting> demurrageReceptionSetting = demurrageReceptionSettingRepository.findFirstByBusinessUnitIdAndActiveTrue(input.getUnidadNegocioId());
                        seteoRecepcionSobreestadiaId = demurrageReceptionSetting.map(DemurrageReceptionSetting::getId).orElse(null);
                        catOrigenRecepcionId = demurrageReceptionSetting.map(DemurrageReceptionSetting::getCatOrigenRecepcion).map(Catalog::getId).orElse(null);

                        if (seteoRecepcionSobreestadiaId == null) {
                            seteoRecepcionSobreestadiaId = demurrageReceptionSettingRepository.save(
                                    DemurrageReceptionSetting.builder()
                                            .catOrigenRecepcion(Catalog.builder().id(catalogIds.get(CATALOG_RECEPTION_ORIGIN_VARIOUS)).build())
                                            .ftpWayPassive(false)
                                            .active(true)
                                            .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                            .registrationDate(LocalDateTime.now())
                                            .sobrestadiaDescription("Carga de fecha sobrestadía - Origen Varios - " + nameUn)
                                            .catOrigenCreacionSobest(Catalog.builder().id(catalogIds.get(CATALOG_BL_AUTOMATIC_CREATION_ORIGIN)).build())
                                            .businessUnit(businessUnitClass)
                                            .build()
                            ).getId();
                        }

                        if (seteoRecepcionSobreestadiaId != null && !catalogIds.get(CATALOG_RECEPTION_ORIGIN_VARIOUS).equals(catOrigenRecepcionId)) {
                            seteoRecepcionSobreestadiaId = null;
                        } else {
                            demurrageReceptionSettingRepository.flush();
                        }

                        if (seteoRecepcionSobreestadiaId != null) {
                            List<DemurrageReceptionSettingBU> demurragesToSave = new ArrayList<>();
                            for (Integer sl : details.stream().filter(dt -> dt.getLineaNavieraMemo() != null).map(BlManualRegisterDetailDTO::getLineaNavieraMemo).toList()) {
                                demurragesToSave.add(DemurrageReceptionSettingBU.builder()
                                        .seteoRecepcionSobreestadia(DemurrageReceptionSetting.builder().id(seteoRecepcionSobreestadiaId).build())
                                        .subBusinessUnit(BusinessUnit.builder().id(input.getSubUnidadNegocioId()).build())
                                        .active(true)
                                        .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                        .registrationDate(LocalDateTime.now())
                                        .shippingLine(ShippingLine.builder().id(sl).build())
                                        .isRestrictiveOpe(false)
                                        .isRestrictiveBilling(false)
                                        .build());
                            }
                            demurrageReceptionSettingBURepository.saveAll(demurragesToSave);
                            demurrageReceptionSettingRepository.flush();

                            List<DemurrageReceptionSettingBU> newDemurrageReceptionSettingBUS = demurrageReceptionSettingBURepository.findAllByShippingLine(details.stream().filter(dt -> dt.getLineaNavieraMemo() != null).map(dt -> dt.getLineaNavieraMemo()).distinct().toList(), input.getSubUnidadNegocioId());
                            details.forEach(detail ->
                                    detail.setSeteoRecepcionSobrestadiaId(newDemurrageReceptionSettingBUS.stream().filter(drs -> drs.getActive() && Optional.ofNullable(drs.getShippingLine()).map(ShippingLine::getId).orElse(0).equals(detail.getLineaNavieraMemo())).findFirst().map(DemurrageReceptionSettingBU::getId).orElse(null))
                            );
                        }
                    }

                    if (details.stream().noneMatch(dt -> dt.getDocumentoCargaDetalleId() == null)) {
                        output.setRespMensaje(messageLanguageService.getMessage(blLoadError, 6, input.getIdiomaId()));
                    } else {
                        //IF THE CONTAINER EXISTS AND THE BUSINESS UNIT CORRESPONDS TO ECUADOR, UPDATE THE RECORDS
                        if ((Integer.valueOf(28).equals(input.getUnidadNegocioId()) ||
                                Integer.valueOf(29).equals(input.getUnidadNegocioId()) ||
                                Integer.valueOf(30).equals(input.getUnidadNegocioId()))
                                && details.stream().anyMatch(dt -> dt.getContenedorId() != null)
                        ) {
                            for (BlManualRegisterDetailDTO detail : details) {
                                Optional<Container> currentContainer = containers.stream().filter(cnt -> cnt.getContainerNumber().equals(detail.getContenedor())).findFirst();
                                if (currentContainer.isPresent()) {
                                    Container cnt = currentContainer.get();
                                    cnt.setShippingLine(detail.getCntLineaNavieraId() != null && !detail.getCntLineaNavieraId().equals(Optional.ofNullable(cnt.getShippingLine()).map(ShippingLine::getId).orElse(0)) ? ShippingLine.builder().id(detail.getCntLineaNavieraId()).build() : cnt.getShippingLine());
                                    cnt.setTraceContainer(detail.getCntLineaNavieraId() != null && !detail.getCntLineaNavieraId().equals(Optional.ofNullable(cnt.getShippingLine()).map(ShippingLine::getId).orElse(0)) ? "CREAR-BL-01" : cnt.getTraceContainer());
                                    cnt.setCatSize(detail.getCatTamanoId() != null && !detail.getCatTamanoId().equals(cnt.getCatSize().getId()) ? Catalog.builder().id(detail.getCatTamanoId()).build() : cnt.getCatSize());
                                    cnt.setTraceContainer(detail.getCatTamanoId() != null && !detail.getCatTamanoId().equals(cnt.getCatSize().getId()) ? "CREAR-BL-02" : cnt.getTraceContainer());
                                    cnt.setCatContainerType(detail.getCatTipoContenedorId() != null && !detail.getCatTipoContenedorId().equals(cnt.getCatContainerType().getId()) ? Catalog.builder().id(detail.getCatTipoContenedorId()).build() : cnt.getCatContainerType());
                                    cnt.setTraceContainer(detail.getCatTipoContenedorId() != null && !detail.getCatTipoContenedorId().equals(cnt.getCatContainerType().getId()) ? "CREAR-BL-03" : cnt.getTraceContainer());
                                    cnt.setModificationUser(User.builder().id(input.getUsuarioRegistroId()).build());
                                    cnt.setModificationDate(LocalDateTime.now());
                                    containerRepository.save(cnt);
                                }
                            }
                        }

                        //UPDATE ISO FOR THE CONTAINERS THAT DON'T HAVE A REGISTERED VALUE
                        if (details.stream().anyMatch(dt -> dt.getContenedorId() != null)) {
                            List<Container> finalContainers = containers;
                            for (BlManualRegisterDetailDTO detail : details.stream()
                                    .filter(dt -> dt.getCodigoIsoId() != null
                                            && finalContainers.stream().anyMatch(cnt -> dt.getContenedor().equalsIgnoreCase(cnt.getContainerNumber())
                                            && cnt.getIsoCode() == null)).toList()) {
                                Optional<Container> currentContainer = containers.stream().filter(cnt -> cnt.getContainerNumber().equals(detail.getContenedor())).findFirst();
                                if (currentContainer.isPresent()) {
                                    Container cnt = currentContainer.get();
                                    cnt.setIsoCode(IsoCode.builder().id(detail.getCodigoIsoId()).build());
                                    cnt.setModificationUser(User.builder().id(input.getUsuarioRegistroId()).build());
                                    cnt.setModificationDate(LocalDateTime.now());
                                    cnt.setTraceContainer("CREAR-BL-04");
                                    containerRepository.save(cnt);
                                }
                            }
                        }

                        //CREATE CONTAINERS THAT DON'T EXIST
                        for (BlManualRegisterDetailDTO detail : details) {
                            if (detail.getContenedorId() == null && detail.getContenedor() != null) {
                                Integer cntId = containerRepository.save(
                                        Container.builder()
                                                .containerNumber(detail.getContenedor())
                                                .catFamily(detail.getCntFamiliaId() != null ?
                                                        Catalog.builder().id(detail.getCntFamiliaId()).build() : null)
                                                .catSize(detail.getCatTamanoId() != null ?
                                                        Catalog.builder().id(detail.getCatTamanoId()).build() : null)
                                                .catContainerType(detail.getCatTipoContenedorId() != null ?
                                                        Catalog.builder().id(detail.getCatTipoContenedorId()).build() : null)
                                                .shippingLine(detail.getCntLineaNavieraId() != null ?
                                                        ShippingLine.builder().id(detail.getCntLineaNavieraId()).build() : null)
                                                .containerTare(detail.getTara() == null ? detail.getTaraReferencial() : detail.getTara())
                                                .maximunPayload(0)
                                                .isoCode(detail.getCodigoIsoId() != null ?
                                                        IsoCode.builder().id(detail.getCodigoIsoId()).build() : null)
                                                .catGrade(null)
                                                .catReeferType(detail.getCatTipoReeferId() == null ? null :
                                                        Catalog.builder().id(detail.getCatTipoReeferId()).build())
                                                .catEngineBrand(null)
                                                .manufactureDate(null)
                                                .shipperOwn(false)
                                                .active(true)
                                                .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                                .registrationDate(LocalDateTime.now())
                                                .traceContainer("CREAR-BL-05")
                                                .forSale(false)
                                                .build()
                                ).getId();
                                detail.setContenedorId(cntId);  // Set the generated Container ID into the detail
                            }
                        }
                        containerRepository.flush();

                        //  cargoDocumentRepository.flush(); // Uncomment if necessary to flush before the next operation

                        // CREATE THE CARGO DOCUMENTS THAT DON'T EXIST
                        if (details.stream().anyMatch(dt -> dt.getDocumentoCargaId() == null)) {
                            for (BlManualRegisterDetailDTO detail : details.stream().filter(dt -> dt.getDocumentoCargaId() == null)
                                    .filter(BlManualRegisterDetailDTO.distinctByKey(item -> List.of(
                                            Optional.ofNullable(item.getProgramacionNaveDetalleId()).orElse(1),
                                            item.getBl().toUpperCase(),
                                            Optional.ofNullable(item.getCntLineaNavieraId()).orElse(1))))
                                    .toList()) {
                                // Save CargoDocument and ensure it is persisted before setting into CargoDocumentDetail

                                CargoDocument cargoDocument = cargoDocumentRepository.save(
                                        CargoDocument.builder()
                                                .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(detail.getProgramacionNaveDetalleId()).build())
                                                .catCargoOrigin(Catalog.builder().id(catalogIds.get(CATALOG_PRIMARY_CARGO_ORIGIN)).build())
                                                .catTransportRoute(Catalog.builder().id(catalogIds.get(CATALOG_SEAWAY_TRANSPORT)).build())
                                                .cargoDocument(detail.getBl().toUpperCase())
                                                .isBroken(false)
                                                .originPort(Optional.ofNullable(input.getPuertoEmbarqueId())
                                                        .map(id -> Port.builder().id(id).build())
                                                        .orElse(null))
                                                .loadingPort(Port.builder().id(input.getPuertoEmbarqueId()).build())
                                                .dischargePort(Port.builder().id(input.getPuertoDescargaId()).build())
                                                .destinationPort(Optional.ofNullable(input.getPuertoDescargaId())
                                                        .map(id -> Port.builder().id(id).build())
                                                        .orElse(null))
                                                .shippingLine(Optional.ofNullable(detail.getCntLineaNavieraId())
                                                        .map(id -> ShippingLine.builder().id(id).build())
                                                        .orElse(null))
                                                .depot(Optional.ofNullable(input.getDepositoVacioId())
                                                        .map(id -> Depot.builder().id(id).build())
                                                        .orElse(null))
                                                .shipperCompany(Optional.ofNullable(input.getEmpresaEmbarcadorId())
                                                        .map(id -> Company.builder().id(id).build())
                                                        .orElse(null))
                                                .consigneeCompany(Optional.ofNullable(input.getEmpresaConsignatarioId())
                                                        .map(id -> Company.builder().id(id).build())
                                                        .orElse(null))
                                                .notifierCompany(Optional.ofNullable(notificanteId)
                                                        .map(id -> Company.builder().id(id).build())
                                                        .orElse(null))
                                                .shipperDetail(input.getEmbarcadorDetalle())
                                                .consigneeDetail(input.getConsignatarioDetalle())
                                                .notifierDetail(notificanteDetalle)
                                                .catDocumentCargoStatus(Catalog.builder().id(catalogIds.get(IS_DOCUMENT_ACTIVE)).build())
                                                .active(true)
                                                .catOriginCreation(Catalog.builder().id(catalogIds.get(CATALOG_BL_MANUAL_CREATION_ORIGIN)).build())
                                                .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                                .registrationDate(LocalDateTime.now())
                                                .catMoveType(Optional.ofNullable(input.getCatMoveTypeId())
                                                        .map(id -> catalogRepository.getReferenceById(id))
                                                        .orElse(null))
                                                .maerskDepotWithSd1("1".equals(input.getMaerskDepotWithSd1()))
                                                .originDestinationDepot(Optional.ofNullable(input.getOriginDestinationDepotId())
                                                        .map(id -> Depot.builder().id(id).build())
                                                        .orElse(null))
                                                .build()
                                );

                                cargoDocumentRepository.flush(); // Ensure changes are committed before proceeding
                            }

                            // ubicar los NUEVOS id's de la cabecera: BL es unico x PRO.NAVE y LOCACION (sub_unidad_negocio)
                            List<String> uniqueUppercaseBlNumbers = details.stream()
                                    .filter(dt -> dt.getDocumentoCargaId() == null)
                                    .map(BlManualRegisterDetailDTO::getBl)
                                    .filter(Objects::nonNull)
                                    .map(String::toUpperCase)
                                    .distinct()
                                    .toList();

                            List<CargoDocument> cargoDocuments = cargoDocumentRepository.findByCargoDocumentsList(
                                    uniqueUppercaseBlNumbers,
                                    programacionNaveId,
                                    businessUnitClass,
                                    BusinessUnit.builder().id(input.getSubUnidadNegocioId()).build());

                            List<VesselProgrammingDetail> vesselProgrammingDetails = vesselProgrammingDetailRepository.findAllById(
                                    cargoDocuments.stream()
                                            .filter(cd -> cd.getVesselProgrammingDetail() != null && cd.getVesselProgrammingDetail().getId() != null)
                                            .map(CargoDocument::getVesselProgrammingDetail)
                                            .map(VesselProgrammingDetail::getId)
                                            .distinct().toList()
                            );

                            List<Catalog> catalogList = catalogRepository.findAllById(
                                    vesselProgrammingDetails.stream()
                                            .filter(vpd -> vpd.getCatOperation() != null && vpd.getCatOperation().getId() != null)
                                            .map(VesselProgrammingDetail::getCatOperation)
                                            .map(Catalog::getId)
                                            .distinct().toList()
                            );

                            for (BlManualRegisterDetailDTO detail : details) {
                                if (detail.getDocumentoCargaId() == null) {
                                    Optional<CargoDocument> currentCargoDocument =
                                            cargoDocuments.stream()
                                                    .filter(cd -> cd.getCargoDocument().equalsIgnoreCase(detail.getBl()))
                                                    .filter(cd ->
                                                            vesselProgrammingDetails.stream()
                                                                    .anyMatch(vpd -> vpd.getId().equals(Optional.ofNullable(cd).map(CargoDocument::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(0)))
                                                    )
                                                    .filter(cd -> {
                                                        Optional<VesselProgrammingDetail> ve = vesselProgrammingDetails.stream()
                                                                .filter(vpd -> vpd.getId().equals(Optional.ofNullable(cd).map(CargoDocument::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(0)))
                                                                .findFirst();
                                                        return catalogList.stream()
                                                                .filter(cat -> cat.getId().equals(ve.map(VesselProgrammingDetail::getCatOperation).map(Catalog::getId).orElse(0)))
                                                                .filter(cat -> cat.getId().equals(detail.getCatOperacionId()))
                                                                .findFirst().isPresent();
                                                    })
                                                    .findFirst();
                                    //vesselProgrammingDetailRepository.findById(currentCargoDocument.map(CargoDocument::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(0));
                                    /*Optional<Catalog> currentOperationCatalog =
                                            catalogList.stream()
                                                    .filter(cat -> cat.getId().equals(currentVesselProgrammingDetail.map(VesselProgrammingDetail::getCatOperation).map(Catalog::getId).orElse(0)))
                                                    .filter(cat -> cat.getId().equals(detail.getCatOperacionId()))
                                                    .findFirst();*/
                                    if (currentCargoDocument.isPresent()) {
                                        CargoDocument cd = currentCargoDocument.get();
                                        detail.setDocumentoCargaId(cd.getId());
                                        detail.setProgramacionNaveDetalleId(cd.getVesselProgrammingDetail().getId());
                                    }
                                }
                            }
                        }

                        //CREATE THE CARGO DOCUMENT DETAILS THAT DON'T EXIST
                        if (details.stream().anyMatch(dt -> dt.getDocumentoCargaDetalleId() == null)) {
                            // Collect all details to save first
                            List<CargoDocumentDetail> detailsToSave = new ArrayList<>();

                            // Create all detail objects first
                            details.stream().filter(dt -> dt.getDocumentoCargaDetalleId() == null).forEach(dt -> {
                                detailsToSave.add(
                                        CargoDocumentDetail.builder()
                                                .cargoDocument(CargoDocument.builder().id(dt.getDocumentoCargaId()).build())
                                                .product(Product.builder().id(dt.getProductoId()).build())
                                                .container(Container.builder().id(dt.getContenedorId()).build())
                                                .catPackaging(Catalog.builder().id(dt.getCatEmbalajeId()).build())
                                                .manifestedQuantity(dt.getCantidad())
                                                .manifestedWeight(Optional.ofNullable(dt.getPesoNeto()).orElse(BigDecimal.ZERO))
                                                .manifestedVolume(Optional.ofNullable(dt.getVolumen()).orElse(BigDecimal.ZERO))
                                                .catWeightMeasureUnit(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                                                .saysContain(dt.getDiceContener())
                                                .isDangerousCargo(dt.getEsPeligroso())
                                                .isRefrigeratedCargo(dt.getEsRefrigerado())
                                                .brands(dt.getMarcasNumeros())
                                                .commodity(dt.getMercaderia())
                                                .manifestedChassis(null)
                                                .catCargoCondition(Catalog.builder().id(dt.getCatCondicionId()).build())
                                                .active(true)
                                                .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_BL_MANUAL_CREATION_ORIGIN)).build())
                                                .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                                .registrationDate(LocalDateTime.now())
                                                .catMeasureUnitQuantity(Catalog.builder().id(dt.getCatUnidadMedidaCantidadId()).build())
                                                .emptyGateoutSettled(false)
                                                .traceCargoDocumentDetail("CREAR-BL-06")
                                                .build()
                                );
                            });

                            // Save all details at once
                            cargoDocumentDetailRepository.saveAll(detailsToSave);
                            cargoDocumentDetailRepository.flush();
                        }

                        List<VesselProgrammingContainer> vesselProgrammingContainers = vesselProgrammingContainerRepository.findAllActiveByVesselProgrammingDetail(
                                details.stream().filter(dt -> dt.getProgramacionNaveDetalleId() != null)
                                        .map(BlManualRegisterDetailDTO::getProgramacionNaveDetalleId)
                                        .toList()
                        );

                        for (BlManualRegisterDetailDTO detail : details) {
                            Optional<VesselProgrammingContainer> currentVesselProgrammingContainer = vesselProgrammingContainers.stream().filter(vpc -> detail.getProgramacionNaveDetalleId().equals(Optional.ofNullable(vpc.getVesselProgrammingDetail()).map(VesselProgrammingDetail::getId).orElse(null))
                                    && detail.getContenedorId().equals(Optional.ofNullable(vpc.getContainer()).map(Container::getId).orElse(null))).findFirst();
                            if (currentVesselProgrammingContainer.isPresent()) {
                                detail.setProgramacionNaveContenedorId(currentVesselProgrammingContainer.get().getId());
                            }
                        }

                        //CREATE THE VESSSEL CONTAINER PROGRAMMING THAT DON'T EXIST
                        if (details.stream().anyMatch(dt -> dt.getProgramacionNaveContenedorId() == null)) {
                            for (BlManualRegisterDetailDTO dt : details) {
                                if (dt.getProgramacionNaveContenedorId() == null) {
                                    VesselProgrammingContainer newVesselProgrammingContainer = vesselProgrammingContainerRepository.save(
                                            VesselProgrammingContainer.builder()
                                                    .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(dt.getProgramacionNaveDetalleId()).build())
                                                    .container(Container.builder().id(dt.getContenedorId()).build())
                                                    .catManifestedContainerType(Catalog.builder().id(dt.getCatTipoContenedorId()).build())
                                                    .catManifestedSize(Catalog.builder().id(dt.getCatTamanoId()).build())
                                                    .manifestedTemperatureC(null)
                                                    .manifestedSeal1(dt.getPrecinto1())
                                                    .manifestedSeal2(dt.getPrecinto2())
                                                    .manifestedSeal3(dt.getPrecinto3())
                                                    .manifestedSeal4(dt.getPrecinto4())
                                                    .indirectDelivery(true)
                                                    .active(true)
                                                    .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_BL_MANUAL_CREATION_ORIGIN)).build())
                                                    .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                                    .registrationDate(LocalDateTime.now())
                                                    .isRefrigeratedCargo(dt.getEsRefrigerado())
                                                    .isDangerousCargo(dt.getEsPeligroso())
                                                    .emptyGateInSettled(false)
                                                    .traceProgVesCnt("CREAR-BL-07")
                                                    .demurrageDate(dt.getSeteoRecepcionSobrestadiaId() != null && dt.getFechaMemo() != null ?
                                                            LocalDate.parse(dt.getFechaMemo(), DateTimeFormatter.ofPattern(datePattern)).atStartOfDay() : null)
                                                    .build()
                                    );
                                    // UPDATE ID FOR THE DETAILS THAT DID NOT HAVE A VESSEL CONTAINER PROGGRAMING ID
                                    dt.setProgramacionNaveContenedorId(newVesselProgrammingContainer.getId());
                                }
                            }
                            vesselProgrammingContainerRepository.flush();
                        }

                        // SKOZE CREATE prog_nave_cont_imo register
                        for (BlManualRegisterDetailDTO detail : details.stream()
                                .filter(dt -> dt.getImoCheckList() != null && !dt.getImoCheckList().isEmpty())
                                .toList()) {

                            for (BlManualRegisterImoDto imo : detail.getImoCheckList()) {
                                // Fetch the Imo entity
                                Imo imoClass = imoRepository.findById(imo.getImoId())
                                        .orElseThrow(() -> new RuntimeException("IMO not found: " + imo.getImoId()));

                                // Create composite key
                                ProgrammingShipContainerImoId id = new ProgrammingShipContainerImoId(
                                        detail.getProgramacionNaveContenedorId(),  // VesselProgrammingContainer ID
                                        imo.getImoId()  // IMO ID
                                );

                                // Create VesselProgrammingContainerImo entity
                                VesselProgrammingContainerImo containerImo = new VesselProgrammingContainerImo();
                                containerImo.setId(id);

                                // Set VesselProgrammingContainer
                                VesselProgrammingContainer vpc = new VesselProgrammingContainer();
                                vpc.setId(detail.getProgramacionNaveContenedorId());
                                containerImo.setVesselProgrammingContainer(vpc);

                                // Set IMO entity
                                containerImo.setImo(imoClass);

                                // Set active flag
                                containerImo.setActive(true);

                                // Fetch and set the registration user
                                User user = userRepository.findById(input.getUsuarioRegistroId())
                                        .orElseThrow(() -> new RuntimeException("User not found: " + input.getUsuarioRegistroId()));
                                containerImo.setRegistrationUser(user);

                                // Set registration date
                                containerImo.setRegistrationDate(LocalDateTime.now());

                                // Save the entity
                                vesselProgrammingContainerImoRepository.save(containerImo);
                            }

                        }

                        //DEMURRAGE DATE INSERTION
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                        if (details.stream().anyMatch(dt -> dt.getFechaMemo() != null)) {
                            List<DemurrageReception> demurrageReceptions = demurrageReceptionRepository
                                    .findAllBydemurrageReceptionSetting(details.stream()
                                            .filter(dt -> dt.getSeteoRecepcionSobrestadiaId() != null)
                                            .map(BlManualRegisterDetailDTO::getSeteoRecepcionSobrestadiaId)
                                            .distinct().toList());
                            for (BlManualRegisterDetailDTO dt : details) {
                                if (dt.getFechaMemo() != null) {
                                    Optional<DemurrageReception> currentDemurrageReception = demurrageReceptions.stream()
                                            .filter(dr -> Optional.ofNullable(dr.getSeteoRecepcionSobrestadia()).map(DemurrageReceptionSetting::getId).orElse(0).equals(dt.getSeteoRecepcionSobrestadiaId()))
                                            .filter(dr -> Optional.ofNullable(dr.getVesselProgrammingDetail()).map(VesselProgrammingDetail::getId).orElse(0).equals(dt.getProgramacionNaveDetalleId()))
                                            .filter(dr -> Optional.ofNullable(dr.getContainer()).map(Container::getId).orElse(0).equals(dt.getContenedorId()))
                                            .filter(dr -> dr.getActive()).findFirst();
                                    if (currentDemurrageReception.isEmpty()) {
                                        demurrageReceptionRepository.save(
                                                DemurrageReception.builder()
                                                        .registrationDate(LocalDateTime.now())
                                                        .dateSobretadia(LocalDate.parse(dt.getFechaMemo(), formatter).atStartOfDay()) // ✅ Fixed parsing
                                                        .subBusinessUnit(BusinessUnit.builder().id(input.getSubUnidadNegocioId()).build())
                                                        .container(Container.builder().id(dt.getContenedorId()).build())
                                                        .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(dt.getProgramacionNaveDetalleId()).build())
                                                        .coreorBl(dt.getBl())
                                                        .seteoRecepcionSobrestadia(DemurrageReceptionSetting.builder().id(dt.getSeteoRecepcionSobrestadiaId()).build())
                                                        .active(true)
                                                        .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                                        .coreorContainer(dt.getContenedor())
                                                        .catOrigenCreacionSobrestadia(Catalog.builder().id(catalogIds.get(CATALOG_CREATION_FROM_REGISTER_BL)).build())
                                                        .observationProcess("Fecha Sobrestadía cargada del \"Crear BL\".")
                                                        .sobrestadiaConformable(true)
                                                        .build()
                                        );
                                    }
                                }
                            }

                            //UPDATE DEMURRAGE DATE FOR CONTAINERS, IT'LL AFFECT ALREADY REGISTERED CONTAINERS
                            List<VesselProgrammingContainer> demurrageVesselProgrammingContainers = vesselProgrammingContainerRepository.findAllById(details.stream().filter(dt -> dt.getProgramacionNaveContenedorId() != null).map(BlManualRegisterDetailDTO::getProgramacionNaveContenedorId).toList());
                            for (BlManualRegisterDetailDTO dt : details) {
                                Optional<VesselProgrammingContainer> currentVPC = demurrageVesselProgrammingContainers
                                        .stream()
                                        .filter(vpc -> vpc.getId().equals(dt.getProgramacionNaveContenedorId()))
                                        .filter((vpc -> vpc.getDemurrageDate() == null || vpc.getDemurrageDate().format(formatter).equals(dt.getFechaMemo())))
                                        .findFirst();
                                if (currentVPC.isPresent()) {
                                    VesselProgrammingContainer currentVesselProgrammingContainer = currentVPC.get();
                                    currentVesselProgrammingContainer.setDemurrageDate(
                                            LocalDate.parse(dt.getFechaMemo(), formatter).atStartOfDay()
                                    );
                                    currentVesselProgrammingContainer.setModificationUser(User.builder().id(input.getUsuarioRegistroId()).build());
                                    currentVesselProgrammingContainer.setModificationDate(LocalDateTime.now().with(LocalTime.MIDNIGHT));
                                    currentVesselProgrammingContainer.setTraceProgVesCnt("CREAR-BL-08");
                                    vesselProgrammingContainerRepository.save(currentVesselProgrammingContainer);
                                }
                            }
                            /*
                            //MUST FOLLOW THE SP STRUCTURE
                            details.stream().filter(dt -> dt.getFechaMemo() != null).forEach(dt -> {
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

                                Optional<DemurrageReceptionSetting> existingSetting = Optional.empty();

                                // Check if the seteo_recepcion_sobrestadia_id exists in DemurrageReceptionSetting
                                if (existingSetting.isEmpty()) {
                                    // Insert into DemurrageReceptionSetting if it does not exist
                                    DemurrageReceptionSetting newSetting = demurrageReceptionSettingRepository.save(
                                            DemurrageReceptionSetting.builder()
                                                    .id(dt.getSeteoRecepcionSobrestadiaId())
                                                    .active(true)
                                                    .ftpWayPassive(false)
                                                    .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                                    .registrationDate(LocalDateTime.now())
                                                    .catOrigenCreacionSobest(Catalog.builder().id(catalogIds.get(CATALOG_BL_AUTOMATIC_CREATION_ORIGIN)).build())
                                                    .businessUnit(businessUnitClass)
                                                    .build()
                                    );
                                    dt.setSeteoRecepcionSobrestadiaId(newSetting.getId());
                                }

                                if (demurrageReceptions.stream().noneMatch(dr ->
                                        dr.getSeteoRecepcionSobrestadia().getId().equals(dt.getSeteoRecepcionSobrestadiaId()) &&
                                                dr.getVesselProgrammingDetail().getId().equals(dt.getProgramacionNaveDetalleId()) &&
                                                dr.getContainer().getId().equals(dt.getContenedorId()))) {


                                    demurrageReceptionRepository.save(
                                            DemurrageReception.builder()
                                                    .registrationDate(LocalDateTime.now())
                                                    .dateSobretadia(LocalDate.parse(dt.getFechaMemo(), formatter).atStartOfDay()) // ✅ Fixed parsing
                                                    .subBusinessUnit(BusinessUnit.builder().id(input.getSubUnidadNegocioId()).build())
                                                    .container(Container.builder().id(dt.getContenedorId()).build())
                                                    .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(dt.getProgramacionNaveDetalleId()).build())
                                                    .coreorBl(dt.getBl())
                                                    .seteoRecepcionSobrestadia(DemurrageReceptionSetting.builder().id(dt.getSeteoRecepcionSobrestadiaId()).build())
                                                    .active(true)
                                                    .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                                    .coreorContainer(dt.getContenedor())
                                                    .catOrigenCreacionSobrestadia(Catalog.builder().id(catalogIds.get(CATALOG_CREATION_FROM_REGISTER_BL)).build())
                                                    .observationProcess("Fecha Sobrestadía cargada del \"Crear BL\".")
                                                    .sobrestadiaConformable(true)
                                                    .build()
                                    );
                                }

                            });*/


                        }

                        output.setRespEstado(1);
                        output.setRespMensaje(messageLanguageService.getMessage(blLoadError, 24, input.getIdiomaId()));
                    }
                } else {
                    Integer pDocumentLoadId = cargoDocumentRepository.findByCargoDocumentsList(
                            List.of(input.getNumeroBl()),
                            programacionNaveId,
                            businessUnitClass,
                            subBusinessUnitClass).stream().findFirst().map(CargoDocument::getId).orElse(null);
                    if (pDocumentLoadId != null) {
                        CargoDocument cargoDocument = cargoDocumentRepository.findByCargoDocumentsList(List.of(input.getNumeroBl()),
                                programacionNaveId,
                                businessUnitClass,
                                subBusinessUnitClass).stream().findFirst().get();
                        cargoDocument.setVesselProgrammingDetail(VesselProgrammingDetail.builder().id(input.getProgramacionNaveDetalleId()).build());
                        cargoDocument.setOriginPort(Optional.ofNullable(input.getPuertoEmbarqueId())
                                .map(id -> Port.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setLoadingPort(Port.builder().id(input.getPuertoEmbarqueId()).build());
                        cargoDocument.setDestinationPort(Optional.ofNullable(input.getPuertoDescargaId())
                                .map(id -> Port.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setDischargePort(Port.builder().id(input.getPuertoDescargaId()).build());
                        cargoDocument.setShippingLine(Optional.ofNullable(input.getLineaNavieraId())
                                .map(id -> ShippingLine.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setEmptyDepot(Optional.ofNullable(input.getDepositoVacioId())
                                .map(id -> Depot.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setShipperCompany(Optional.ofNullable(input.getEmpresaEmbarcadorId())
                                .map(id -> Company.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setConsigneeCompany(Optional.ofNullable(input.getEmpresaConsignatarioId())
                                .map(id -> Company.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setNotifierCompany(Optional.ofNullable(notificanteId)
                                .map(id -> Company.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setModificationUser(Optional.ofNullable(input.getUsuarioRegistroId())
                                .map(id -> User.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setModificationDate(LocalDateTime.now());
                        cargoDocument.setCatMoveType(Optional.ofNullable(input.getCatMoveTypeId())
                                .map(id -> Catalog.builder().id(id).build())
                                .orElse(null));
                        cargoDocument.setMaerskDepotWithSd1("1".equals(input.getMaerskDepotWithSd1()));
                        cargoDocument.setOriginDestinationDepot(Optional.ofNullable(input.getOriginDestinationDepotId())
                                .map(id -> Depot.builder().id(id).build())
                                .orElse(null));
                        cargoDocumentRepository.save(cargoDocument);

                    } else {

                        cargoDocumentRepository.save(
                                CargoDocument.builder()
                                        .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(input.getProgramacionNaveDetalleId()).build())
                                        .catCargoOrigin(Catalog.builder().id(catalogIds.get(CATALOG_PRIMARY_CARGO_ORIGIN)).build())
                                        .catTransportRoute(Catalog.builder().id(catalogIds.get(CATALOG_SEAWAY_TRANSPORT)).build())
                                        .cargoDocument(input.getNumeroBl().toUpperCase())
                                        .isBroken(false)
                                        .originPort(Optional.ofNullable(input.getPuertoEmbarqueId())
                                                .map(id -> Port.builder().id(id).build())
                                                .orElse(null))
                                        .loadingPort(Port.builder().id(input.getPuertoEmbarqueId()).build())
                                        .dischargePort(Port.builder().id(input.getPuertoDescargaId()).build())
                                        .destinationPort(Optional.ofNullable(input.getPuertoDescargaId())
                                                .map(id -> Port.builder().id(id).build())
                                                .orElse(null))
                                        .shippingLine(Optional.ofNullable(input.getLineaNavieraId())
                                                .map(id -> ShippingLine.builder().id(id).build())
                                                .orElse(null))
                                        .depot(Optional.ofNullable(input.getDepositoVacioId())
                                                .map(id -> Depot.builder().id(id).build())
                                                .orElse(null))
                                        .shipperCompany(Optional.ofNullable(input.getEmpresaEmbarcadorId())
                                                .map(id -> Company.builder().id(id).build())
                                                .orElse(null))
                                        .consigneeCompany(Optional.ofNullable(input.getEmpresaConsignatarioId())
                                                .map(id -> Company.builder().id(id).build())
                                                .orElse(null))
                                        .notifierCompany(Optional.ofNullable(notificanteId)
                                                .map(id -> Company.builder().id(id).build())
                                                .orElse(null))
                                        .shipperDetail(input.getEmbarcadorDetalle())
                                        .consigneeDetail(input.getConsignatarioDetalle())
                                        .notifierDetail(notificanteDetalle)
                                        .catDocumentCargoStatus(Catalog.builder().id(catalogIds.get(IS_DOCUMENT_ACTIVE)).build())
                                        .active(true)
                                        .catOriginCreation(Catalog.builder().id(catalogIds.get(CATALOG_BL_MANUAL_CREATION_ORIGIN)).build())
                                        .registrationUser(User.builder().id(input.getUsuarioRegistroId()).build())
                                        .registrationDate(LocalDateTime.now())
                                        .catMoveType(Optional.ofNullable(input.getCatMoveTypeId())
                                                .map(id -> Catalog.builder().id(id).build())
                                                .orElse(null))
                                        .maerskDepotWithSd1("1".equals(input.getMaerskDepotWithSd1()))
                                        .originDestinationDepot(Optional.ofNullable(input.getOriginDestinationDepotId())
                                                .map(id -> Depot.builder().id(id).build())
                                                .orElse(null))
                                        .build()
                        );

                    }

                    output.setRespEstado(1);
                    output.setRespMensaje(messageLanguageService.getMessage(blLoadError, 24, input.getIdiomaId()));
                }
            }
        }

        if (output.getRespMensaje() != null && !output.getRespMensaje().isBlank() && Integer.valueOf(0).equals(output.getRespEstado())) {
            output.setRespEstado(2);
        }

        return output;
    }

    private boolean validateDateString(String string) {
        try {
            LocalDate.parse(string, DateTimeFormatter.ofPattern(datePattern));
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}