package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.ges.dto.CurrencyBusinessDto;
import com.maersk.sd1.sds.controller.dto.ShippingLineOutput;
import com.maersk.sd1.seg.controller.dto.BusinessUnitListTempDTO;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class GetCatalogsByTablesOutput {

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("total_registros")
    private List<List<Long>> totalRecords;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("result_list")
    private List<ShippingLineOutput.ShippingLineResult> shippingLineResultList;

    @JsonProperty("companies")
    private List<CompanyData> companieList;

    @JsonProperty("total_records")
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    private List<List<Long>> totalRegistros;

    @JsonProperty("depots")
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    private List<DepositListOutput.DepotData> depots;

    @JsonProperty("total_prod_records")
    private List<List<Long>> totalProductRecords;

    @JsonProperty("products")
    private List<ProductListOutput.ProductDetailOutput> products;

    @JsonProperty("total_imo_records")
    private List<List<Integer>> totalImoRecords;

    @JsonProperty("imos")
    private List<ListImoDTO> data;

    @JsonProperty("totalRecords")
    private List<List<Long>> totalBusiRecords;

    @JsonProperty("businessUnits")
    private List<BusinessUnitListTempDTO> businessUnits;

    @JsonProperty("currencies")
    private List<CurrencyDTO.CurrencyData> currencyList;

    @JsonProperty("business_currency_list")
    private List<CurrencyBusinessDto.CurrencyBusiness> currencyBusinessesData;

    @JsonProperty("coparn_configuration")
    private List<CoparnConfigurationOutput.DataItem> coparnConfiguration;

    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class CompanyData{
        private Integer companyId;
        private String document;
        private String companyName;
    }
}