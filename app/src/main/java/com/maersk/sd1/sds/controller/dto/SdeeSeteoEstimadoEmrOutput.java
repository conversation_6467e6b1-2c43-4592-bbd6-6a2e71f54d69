package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * Output DTO that holds the total record count and the list of results.
 */
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class SdeeSeteoEstimadoEmrOutput {

    @JsonProperty("total_registros")
    private List<List<Long>> totalRegistros;

    @JsonProperty("detalle")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<SdeeSeteoEstimadoEmrDetail> detalle;

    /**
     * Nested DTO for each record in the result set.
     */
    @Data
    @AllArgsConstructor
    public static class SdeeSeteoEstimadoEmrDetail {

        @JsonProperty("seteo_estimado_emr_id")
        private Integer seteoEstimadoEmrId;

        @JsonProperty("unidad_negocio_id")
        private BigDecimal unidadNegocioId;

        @JsonProperty("sub_unidad_negocio_id")
        private BigDecimal subUnidadNegocioId;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("cat_modo_generar_archivo_esimado_id")
        private BigDecimal catModoGenerarArchivoEstId;

        @JsonProperty("descripcion_servicio")
        private String descripcionServicio;

        @JsonProperty("archivo_correlativo")
        private Integer archivoCorrelativo;

        @JsonProperty("archivo_extension")
        private String archivoExtension;

        @JsonProperty("correo_envio")
        private String correoEnvio;

        @JsonProperty("minutos_transcurridos")
        private Integer minutosTranscurridos;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro")
        private Timestamp fechaRegistro;

        @JsonProperty("usuario_registro")
        private BigDecimal usuarioRegistro;

        @JsonProperty("fecha_modificacion")
        private Timestamp fechaModificacion;

        @JsonProperty("shopcode_merc")
        private String shopcodeMerc;

        @JsonProperty("azure_id")
        private String azureId;

        @JsonProperty("usuario_id")
        private BigDecimal usuarioId;

        @JsonProperty("usuario_registro_nombres")
        private String usuarioRegistroNombres;

        @JsonProperty("usuario_registro_apellidos")
        private String usuarioRegistroApellidos;
    }
}
