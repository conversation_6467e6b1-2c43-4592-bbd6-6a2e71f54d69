package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.FleetEquipmentEdiSettingDTO;
import com.maersk.sd1.sds.dto.FleetSetEdiListInput;
import com.maersk.sd1.sds.dto.FleetSetEdiListOutput;
import com.maersk.sd1.common.repository.FleetEquipmentEdiSettingRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@RequiredArgsConstructor
@Service
public class FleetSetEdiListService {

    private static final Logger logger = LogManager.getLogger(FleetSetEdiListService.class);

    private final FleetEquipmentEdiSettingRepository fleetEquipmentEdiSettingRepository;
    private final CatalogRepository catalogRepository;

    @Transactional(readOnly = true)
    public FleetSetEdiListOutput getFleetSetEdiList(FleetSetEdiListInput.Input input) {
        logger.info("Executing getFleetSetEdiList with input: {}", input);
        FleetSetEdiListOutput output = new FleetSetEdiListOutput();

        List<FleetEquipmentEdiSettingDTO> fleetEquipmentEdiSettingDTOList = fleetEquipmentEdiSettingRepository.findFleetEquipmentEdiSettings(input.getBusinessUnitId(), input.getShippingLineId(), input.getStatusId(), input.getDateRegistrationMin(), input.getDateRegistrationMax(), input.getLanguageId());

        output.setTotal(List.of(List.of(fleetEquipmentEdiSettingDTOList.size())));

        int page = input.getPage();
        int size = input.getSize();
        int fromIndex = (page - 1) * size;
        int toIndex = Math.min(fromIndex + size, fleetEquipmentEdiSettingDTOList.size());

        fleetEquipmentEdiSettingDTOList= fleetEquipmentEdiSettingDTOList.subList(fromIndex, toIndex);

        List<FleetSetEdiListOutput.FleetSetEdiListRecord> recordList = fleetEquipmentEdiSettingDTOList.stream()
                .map(dto -> {
                    AtomicInteger keyCounter = new AtomicInteger(1);
                    FleetSetEdiListOutput.FleetSetEdiListRecord records = new FleetSetEdiListOutput.FleetSetEdiListRecord();
                    int key = keyCounter.getAndIncrement();
                    records.setKey(key);
                    records.setFleetEquipmentEdiSettingId(dto.getFleetEquipmentEdiSettingId());
                    records.setFleetEquipEdiDescription(dto.getFleetEquipEdiDescription());
                    records.setBusinessUnitId(dto.getBusinessUnitId());
                    records.setBusinessUnitName(dto.getBusinessUnit());
                    records.setShippingLineId(dto.getShippingLineId());
                    records.setShippingLineName(dto.getShippingLine());
                    records.setCatRecepModeFleetEquipSettingId(dto.getCatRecepModeFleetEquipSettingId());
                    records.setCatRecepModeFleetEquipSettingDesc(dto.getCatRecepModeFleetEquipSetting());
                    records.setFleetEquipEdiFileExtension(dto.getFleetEquipEdiFileExtension());
                    records.setAzureStorageFleetEquipEdiId(dto.getAzureStorageFleetEquipEdiId() != null ? dto.getAzureStorageFleetEquipEdiId() : null);
                    records.setAzureStorageFleetEquipEdiAliasId(dto.getAzureStorageFleetEquipEdiAliasId() != null ? dto.getAzureStorageFleetEquipEdiAliasId() : null);
                    records.setSftpConfigFleetEquipEdiId(dto.getSftpConfigFleetEquipEdiId() != null ? dto.getSftpConfigFleetEquipEdiId() : null);
                    records.setSftpConfigFleetEquipEdiAliasId(dto.getSftpConfigFleetEquipEdiAliasId() != null ? dto.getSftpConfigFleetEquipEdiAliasId() : null);
                    records.setStatus(dto.getActive());
                    records.setRegistrationDate(dto.getRegistrationDate());
                    records.setUserRegistrationId(dto.getUserRegistrationId());
                    records.setUserRegistrationNames(dto.getUserRegistrationNames());
                    records.setUserRegistrationLastName(dto.getUserRegistrationLastname());
                    records.setModificationDate(dto.getModificationDate());
                    records.setUserModificationId(dto.getUserModificationId());
                    records.setUserModificationNames(dto.getUserModificationNames());
                    records.setUserModificationLastName(dto.getUserModificationLastname());
                    return records;
                })
                .toList();

        output.setList(recordList);

        return output;
    }
}
