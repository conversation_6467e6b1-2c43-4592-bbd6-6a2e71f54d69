package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.dto.VesselProgrammingPortRegisterInput;
import com.maersk.sd1.sds.dto.VesselProgrammingPortRegisterOutput;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.service.VesselProgrammingPortRegisterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class VesselProgrammingPortRegisterController {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingPortRegisterController.class);

    private final VesselProgrammingPortRegisterService registerService;

    @Autowired
    public VesselProgrammingPortRegisterController(VesselProgrammingPortRegisterService registerService) {
        this.registerService = registerService;
    }

    @PostMapping("/sdsregistrarProgramacionNavePuerto")
    public ResponseEntity<ResponseController<VesselProgrammingPortRegisterOutput>> register(
            @RequestBody @Valid VesselProgrammingPortRegisterInput.Root request) {
        try {
            VesselProgrammingPortRegisterInput.Input input = request.getPrefix().getInput();

            VesselProgrammingPortRegisterOutput output = registerService.registerVesselProgrammingPort(
                    input.getVesselProgrammingId(),
                    input.getPortId(),
                    input.getRegistrationUserId(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            VesselProgrammingPortRegisterOutput output = new VesselProgrammingPortRegisterOutput();
            output.setResponseStatus(0);
            output.setResponseMessage(e.toString());
            output.setResponseNewId(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}