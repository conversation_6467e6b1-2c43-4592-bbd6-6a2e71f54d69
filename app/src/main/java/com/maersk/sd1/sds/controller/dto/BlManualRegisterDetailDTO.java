package com.maersk.sd1.sds.controller.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

@Data
public class BlManualRegisterDetailDTO {

    @SerializedName("contenedor")
    private String contenedor;

    @SerializedName("catalogo_tamano_id")
    private Integer catTamanoId;

    @SerializedName("catalogo_tipo_contenedor_id")
    private Integer catTipoContenedorId;

    @SerializedName("tara")
    private Integer tara;

    @SerializedName("catalogo_condicion_id")
    private Integer catCondicionId;

    @SerializedName("fecha_devolucion_vacio")
    private String fechaMemo;

    @SerializedName("MARCAS_NUMEROS")
    private String marcasNumeros;

    @SerializedName("MERCADERIA")
    private String mercaderia;

    @SerializedName("CANTIDAD")
    private BigDecimal cantidad;

    @SerializedName("VOLUMEN")
    private BigDecimal volumen;

    @SerializedName("DICE_CONTENER")
    private Integer diceContener;

    @SerializedName("PRECINTO1")
    private String precinto1;

    @SerializedName("PRECINTO2")
    private String precinto2;

    @SerializedName("PRECINTO3")
    private String precinto3;

    @SerializedName("PRECINTO4")
    private String precinto4;

    @SerializedName("PESO_NETO")
    private BigDecimal pesoNeto;

    @SerializedName("BL")
    private String bl;

    @SerializedName("cat_operacion_id")
    private Integer catOperacionId;

    @SerializedName("documento_carga_id")
    private Integer documentoCargaId;

    @SerializedName("programacion_nave_detalle_id")
    private Integer programacionNaveDetalleId;

    @SerializedName("cat_embalaje_id")
    private Integer catEmbalajeId;

    @SerializedName("cat_unidad_medida_cantidad_id")
    private Integer catUnidadMedidaCantidadId;

    @SerializedName("cnt_linea_naviera_id")
    private Integer cntLineaNavieraId;

    @SerializedName("es_cnt_reefer")
    private Boolean esCntReefer;

    @SerializedName("producto_id")
    private Integer productoId;

    @SerializedName("contenedor_id")
    private Integer contenedorId;

    @SerializedName("codigo_iso_id")
    private Integer codigoIsoId;

    @SerializedName("cnt_familia_id")
    private Integer cntFamiliaId;

    @SerializedName("es_peligroso")
    private Boolean esPeligroso;

    @SerializedName("es_refrigerado")
    private Boolean esRefrigerado;

    @SerializedName("documento_carga_detalle_id")
    private Integer documentoCargaDetalleId;

    @SerializedName("DIMENSION")
    private String dimension;

    @SerializedName("TIPOCNT")
    private String tipocnt;

    @SerializedName("programacion_nave_contenedor_id")
    private Integer programacionNaveContenedorId;

    @SerializedName("cat_tipo_reefer_id")
    private Integer catTipoReeferId;

    @SerializedName("tara_referencial")
    private Integer taraReferencial;

    @SerializedName("linea_naviera_memo")
    private Integer lineaNavieraMemo;

    @SerializedName("seteo_recepcion_sobrestadia_id")
    private Integer seteoRecepcionSobrestadiaId;

    @SerializedName("imo_checklist")
    private List<BlManualRegisterImoDto> imoCheckList;

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
}
