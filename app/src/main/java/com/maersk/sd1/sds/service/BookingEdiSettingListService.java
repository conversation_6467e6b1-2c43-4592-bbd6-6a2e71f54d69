package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BookingEdiSetting;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sds.dto.BookingEdiSettingListInputDTO;
import com.maersk.sd1.sds.dto.BookingEdiSettingListOutputDTO;
import com.maersk.sd1.sds.dto.BookingEdiSettingListOutputDTO.BookingEdiSettingRowDTO;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Root;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Service class for listing BookingEdiSetting data by dynamic filters,
 * translating the logic from the stored procedure [sds].[seteo_edi_coparn_listar]  into JPA criteria.
 */
@RequiredArgsConstructor
@Service
public class BookingEdiSettingListService {

    private static final Logger logger = LogManager.getLogger(BookingEdiSettingListService.class);

    private final BookingEdiSettingRepository bookingEdiSettingRepository;

    /**
     * Translates the parameters from the input DTO into a dynamic JPA specification,
     * applies pagination, and returns the total count plus the filtered list.
     */
    public BookingEdiSettingListOutputDTO listBookingEdiSettings(BookingEdiSettingListInputDTO.Input input) {
        try {
            // Manage pagination defaults:
            int pageNumber = getPageNumber(input);
            int pageSize = getPageSize(input);

            Specification<BookingEdiSetting> spec = buildSpecification(input);
            Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
            Page<BookingEdiSetting> pageResult = bookingEdiSettingRepository.findAll(spec, pageable);

            return buildOutput(pageResult);
        } catch (Exception e) {
            logger.error("Error listing BookingEdiSettings", e);
            return buildEmptyOutput();
        }
    }

    private int getPageNumber(BookingEdiSettingListInputDTO.Input input) {
        return (input.getPage() == null || input.getPage() < 1) ? 1 : input.getPage();
    }

    private int getPageSize(BookingEdiSettingListInputDTO.Input input) {
        return (input.getSize() == null || input.getSize() < 1) ? Integer.MAX_VALUE : input.getSize();
    }

    private BookingEdiSettingListOutputDTO buildOutput(Page<BookingEdiSetting> pageResult) {
        BookingEdiSettingListOutputDTO output = new BookingEdiSettingListOutputDTO();
        output.setTotalRegistros(List.of(List.of(pageResult.getTotalElements())));

        List<BookingEdiSettingRowDTO> rows = buildRows(pageResult);
        output.setResultados(rows);
        return output;
    }

    private BookingEdiSettingListOutputDTO buildEmptyOutput() {
        BookingEdiSettingListOutputDTO emptyOutput = new BookingEdiSettingListOutputDTO();
        emptyOutput.setTotalRegistros(List.of(List.of(0L)));
        emptyOutput.setResultados(new ArrayList<>());
        return emptyOutput;
    }

    private List<BookingEdiSettingRowDTO> buildRows(Page<BookingEdiSetting> pageResult) {
        List<BookingEdiSettingRowDTO> rows = new ArrayList<>();
        for (BookingEdiSetting b : pageResult.getContent()) {
            BookingEdiSettingRowDTO row = buildRow(b);
            rows.add(row);
        }
        return rows;
    }

    protected BookingEdiSettingRowDTO buildRow(BookingEdiSetting b) {
        BookingEdiSettingRowDTO row = new BookingEdiSettingRowDTO();
        row.setSeteoEdiCoparnId(b.getId());
        row.setLineaNavieraId(b.getShippingLine() == null ? null : b.getShippingLine().getId());
        row.setCatCanalRecepcionCoparnId(b.getCatCanalRecepcionCoparn() == null ? null : Long.valueOf(b.getCatCanalRecepcionCoparn().getId()));
        row.setCatModoProcesarCoparnId(b.getCatModoProcesarCoparn() == null ? null : Long.valueOf(b.getCatModoProcesarCoparn().getId()));
        row.setBkEdiDescription(b.getBkEdiDescription());
        row.setAzureId(b.getAzureId());
        row.setBkEdiSftpId(b.getBkEdiSftpId());
        row.setBkEdiFtpId(b.getBkEdiFtpId());
        row.setBkEdiFolderRoute(b.getBkEdiFolderRoute());
        row.setDownloadFileExtension(b.getDownloadFileExtension());
        row.setEdiMoveRoute(b.getEdi_move_route());
        row.setAllowCreateAutomaticVesselProgramming(b.getAllowCreateAutomaticVesselProgramming());
        row.setAllowCreateAutomaticCustomer(b.getAllowCreateAutomaticCustomer());
        row.setIsHistorical(b.getIsHistorical());
        row.setDeactivationDate(b.getDeactivationDate());
        row.setDeactivationReason(b.getDeactivationReason());
        row.setActive(b.getActive());
        row.setRegistrationDate(b.getRegistrationDate());
        row.setModificationDate(b.getModificationDate());
        row.setBusinessUnitId(b.getBusinessUnit() == null ? null : Long.valueOf(b.getBusinessUnit().getId()));

        setUserDetails(b, row);

        return row;
    }

    private void setUserDetails(BookingEdiSetting b, BookingEdiSettingRowDTO row) {
        setRegistrationUser(b, row);
        setModificationUser(b, row);
    }

    protected void setRegistrationUser(BookingEdiSetting b, BookingEdiSettingRowDTO row) {
        User regUser = b.getRegistrationUser();
        if (regUser != null) {
            row.setRegistrationUserId(regUser.getId());
            row.setRegistrationUserNames(regUser.getNames());
            String apellidos = getFullName(regUser);
            row.setRegistrationUserApellidos(apellidos.trim());
        }
    }

    protected void setModificationUser(BookingEdiSetting b, BookingEdiSettingRowDTO row) {
        User modUser = b.getModificationUser();
        if (modUser != null) {
            row.setModificationUserId(modUser.getId());
            row.setModificationUserNames(modUser.getNames());
            String apellidosMod = getFullName(modUser);
            row.setModificationUserApellidos(apellidosMod.trim());
        }
    }

    private String getFullName(User user) {
        return (user.getFirstLastName() != null ? user.getFirstLastName() : "") +
                (user.getSecondLastName() != null ? (" " + user.getSecondLastName()) : "");
    }

    /**
     * Builds a JPA Specification to emulate the dynamic WHERE clauses from the stored procedure.
     */
    private Specification<BookingEdiSetting> buildSpecification(BookingEdiSettingListInputDTO.Input input) {
        return (root, query, cb) -> {
            List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

            addPredicates(input, root, cb, predicates);

            return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
        };
    }

    public void addPredicates(BookingEdiSettingListInputDTO.Input input, Root<BookingEdiSetting> root, CriteriaBuilder cb, List<jakarta.persistence.criteria.Predicate> predicates) {
        if (input.getSeteoEdiCoparnId() != null) predicates.add(cb.equal(root.get("id"), input.getSeteoEdiCoparnId()));
        if (input.getLineaNavieraId() != null) predicates.add(cb.equal(root.get("shippingLine").get("id"), input.getLineaNavieraId()));
        if (input.getCatCanalRecepcionCoparnId() != null) predicates.add(cb.equal(root.get("catCanalRecepcionCoparn").get("id"), input.getCatCanalRecepcionCoparnId()));
        if (input.getCatModoProcesarCoparnId() != null) predicates.add(cb.equal(root.get("catModoProcesarCoparn").get("id"), input.getCatModoProcesarCoparnId()));
        if (input.getEdiCoparnDescripcion() != null && !input.getEdiCoparnDescripcion().isEmpty()) predicates.add(cb.like(cb.lower(root.get("bkEdiDescription")), "%" + input.getEdiCoparnDescripcion().toLowerCase() + "%"));
        if (input.getAzureId() != null && !input.getAzureId().isEmpty()) predicates.add(cb.like(cb.lower(root.get("azureId")), "%" + input.getAzureId().toLowerCase() + "%"));
        if (input.getSftpCoparnId() != null && !input.getSftpCoparnId().isEmpty()) predicates.add(cb.like(cb.lower(root.get("bkEdiSftpId")), "%" + input.getSftpCoparnId().toLowerCase() + "%"));
        if (input.getFtpCoparnId() != null && !input.getFtpCoparnId().isEmpty()) predicates.add(cb.like(cb.lower(root.get("bkEdiFtpId")), "%" + input.getFtpCoparnId().toLowerCase() + "%"));
        if (input.getCarpetaCoparnRuta() != null && !input.getCarpetaCoparnRuta().isEmpty()) predicates.add(cb.like(cb.lower(root.get("bkEdiFolderRoute")), "%" + input.getCarpetaCoparnRuta().toLowerCase() + "%"));
        if (input.getExtensionArchivoDescargar() != null && !input.getExtensionArchivoDescargar().isEmpty()) predicates.add(cb.like(cb.lower(root.get("downloadFileExtension")), "%" + input.getExtensionArchivoDescargar().toLowerCase() + "%"));
        if (input.getRutaMoverEdi() != null && !input.getRutaMoverEdi().isEmpty()) predicates.add(cb.like(cb.lower(root.get("edi_move_route")), "%" + input.getRutaMoverEdi().toLowerCase() + "%"));
        if (input.getPermitirCrearProgNaveAutomatico() != null) predicates.add(cb.equal(root.get("allowCreateAutomaticVesselProgramming"), input.getPermitirCrearProgNaveAutomatico()));
        if (input.getPermitirCrearClienteAutomatico() != null) predicates.add(cb.equal(root.get("allowCreateAutomaticCustomer"), input.getPermitirCrearClienteAutomatico()));
        if (input.getEsHistorico() != null) predicates.add(cb.equal(root.get("isHistorical"), input.getEsHistorico()));
        // fecha_debaja range
        if (input.getFechaDeBajaMin() != null && input.getFechaDeBajaMax() != null) {
            LocalDateTime from = input.getFechaDeBajaMin().atStartOfDay();
            LocalDateTime to = input.getFechaDeBajaMax().plusDays(1).atStartOfDay();
            predicates.add(cb.greaterThanOrEqualTo(root.get("deactivationDate"), from));
            predicates.add(cb.lessThan(root.get("deactivationDate"), to));
        }

        if (input.getMotivoDeBaja() != null && !input.getMotivoDeBaja().isEmpty()) predicates.add(cb.like(cb.lower(root.get("deactivationReason")), "%" + input.getMotivoDeBaja().toLowerCase() + "%"));
        if (input.getActivo() != null) predicates.add(cb.equal(root.get("active"), input.getActivo()));
        // fecha_registro range (updated logic)
        if (input.getFechaRegistroMin() != null && input.getFechaRegistroMax() != null) {
            // Convert LocalDate to LocalDateTime (start of the day)
            LocalDateTime from = input.getFechaRegistroMin().atStartOfDay(); // Start of the day
            LocalDateTime to = input.getFechaRegistroMax().atStartOfDay().plusDays(1); // Start of next day (end of the range)

            // Add the predicates for filtering the date range
            predicates.add(cb.greaterThanOrEqualTo(root.get("registrationDate"), from));
            predicates.add(cb.lessThan(root.get("registrationDate"), to));
        }

        // fecha_modificacion range (updated logic)
        if (input.getFechaModificacionMin() != null && input.getFechaModificacionMax() != null) {
            // Convert LocalDate to LocalDateTime (start of the day)
            LocalDateTime from = input.getFechaModificacionMin().atStartOfDay(); // Start of the day
            LocalDateTime to = input.getFechaModificacionMax().atStartOfDay().plusDays(1); // Start of next day (end of the range)

            // Add the predicates for filtering the date range
            predicates.add(cb.greaterThanOrEqualTo(root.get("modificationDate"), from));
            predicates.add(cb.lessThan(root.get("modificationDate"), to));
        }

        if (input.getUnidadNegocioId() != null) predicates.add(cb.equal(root.get("businessUnit").get("id"), input.getUnidadNegocioId()));
    }

}
