package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.BookingDetail;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListInput;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListOutput;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListOutput.BookingDetailItem;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListOutput.BookingItem;
import com.maersk.sd1.sde.dto.PreAssignmentListProcessDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class PreAssignmentListService {

    private static final Logger logger = LogManager.getLogger(PreAssignmentListService.class);

    private final BookingRepository bookingRepository;
    private final BookingDetailRepository bookingDetailRepository;
    private final CatalogRepository catalogRepository;

    public PreAssignmentListService(BookingRepository bookingRepository, BookingDetailRepository bookingDetailRepository, CatalogRepository catalogRepository) {
        this.bookingRepository = bookingRepository;
        this.bookingDetailRepository = bookingDetailRepository;
        this.catalogRepository = catalogRepository;
    }

    @Transactional
    public PreAssignmentListOutput preAssignmentList(PreAssignmentListInput.Input input) {
        PreAssignmentListOutput output = new PreAssignmentListOutput();
        output.setRespStatus(1);
        output.setRespMessage("OK");

        try {
            LocalDateTime[] dateRange = getDateRange(input);
            Catalog isDocumentActive = catalogRepository.findByAlias("43061");
            Catalog isCreationPreAssig = catalogRepository.findByAlias("sd1_creationsource_pre_assig");

            handleBookingApproval(input, isDocumentActive, isCreationPreAssig);

            Page<Booking> pageResult = getPageResult(input, dateRange);

            long totalRegistros = pageResult.getTotalElements();
            List<BookingItem> bookingItemList = getBookingItems(pageResult);

            output.setTotalRegisters(totalRegistros);
            output.setBookings(bookingItemList);

        } catch (Exception e) {
            logger.error("Error in preAssignmentList", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
        }

        return output;
    }

    private LocalDateTime[] getDateRange(PreAssignmentListInput.Input input) {
        LocalDate dateMin = input.getBookingIssueDateMin() != null ? input.getBookingIssueDateMin() : LocalDate.of(2021, 11, 1);
        LocalDate dateMax = input.getBookingIssueDateMax() != null ? input.getBookingIssueDateMax() : LocalDate.now().plusMonths(1);
        return new LocalDateTime[]{dateMin.atStartOfDay(), dateMax.atTime(23, 59, 59)};
    }

    private void handleBookingApproval(PreAssignmentListInput.Input input, Catalog isDocumentActive, Catalog isCreationPreAssig) {
        if ((input.getBookingNumber() != null && !input.getBookingNumber().isEmpty()) || (input.getBookingId() != null && input.getBookingId() > 0)) {
            Optional<Booking> foundBookingOptional = bookingRepository.findBookingToApprove(
                    input.getSubBusinessUnitId(),
                    input.getBookingId(),
                    input.getBookingNumber(),
                    isDocumentActive.getId()
            );
            Booking foundBooking = foundBookingOptional.orElse(null);
            if (foundBooking != null && !foundBooking.getApprovedBooking()) {
                foundBooking.setApprovedBooking(true);
                foundBooking.setApprovedBookingDate(LocalDateTime.now());
                foundBooking.setApprovedUser(null);
                foundBooking.setCatOriginBookingCreation(isCreationPreAssig);
                bookingRepository.save(foundBooking);
            }
        }
    }

    private Page<Booking> getPageResult(PreAssignmentListInput.Input input, LocalDateTime[] dateRange) {
        int page = (input.getPage() == null || input.getPage() <= 0) ? 1 : input.getPage();
        int size = (input.getSize() == null || input.getSize() <= 0) ? 10 : input.getSize();
        PageRequest pageReq = PageRequest.of(page - 1, size);

        PreAssignmentListProcessDTO dto = new PreAssignmentListProcessDTO();
        dto.setStartDate(dateRange[0]);
        dto.setEndDate(dateRange[1]);
        dto.setBookingNumber(input.getBookingNumber());
        dto.setVesselProgrammingDetailId(input.getVesselProgrammingDetailId());
        dto.setBookingId(input.getBookingId());
        dto.setSubBusinessUnitId(input.getSubBusinessUnitId());
        dto.setOperation(input.getOperation() == null ? null : parseIntOrNull(input.getOperation()));
        dto.setClientCompanyId(input.getClientCompanyId());
        dto.setShippingLineId(input.getShippingLineId());

        return bookingRepository.findBookings(dto, pageReq);
    }

    private List<BookingItem> getBookingItems(Page<Booking> pageResult) {
        List<BookingItem> bookingItemList = new ArrayList<>();
        for (Booking b : pageResult.getContent()) {
            BookingItem item = createBookingItem(b);
            bookingItemList.add(item);
        }
        return bookingItemList;
    }

    private BookingItem createBookingItem(Booking b) {
        BookingItem item = new BookingItem();
        item.setBookingId(b.getId());
        item.setBookingNumber(b.getBookingNumber());
        item.setVesselVoyage(getVesselVoyage(b));
        item.setOperation(getOperation(b));
        item.setShippingLine(getShippingLine(b));
        item.setClient(getClient(b));
        item.setLoadingPort(getLoadingPort(b));
        item.setDischargePort(getDischargePort(b));
        item.setBookingIssueDateAsText(formatDate(b.getBookingIssueDate()));
        item.setBookingDetail(getBookingDetailItems(b));
        item.setBookingStatus(getBookingStatus(b));
        item.setRegistrationDate(formatDate(b.getRegistrationDate()));
        item.setModificationDate(formatDate(b.getModificationDate()));
        item.setRegistrationUserId(getUserId(b.getRegistrationUser()));
        item.setRegistrationUserNames(getUserNames(b.getRegistrationUser()));
        item.setRegistrationUserLastNames(getUserLastNames(b.getRegistrationUser()));
        item.setModificationUserId(getUserId(b.getModificationUser()));
        item.setModificationUserNames(getUserNames(b.getModificationUser()));
        item.setModificationUserLastNames(getUserLastNames(b.getModificationUser()));
        return item;
    }

    private String getShippingLine(Booking b) {
        return b.getShippingLine() != null ? b.getShippingLine().getShippingLineCompany() : "";
    }

    private String getClient(Booking b) {
        return b.getClientCompany() != null ? b.getClientCompany().getLegalName() : "";
    }

    private String getLoadingPort(Booking b) {
        return b.getLoadingPort() != null ? b.getLoadingPort().getName() : "";
    }

    private String getDischargePort(Booking b) {
        return b.getDischargePort() != null ? b.getDischargePort().getName() : "";
    }

    private String getBookingStatus(Booking b) {
        return b.getCatBookingStatus() != null ? b.getCatBookingStatus().getDescription() : "";
    }

    private Long getUserId(User user) {
        return user != null ? Long.valueOf(user.getId()) : null;
    }

    private String getUserNames(User user) {
        return user != null ? user.getNames() : "";
    }

    private String getUserLastNames(User user) {
        if (user == null) {
            return "";
        }
        String firstLastName = user.getFirstLastName() != null ? user.getFirstLastName() : "";
        String secondLastName = user.getSecondLastName() != null ? user.getSecondLastName() : "";
        return (firstLastName + " " + secondLastName).trim();
    }

    private String getVesselVoyage(Booking b) {
        String vesselName = (b.getVesselProgrammingDetail() != null &&
                b.getVesselProgrammingDetail().getVesselProgramming() != null &&
                b.getVesselProgrammingDetail().getVesselProgramming().getVessel() != null)
                ? b.getVesselProgrammingDetail().getVesselProgramming().getVessel().getName() : "";
        String voyage = (b.getVesselProgrammingDetail() != null &&
                b.getVesselProgrammingDetail().getVesselProgramming() != null)
                ? b.getVesselProgrammingDetail().getVesselProgramming().getVoyage() : "";
        return vesselName + "/" + voyage;
    }

    private String getOperation(Booking b) {
        return (b.getVesselProgrammingDetail() != null &&
                b.getVesselProgrammingDetail().getCatOperation() != null)
                ? b.getVesselProgrammingDetail().getCatOperation().getLongDescription() : "";
    }

    private String formatDate(LocalDateTime dateTime) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime != null ? dateTime.format(dtf) : "";
    }

    private List<BookingDetailItem> getBookingDetailItems(Booking b) {
        List<BookingDetail> details = bookingDetailRepository.findByBooking(b);
        List<BookingDetailItem> detailItems = new ArrayList<>();
        for (BookingDetail d : details) {
            String sizeDesc = (d.getCatSize() != null) ? d.getCatSize().getDescription() : "";
            String typeDesc = (d.getCatContainerType() != null) ? d.getCatContainerType().getDescription() : "";
            String info = "Tipo Cnt: " + sizeDesc + " - " + typeDesc + " "
                    + "Solicitado: " + d.getReservationQuantity() + " "
                    + "Atendido: " + d.getAttendedQuantity();

            BookingDetailItem bdItem = new BookingDetailItem();
            bdItem.setDetailDescription(info);
            detailItems.add(bdItem);
        }
        return detailItems;
    }

    private Integer parseIntOrNull(String val) {
        try {
            return Integer.valueOf(val);
        } catch (Exception e) {
            return null;
        }
    }
}