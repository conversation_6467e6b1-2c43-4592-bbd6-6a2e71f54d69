package com.maersk.sd1.sde.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class CompanyMailServiceOutput {

    @JsonProperty("asunto_correo")
    private String asuntoCorreo;

    @JsonProperty("lista_edi_codeco_envio_id")
    private List<Integer> listaEdiCodecoEnvioId;

    @JsonProperty("lista_pendientes_envio")
    private List<CompanyMailServiceOutputDetail> listaPendientesEnvio;

    @Data
    public static class CompanyMailServiceOutputDetail {
        @JsonProperty("edi_codeco_envio_id")
        private Integer ediCodecoEnvioId;

        @JsonProperty("nombre_archivo_codeco")
        private String nombreArchivoCodeco;

        @JsonProperty("cat_structure_format_id")
        private Integer catStructureFormatId;
    }
}
