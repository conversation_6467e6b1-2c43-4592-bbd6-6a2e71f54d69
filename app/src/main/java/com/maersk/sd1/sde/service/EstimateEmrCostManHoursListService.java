package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.controller.dto.EstimateEmrCostManHoursListInput;
import com.maersk.sd1.sde.controller.dto.EstimateEmrCostManHoursListOutput;
import com.maersk.sd1.sde.controller.dto.EstimateEmrCostManHoursListOutput.EstimateEmrCostManHoursItem;
import com.maersk.sd1.common.repository.EstimateEmrCostManHoursRepository;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.EstimateEmrCostManHours;
import com.maersk.sd1.common.repository.CatalogRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class EstimateEmrCostManHoursListService {

    private static final Logger logger = LogManager.getLogger(EstimateEmrCostManHoursListService.class);

    private final EstimateEmrCostManHoursRepository estimateEmrCostManHoursRepository;
    private final CatalogRepository catalogRepository;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Transactional(readOnly = true)
    public EstimateEmrCostManHoursListOutput estimateEmrCost(EstimateEmrCostManHoursListInput.Input input) {
        EstimateEmrCostManHoursListOutput response = new EstimateEmrCostManHoursListOutput();
        try {
            logger.info("Executing list service with input: {}", input);

            int page = (input.getPage() == null || input.getPage() < 1) ? 1 : input.getPage();
            int size = (input.getSize() == null || input.getSize() < 1) ? Integer.MAX_VALUE : input.getSize();

            LocalDateTime fechaRegistroMin = input.getFechaRegistroMin() != null ?
                    input.getFechaRegistroMin().atStartOfDay() : null;
            LocalDateTime fechaRegistroMax = input.getFechaRegistroMax() != null ?
                    input.getFechaRegistroMax().plusDays(1).atStartOfDay() : null;
            LocalDateTime fechaModificacionMin = input.getFetchModificationMin() != null ?
                    input.getFetchModificationMin().atStartOfDay() : null;
            LocalDateTime fechaModificacionMax = input.getFetchModificationMax() != null ?
                    input.getFetchModificationMax().plusDays(1).atStartOfDay() : null;

            PageRequest pageRequest = PageRequest.of(page - 1, size, Sort.by("id").descending());

            Page<EstimateEmrCostManHours> resultPage = estimateEmrCostManHoursRepository.findByFilters(
                    input.getEstimadoEmrCostoHhId(),
                    input.getBusinessUnitId(),
                    input.getSubBusinessUnitId(),
                    input.getLineaNavieraId(),
                    input.getCatTipoEstimadoId(),
                    input.getMonedaId(),
                    input.getCostoHoraHombre(),
                    input.getActive(),
                    input.getCatEquipmentCategoryId(),
                    input.getChassisOwnerId(),
                    fechaRegistroMin,
                    fechaRegistroMax,
                    fechaModificacionMin,
                    fechaModificacionMax,
                    pageRequest
            );

            List<EstimateEmrCostManHoursItem> items = new ArrayList<>();
            for (EstimateEmrCostManHours e : resultPage.getContent()) {
                EstimateEmrCostManHoursItem item = new EstimateEmrCostManHoursItem();
                item.setEstimateEmrCostHHId(e.getId());
                item.setBusinessUnitId(e.getBusinessUnit() != null ? e.getBusinessUnit().getId() : null);
                item.setSubBusinessUnitId(e.getSubBusinessUnit() != null ? e.getSubBusinessUnit().getId() : null);
                item.setLineaNavieraId(e.getShippingLine() != null ? e.getShippingLine().getId() : null);
                item.setCatTipoEstimadoId(e.getCatTypeEstimate() != null ? e.getCatTypeEstimate().getId() : null);
                item.setMonedaId(e.getCurrency() != null ? e.getCurrency().getId() : null);
                item.setCostoHoraHombre((double) e.getCostManHour());
                item.setActive(e.getActive());
                item.setFechaRegistro(
                        e.getRegistrationDate() != null ? e.getRegistrationDate().format(DATE_TIME_FORMATTER) : null);
                item.setFechaModificacion(
                        e.getModificationDate() != null ? e.getModificationDate().format(DATE_TIME_FORMATTER) : null);

                if (e.getRegistrationUser() != null) {
                    item.setUserRegistrationId(e.getRegistrationUser().getId());
                    item.setUserRegistrationNames(e.getRegistrationUser().getNames());
                    String apellidoPaterno = e.getRegistrationUser().getFirstLastName() != null ? e.getRegistrationUser().getFirstLastName() : "";
                    String apellidoMaterno = e.getRegistrationUser().getSecondLastName() != null ? e.getRegistrationUser().getSecondLastName() : "";
                    item.setUsuarioRegistroApellidos((apellidoPaterno + " " + apellidoMaterno).trim());
                }
                if (e.getModificationUser() != null) {
                    item.setUserModificationId(e.getModificationUser().getId());
                    item.setUserModificationNames(e.getModificationUser().getNames());
                    String apellidoPaterno = e.getModificationUser().getFirstLastName() != null ? e.getModificationUser().getFirstLastName() : "";
                    String apellidoMaterno = e.getModificationUser().getSecondLastName() != null ? e.getModificationUser().getSecondLastName() : "";
                    item.setUsuarioModificacionApellidos((apellidoPaterno + " " + apellidoMaterno).trim());
                }

                item.setCatEquipmentCategory(e.getCatCategoryEquipment());
                // Retrieve catalog description for cat_equipment_category
                if (e.getCatCategoryEquipment() != null) {
                    Catalog cat = catalogRepository.findById(e.getCatCategoryEquipment()).orElse(null);
                    item.setCatEquipmentCategoryDesc(
                            cat != null ? cat.getLongDescription() : null);
                }

                item.setChassisOwner(e.getChassisOwnerCompany() != null ? e.getChassisOwnerCompany().getLegalName() : null);
                item.setBusinessUnit(e.getBusinessUnit() != null ? e.getBusinessUnit().getName() : null);
                item.setSubBusinessUnit(e.getSubBusinessUnit() != null ? e.getSubBusinessUnit().getName() : null);
                item.setShippingLine(e.getShippingLine() != null ? e.getShippingLine().getShippingLineCompany() : null);
                item.setEstimateType(e.getCatTypeEstimate() != null ? e.getCatTypeEstimate().getDescription() : null);
                item.setMoneyName(e.getCurrency() != null ? e.getCurrency().getName() : null);

                items.add(item);
            }

            response.setTotalRegistros(List.of(List.of(resultPage.getTotalElements())));
            response.setData(items);

        } catch (Exception e) {
            logger.error("Error in list EstimateEmrCostManHoursListService", e);
            response.setTotalRegistros(List.of(List.of(0L)));
            response.setData(new ArrayList<>());
        }
        return response;
    }
}