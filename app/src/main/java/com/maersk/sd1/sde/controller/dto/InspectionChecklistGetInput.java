package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;

@UtilityClass
public class InspectionChecklistGetInput {

    @Data
    public static class Input {

        @JsonProperty("sub_business_unit_id")
        @NotNull
        private BigDecimal subBusinessUnitId;

        @JsonProperty("cat_inspection_type_fgis")
        @NotNull
        @Size(max = 50)
        private String catInspectionTypeFgis;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SDE")
        @NotNull
        private Prefix prefix;
    }

}