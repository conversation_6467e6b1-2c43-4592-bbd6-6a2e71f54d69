package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SetupContainerGateinRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Long unidadNegocioId;

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private Long subUnidadNegocioId;

        @JsonProperty("linea_naviera_id")
        @NotNull
        private Integer lineaNavieraId;

        @JsonProperty("sistema_entrega")
        @NotNull
        @Size(max = 50)
        private String sistemaEntrega;

        @JsonProperty("info_sistema_entrega")
        @Size(max = 200)
        private String infoSistemaEntrega;

        @JsonProperty("identificador_receptor")
        @NotNull
        @Size(max = 10)
        private String identificadorReceptor;

        @JsonProperty("enviar_gate_in_empty")
        @NotNull
        private Boolean enviarGateInEmpty;

        @JsonProperty("enviar_gate_out_empty")
        @NotNull
        private Boolean enviarGateOutEmpty;

        @JsonProperty("enviar_gate_in_full")
        @NotNull
        private Boolean enviarGateInFull;

        @JsonProperty("enviar_gate_out_full")
        @NotNull
        private Boolean enviarGateOutFull;

        @JsonProperty("enviar_status_activity")
        @NotNull
        private Boolean enviarStatusActivity;

        @JsonProperty("cat_formato_gate_out_empty")
        private Long catFormatoGateOutEmpty;

        @JsonProperty("cat_formato_gate_in_full")
        private Long catFormatoGateInFull;

        @JsonProperty("cat_formato_gate_out_full")
        private Long catFormatoGateOutFull;

        @JsonProperty("cat_formato_gate_in_empty")
        private Long catFormatoGateInEmpty;

        @JsonProperty("cat_formato_status_activity")
        private Long catFormatoStatusActivity;

        @JsonProperty("cat_canal_envio_id")
        @NotNull
        private Long catCanalEnvioId;

        @JsonProperty("cat_modo_generar_archivo_id")
        @NotNull
        private Long catModoGenerarArchivoId;

        @JsonProperty("correo_codeco_destino")
        @Size(max = 200)
        private String correoCodecoDestino;

        @JsonProperty("correo_telex_destino")
        @Size(max = 200)
        private String correoTelexDestino;

        @JsonProperty("parametro_1")
        @Size(max = 10)
        private String parametro1;

        @JsonProperty("parametro_2")
        @Size(max = 10)
        private String parametro2;

        @JsonProperty("parametro_3")
        @Size(max = 10)
        private String parametro3;

        @JsonProperty("parametro_4")
        @Size(max = 10)
        private String parametro4;

        @JsonProperty("es_historico")
        @NotNull
        private Boolean esHistorico;

        @JsonProperty("fecha_debaja")
        private String fechaDebaja;

        @JsonProperty("motivo_debaja")
        @Size(max = 200)
        private String motivoDebaja;

        @JsonProperty("activo")
        @NotNull
        private Boolean activo;

        @JsonProperty("usuario_registro_id")
        @NotNull
        private Long usuarioRegistroId;

        @JsonProperty("parametro_5")
        @Size(max = 10)
        private String parametro5;

        @JsonProperty("parametro_6")
        @Size(max = 10)
        private String parametro6;

        @JsonProperty("azure_id_codeco")
        @Size(max = 100)
        private String azureIdCodeco;

        @JsonProperty("azure_id_telex")
        @Size(max = 100)
        private String azureIdTelex;

        @JsonProperty("sftp_id")
        @Size(max = 100)
        private String sftpId;

        @JsonProperty("extension_archivo_enviar")
        @Size(max = 100)
        private String extensionArchivoEnviar;

        @JsonProperty("minutos_trancurridos")
        private Integer minutosTranscurridos;

        @JsonProperty("sub_unidades_json")
        @NotNull
        private List<Map<String, Object>> subUnidadesJson;

        @JsonProperty("gate_in_empty_movimiento_id")
        private Long gateInEmptyMovimientoId;

        @JsonProperty("gate_in_empty_procedencia_json")
        private List<Map<String, Object>> gateInEmptyProcedenciaJson;

        @JsonProperty("gate_out_empty_movimiento_id")
        private Long gateOutEmptyMovimientoId;

        @JsonProperty("gate_out_empty_procedencia_json")
        private List<Map<String, Object>> gateOutEmptyProcedenciaJson;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        @NotNull
        private Prefix prefix;
    }
}