package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;

import java.time.LocalDate;


public class EquipmentListInput {

    @Data
    public static class Input {
        @JsonProperty("sub_business_unit_id")
        @NotNull(message = "subBusinessUnitId cannot be null")
        private Integer subBusinessUnitId;

        @JsonProperty("truck_in_date_min")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
        @PastOrPresent
        private LocalDate truckInDateMin;

        @JsonProperty("truck_in_date_max")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
        @PastOrPresent
        private LocalDate truckInDateMax;

        @JsonProperty("eir_number")
        private Integer eirNumber;

        @JsonProperty("equipment_number")
        private String equipmentNumber;

        @JsonProperty("reference_document_number")
        private String referenceDocumentNumber;

        @JsonProperty("shipping_line_chassis_owner")
        private String shippingLineChassisOwner;

        @JsonProperty("equipment_category")
        private Integer equipmentCategory;

        @JsonProperty("language_id")
        @NotNull(message = "languageId cannot be null")
        private Integer languageId;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private EquipmentListInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private EquipmentListInput.Prefix prefix;
    }
}

