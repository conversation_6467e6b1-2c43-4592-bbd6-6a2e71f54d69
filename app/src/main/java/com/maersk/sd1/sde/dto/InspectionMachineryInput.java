package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionMachineryInput {

    @Data
    public static class Input {

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("usuario_id")
        private Integer userId;

        @JsonProperty("idioma_id")
        private Integer languageId;

        @JsonSetter("eir_id")
        public void setEirId(String eirId) {
            if (StringUtils.isNotEmpty(eirId)) {
                this.eirId = Integer.parseInt(eirId);
            } else {
                this.eirId = null;
            }
        }
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix sde;

        public InspectionMachineryInput.Input getInput() {
            return sde != null ? sde.getInput() : null;
        }
    }
}