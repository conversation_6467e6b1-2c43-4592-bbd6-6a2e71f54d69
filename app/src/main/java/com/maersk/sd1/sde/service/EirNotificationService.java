package com.maersk.sd1.sde.service;


import com.maersk.sd1.common.model.EirNotification;
import com.maersk.sd1.common.repository.EirNotificationRepository;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class EirNotificationService {

    private final EirNotificationRepository repository;

    public List<EirNotification> getAllNotifications() {
        return repository.findAll();
    }

    public Optional<EirNotification> getNotificationById(int id) {
        return repository.findById(id);
    }

    public void deleteNotification(int id) {
        repository.deleteById(id);
    }
}
