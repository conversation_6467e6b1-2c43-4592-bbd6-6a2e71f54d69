package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.controller.dto.InspectionRegisterInput;
import com.maersk.sd1.sde.controller.dto.InspectionRegisterOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Service;

import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

@Service
public class InspectionRegisterService {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public InspectionRegisterService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final Logger logger = LogManager.getLogger(InspectionRegisterService.class);

    public InspectionRegisterOutput registerInspection(InspectionRegisterInput.Root request) {
        logger.info("Request received registerInspection: {}", request);
        SimpleJdbcCall jdbcCall = new SimpleJdbcCall(jdbcTemplate)
                .withProcedureName("registrar_inspeccion_json")
                .withSchemaName("sde")
                .declareParameters(
                        new SqlParameter("unidad_negocio_id", Types.NUMERIC),
                        new SqlParameter("sub_unidad_negocio_id", Types.NUMERIC),
                        new SqlParameter("sub_unidad_negocio_local_id", Types.NUMERIC),
                        new SqlParameter("eir_id", Types.INTEGER),
                        new SqlParameter("IDActividadZona", Types.INTEGER),
                        new SqlParameter("Contenedor", Types.VARCHAR),
                        new SqlParameter("TnoCnt_id", Types.NUMERIC),
                        new SqlParameter("TipoCnt_id", Types.NUMERIC),
                        new SqlParameter("ClaseCnt_id", Types.NUMERIC),
                        new SqlParameter("Tara", Types.INTEGER),
                        new SqlParameter("CargaMaxima", Types.INTEGER),
                        new SqlParameter("CodigoISO_id", Types.INTEGER),
                        new SqlParameter("CodigoLinea_id", Types.INTEGER),
                        new SqlParameter("FechaFabricacion", Types.VARCHAR),
                        new SqlParameter("TipoReefer_id", Types.NUMERIC),
                        new SqlParameter("MarcaMotor_id", Types.NUMERIC),
                        new SqlParameter("ConDano", Types.BIT),
                        new SqlParameter("SiguienteZona", Types.VARCHAR),
                        new SqlParameter("FlagInspCompleja", Types.BIT),
                        new SqlParameter("FlagBarrer", Types.BIT),
                        new SqlParameter("FlagLavadoEspecial", Types.BIT),
                        new SqlParameter("bloquear_contenedor", Types.BIT),
                        new SqlParameter("FlagSensor", Types.VARCHAR),
                        new SqlParameter("FlagRCD", Types.BIT),
                        new SqlParameter("FlagPendRepuesto", Types.BIT),
                        new SqlParameter("DetalleEstimado", Types.NVARCHAR),
                        new SqlParameter("Fotos", Types.NVARCHAR),
                        new SqlParameter("es_inspeccion_parcial", Types.BIT),
                        new SqlParameter("usuario_id", Types.INTEGER),
                        new SqlParameter("jsonChecklist", Types.NVARCHAR),
                        new SqlParameter("edit", Types.BIT),
                        new SqlParameter("jsonChecklistCa", Types.NVARCHAR),
                        new SqlParameter("flag_standard_inspection", Types.BIT),
                        new SqlParameter("flag_ca_inspection", Types.BIT),
                        new SqlParameter("cat_standard_inspection_id", Types.NUMERIC),
                        new SqlParameter("cat_ca_inspection_id", Types.NUMERIC),
                        new SqlParameter("cat_machinery_brand_id", Types.NUMERIC),
                        new SqlParameter("service_date", Types.DATE),
                        new SqlParameter("with_damage_ca", Types.BIT),
                        new SqlParameter("DetalleEstimado_ca", Types.NVARCHAR),
                        new SqlParameter("cat_machinery_model_id", Types.NUMERIC),
                        new SqlParameter("cat_controller_type_id", Types.NUMERIC),
                        new SqlParameter("cat_refrigerant_type_id", Types.NUMERIC),
                        new SqlParameter("engine_serial_number", Types.VARCHAR),
                        new SqlParameter("software_number", Types.VARCHAR),
                        new SqlParameter("available_coolant", Types.NUMERIC),
                        new SqlParameter("update_material_request_emr_detail", Types.NVARCHAR),

                        new SqlParameter("cat_complejidad_dano_id", Types.NUMERIC),
                        new SqlParameter("precinto_bloqueo", Types.VARCHAR),
                        new SqlParameter("cat_type_setting_material_id", Types.NUMERIC),
                        new SqlParameter("cat_color_id", Types.NUMERIC),
                        new SqlParameter("ObservacionesInspector", Types.NVARCHAR),
                        new SqlParameter("ObservacionInspOtros", Types.NVARCHAR),
                        new SqlParameter("DeletedDamagesIds", Types.NVARCHAR),

                        new SqlOutParameter("resp_new_id", Types.INTEGER),
                        new SqlOutParameter("resp_estado", Types.INTEGER),
                        new SqlOutParameter("resp_mensaje", Types.NVARCHAR),
                        new SqlOutParameter("SiguienteZonaGuardada", Types.VARCHAR)
                );

        InspectionRegisterInput.Input input  = request.getPrefix().getInput();

        Map<String, Object> inParams = new HashMap<>();
        inParams.put("unidad_negocio_id", input.getUnidadNegocioId());
        inParams.put("sub_unidad_negocio_id", input.getSubUnidadNegocioId());
        inParams.put("sub_unidad_negocio_local_id", input.getSubUnidadNegocioLocalId());
        inParams.put("eir_id", input.getEirId());
        inParams.put("IDActividadZona", input.getIdActividadZona());
        inParams.put("Contenedor", input.getContenedor());
        inParams.put("TnoCnt_id", input.getTnoCntId());
        inParams.put("TipoCnt_id", input.getTipoCntId());
        inParams.put("ClaseCnt_id", input.getClaseCntId());
        inParams.put("flagInspCompleja", input.getFlagInspCompleja());
        inParams.put("Tara", input.getTara());
        inParams.put("CargaMaxima", input.getCargaMaxima());
        inParams.put("CodigoISO_id", input.getCodigoISOId());
        inParams.put("CodigoLinea_id", input.getCodigoLineaId());
        inParams.put("FechaFabricacion", input.getFechaFabricacion());
        inParams.put("TipoReefer_id", input.getTipoReeferId());
        inParams.put("MarcaMotor_id", input.getMarcaMotorId());
        inParams.put("ConDano", input.getConDano());
        inParams.put("SiguienteZona", input.getSiguienteZona());
        inParams.put("service_date", input.getServiceDate());
        inParams.put("FlagBarrer", input.getFlagBarrer());
        inParams.put("FlagLavadoEspecial", input.getFlagLavadoEspecial());
        inParams.put("cat_complejidad_dano_id", input.getCatComplejidadDanoId());
        inParams.put("bloquear_contenedor", input.getBloquearContenedor());
        inParams.put("precinto_bloqueo", input.getPrecintoBloqueo());
        inParams.put("cat_type_setting_material_id", input.getCatTypeSettingMaterialId());
        inParams.put("cat_color_id", input.getCatColorId());
        inParams.put("ObservacionesInspector", input.getObservacionesInspector());
        inParams.put("ObservacionInspOtros", input.getObservacionInspOtros());
        inParams.put("FlagSensor", input.getFlagSensor());
        inParams.put("FlagRCD", input.getFlagRCD());
        inParams.put("FlagPendRepuesto", input.getFlagPendRepuesto());
        inParams.put("DetalleEstimado", input.getDetalleEstimado());
        inParams.put("DeletedDamagesIds", input.getDeletedDamagesIds());
        inParams.put("Fotos", input.getFotos());
        inParams.put("es_inspeccion_parcial", input.getEsInspeccionParcial());
        inParams.put("usuario_id", input.getUsuarioId());
        inParams.put("jsonChecklist", input.getJsonChecklist());
        inParams.put("edit", input.getEdit());
        inParams.put("jsonChecklistCa", input.getJsonChecklistCa());
        inParams.put("flag_standard_inspection", input.getFlagStandardInspection());
        inParams.put("flag_ca_inspection", input.getFlagCaInspection());
        inParams.put("cat_standard_inspection_id", input.getCatStandardInspectionId());
        inParams.put("cat_ca_inspection_id", input.getCatCaInspectionId());
        inParams.put("cat_machinery_brand_id", input.getCatMachineryBrandId());
        inParams.put("with_damage_ca", input.getWithDamageCa());
        inParams.put("DetalleEstimado_ca", input.getDetalleEstimadoCa());
        inParams.put("cat_machinery_model_id", input.getCatMachineryModelId());
        inParams.put("cat_controller_type_id", input.getCatControllerTypeId());
        inParams.put("cat_refrigerant_type_id", input.getCatRefrigerantTypeId());
        inParams.put("engine_serial_number", input.getEngineSerialNumber());
        inParams.put("software_number", input.getSoftwareNumber());
        inParams.put("available_coolant", input.getAvailableCoolant());
        inParams.put("update_material_request_emr_detail", input.getUpdateMaterialRequestEmrDetail());

        logger.info("Input parameters: {}", inParams);

        Map<String, Object> outParams = jdbcCall.execute(inParams);

        InspectionRegisterOutput response = new InspectionRegisterOutput();
        response.setRespNewId((Integer) outParams.get("resp_new_id"));
        response.setRespEstado((Integer) outParams.get("resp_estado"));
        response.setRespMensaje((String) outParams.get("resp_mensaje"));
        response.setSiguienteZonaGuardada((String) outParams.get("SiguienteZonaGuardada"));
        response.setUpdateMaterialRequestEmrDetail((String) outParams.get("update_material_request_emr_detail"));

        logger.info("Output parameters: {}", outParams);

        return response;
    }
}