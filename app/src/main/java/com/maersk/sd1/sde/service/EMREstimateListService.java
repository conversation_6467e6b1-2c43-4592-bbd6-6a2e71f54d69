package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.repository.BusinessUnitConfigRepository;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.EstimateEmrRepository;
import com.maersk.sd1.common.repository.EstimateEmrSettingRepository;
import com.maersk.sd1.common.service.BusinessUnitConfigService;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sde.controller.dto.EMREstimateListDataOutput;
import com.maersk.sd1.sde.controller.dto.EMREstimateListInput;
import com.maersk.sd1.sde.controller.dto.EMREstimateListOutput;
import com.maersk.sd1.sde.dto.EMREstimateListDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
public class EMREstimateListService {

    private final BusinessUnitRepository businessUnitRepository;

    private final EstimateEmrRepository estimateEmrRepository;

    private final BusinessUnitConfigRepository businessUnitConfigRepository;

    private final EstimateEmrSettingRepository estimateEmrSettingRepository;

    private final GESCatalogService catalogService;

    private final BusinessUnitConfigService businessUnitConfigService;

    private final MessageLanguageService messageLanguageService;

    public EMREstimateListOutput execute(EMREstimateListInput.Input input) throws JsonProcessingException {

        EMREstimateListOutput output = new EMREstimateListOutput();
        ObjectMapper objectMapper = new ObjectMapper();

        Optional<Integer> parentBuOpt = businessUnitRepository.findParentBusinessUnitIdOrNull(input.getSubBusinessUnitId());

        if (!parentBuOpt.isEmpty()) {

            HashMap<String, Integer> catalogIds = new HashMap<>(catalogService.findIdsByAliases(Arrays.asList(
                    Parameter.ESTIMATE_REPAIR_CANCEL_OTHER,
                    Parameter.REASON_EMR_REJECTION
            )));

            List<String> mercPlusList;
            if(input.getMercPlusEstimateNumberList() != null && !input.getMercPlusEstimateNumberList().isEmpty() && !input.getMercPlusEstimateNumberList().equals("[]")){
                JsonNode jsonNode = objectMapper.readTree(input.getMercPlusEstimateNumberList());
                mercPlusList = new ArrayList<>();
                jsonNode.forEach(node -> mercPlusList.add(node.get("merc_plus_estimate_number").asText()));
            } else {
                mercPlusList = null;
            }

            List<String> containerList;
            if(input.getContainerList() != null && !input.getContainerList().isEmpty() && !input.getContainerList().equals("[]")){
                JsonNode jsonNode = objectMapper.readTree(input.getContainerList());
                containerList = new ArrayList<>();
                jsonNode.forEach(node -> containerList.add(node.get("contenedor").asText()));
            } else {
                containerList  = null;
            }

            Integer timeZoneGmt = businessUnitConfigRepository.findValueByBusinessUnitIdAndConfigType(parentBuOpt.get(), Integer.parseInt(Parameter.CATALOG_CONFIGURATION_BUSINESS_UNIT_TIME_ZONE_ALIAS));
            timeZoneGmt = timeZoneGmt * -1;

            LocalDateTime estimatedInspectionDateFrom = null;
            if(input.getInspectionDateFrom() != null){
                estimatedInspectionDateFrom = input.getInspectionDateFrom().atStartOfDay().plusHours(timeZoneGmt);
            }

            LocalDateTime estimatedInspectionDateTo = null;
            if(input.getInspectionDateTo() != null){
                estimatedInspectionDateTo = input.getInspectionDateTo().atTime(23, 59, 59).plusHours(timeZoneGmt);
            }

            System.out.println(estimatedInspectionDateFrom);
            System.out.println(estimatedInspectionDateTo);

            List<Integer> estimatesFound = estimateEmrRepository.findEstimatedEmrIdsByListEstimates(
                    containerList,
                    input.getEstimatedEmrId(),
                    input.getEirId(),
                    input.getSubBusinessUnitId(),
                    input.getEstimatedTypeId(),
                    input.getEstimatedStateId(),
                    estimatedInspectionDateFrom,
                    estimatedInspectionDateTo,
                    input.getEstimatedModeId(),
                    input.getWithoutGatein() != null && input.getWithoutGatein().equals("1"),
                    mercPlusList
            );

            System.out.println("estimatesFound.size()");
            System.out.println(estimatesFound.size());

            List<Integer> innerList = new ArrayList<>();
            innerList.add(estimatesFound.size());

            List<List<Integer>> outerList = new ArrayList<>();
            outerList.add(innerList);

            output.setTotalRecords(outerList);

            Pageable pageable = PageRequest.of(
                    Math.max(input.getPage() - 1, 0),
                    Math.max(input.getSize(), 1)
            );

            Page<EMREstimateListDTO> emrEstimated = estimateEmrRepository.findEstimadoEmrById(
                estimatesFound,
                pageable
            );

            String yesMessage = messageLanguageService.getMessage("si", 1, input.getLanguageId());

            List<EMREstimateListDataOutput> finalResult = new ArrayList<EMREstimateListDataOutput>();

            emrEstimated.forEach(emrEstimateListDTO -> {

                EMREstimateListDataOutput element = new EMREstimateListDataOutput();

                element.setEstimateEmrId(emrEstimateListDTO.getEstimateEmrId());
                element.setCatEstimateTypeId(""+emrEstimateListDTO.getCatEstimateTypeId());
                element.setEstimateTypeName(catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatEstimateTypeId(), input.getLanguageId()));
                element.setContainerNumber(emrEstimateListDTO.getContainerNumber());
                element.setContainerType(emrEstimateListDTO.getCatcntDesc());
                element.setContainerSize(emrEstimateListDTO.getCatsizDesc());
                element.setShippingLine(emrEstimateListDTO.getShippingLineCompany());
                element.setEirId(emrEstimateListDTO.getEirId());
                element.setEstimateModeName(emrEstimateListDTO.getCatmodDesc());
                element.setCatEstimateStatusId(emrEstimateListDTO.getCatEstimateStatusId());
                element.setStatusName(catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatEstimateStatusId(), input.getLanguageId()));
                element.setCurrencyAbbreviation(emrEstimateListDTO.getCurrencyAbbreviation());
                element.setTotalEstimateHours(emrEstimateListDTO.getEstimateTotalHours());
                element.setTotalEstimateCost(emrEstimateListDTO.getEstimateTotalCost());
                element.setInspectionDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getEstimateDateInspection()));
                element.setApprovalRejectionDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getApproveRejectEstimateDate()));
                if(emrEstimateListDTO.getCatRejectCodeEstimateId() != null && emrEstimateListDTO.getCatRejectCodeEstimateId().equals(catalogIds.get(Parameter.REASON_EMR_REJECTION))){
                    element.setRejectionReason(emrEstimateListDTO.getRejectionObsEstimate());
                } else {
                    element.setRejectionReason(catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatRejectCodeEstimateId(), input.getLanguageId()));
                }
                element.setCompletionDate(emrEstimateListDTO.getCompletedEstimateDate());
                element.setGateInDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getGateInEirTruckArrivalDate()));
                element.setGateOutDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getGateOutEirtruckArrivalDate()));
                element.setInspector(emrEstimateListDTO.getInspectorName());

                element.setEstimatorUserId(emrEstimateListDTO.getEstimatorUserId());
                element.setEstimatorUserFirstName(emrEstimateListDTO.getEstimatorName());
                element.setEstimatorUserFirstName(emrEstimateListDTO.getEstimatorLastName());

                element.setApprovalRejectionUserId(emrEstimateListDTO.getApproveRejectUserId());
                element.setApprovalRejectionUserFirstName(emrEstimateListDTO.getApproveName());
                element.setApprovalRejectionUserLastName(emrEstimateListDTO.getApproveLastName());

                element.setCompletionUserId(emrEstimateListDTO.getCompletedUserId());
                element.setCompletionUserFirstName(emrEstimateListDTO.getCompletedName());
                element.setCompletionUserLastName(emrEstimateListDTO.getCompletedLastName());

                element.setLocalBusinessUnit(emrEstimateListDTO.getBusinessUnit());

                Integer shippingLineId = emrEstimateListDTO.getEstimateEMRShippingLineId() == null ? emrEstimateListDTO.getContainerShippingLineId() : emrEstimateListDTO.getEstimateEMRShippingLineId();
                Integer findSettings = estimateEmrSettingRepository.countActiveEstimatesByShippingLineAndSubBusinessUnit(shippingLineId, input.getSubBusinessUnitId());
                String statusApproveSend;
                if(findSettings > 0){
                    statusApproveSend = catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatApprovalSendEstimateStatusId(), input.getLanguageId());
                } else {
                    statusApproveSend = messageLanguageService.getMessage("GENERAL", 4, input.getLanguageId());
                }
                element.setApprovalSubmissionStatus(statusApproveSend);

                element.setWithoutGateIn(emrEstimateListDTO.getWithoutGateinEstimate()?yesMessage:"");
                element.setAutomaticLock((emrEstimateListDTO.getBlockSendingCtrl() != null && emrEstimateListDTO.getBlockSendingCtrl() == 1)?yesMessage:"");
                element.setLocked((emrEstimateListDTO.getBlockSending() != null && emrEstimateListDTO.getBlockSending())?yesMessage:"");

                element.setRegistrationDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getRegistrationDate()));
                element.setRegistrationUserId(emrEstimateListDTO.getRegistrationUserId());
                element.setRegistrationUserFirstName(emrEstimateListDTO.getRegistrationUserName());
                element.setRegistrationUserLastName(emrEstimateListDTO.getRegistrationUserLastName());

                element.setModificationDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getModificationDate()));
                element.setModificationUserId(emrEstimateListDTO.getModificationUserId());
                element.setRegistrationUserFirstName(emrEstimateListDTO.getModificationUserName());
                element.setRegistrationUserLastName(emrEstimateListDTO.getModificationUserLastName());

                element.setCatEstimateApprovalSubmissionStatusId(emrEstimateListDTO.getCatApprovalSendEstimateStatusId());
                element.setOrigin(catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatCreationOriginEstimateId(), input.getLanguageId()));
                element.setEstimateSubmissionDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getEstimateSubmissionDate()));

                element.setCatRepairBoxStatus(emrEstimateListDTO.getCatApprovalRepBoxId());
                element.setRepairBoxStatus(catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatApprovalRepBoxId(), input.getLanguageId()));

                element.setCatRepairMachineryStatus(emrEstimateListDTO.getCatApprovalRepMachineId());
                element.setRepairMachineryStatus(catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatApprovalRepMachineId(), input.getLanguageId()));

                element.setSubmissionUserId(emrEstimateListDTO.getSubmissionUserId());
                element.setSubmissionUserFirstName(emrEstimateListDTO.getSubmissionUserName());
                element.setSubmissionUserLastName(emrEstimateListDTO.getSubmissionUserLastName());

                element.setAutoApprovalFlag(emrEstimateListDTO.getFlagAutoApproval());
                element.setMercPlusEstimateNumber(emrEstimateListDTO.getMercPlusEstimateNumber());
                element.setCleaningStatusId(emrEstimateListDTO.getCatCleaningStatusId());
                element.setCleaningStatus(catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatCleaningStatusId(), input.getLanguageId()));
                element.setCancelEstimateDate(businessUnitConfigService.getDateTime(input.getSubBusinessUnitId(), emrEstimateListDTO.getRegistrationCancelEstimateDate()));

                String reasonCancelEstimateDescription;
                if(Objects.equals(emrEstimateListDTO.getCatReasonCancelEstimateId(), catalogIds.get(Parameter.ESTIMATE_REPAIR_CANCEL_OTHER))){
                    reasonCancelEstimateDescription = emrEstimateListDTO.getReasonCancelEstimateDescription();
                } else {
                    reasonCancelEstimateDescription = catalogService.getCatalogTranslationDesc(emrEstimateListDTO.getCatReasonCancelEstimateId(), input.getLanguageId());
                }
                element.setCancelReason(reasonCancelEstimateDescription);

                element.setCancelUserId(emrEstimateListDTO.getCancelUserId());
                element.setCancelUserFirstName(emrEstimateListDTO.getCancelUserName());
                element.setCancelUserLastName(emrEstimateListDTO.getCancelUserLastName());

                finalResult.add(element);

            });

            output.setFinalResult(finalResult);

        }

        return output;

    }

}