package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;



@UtilityClass
public class RepairManagementInput {

    @Data
    public static class Input {

        @JsonProperty("sub_business_unit_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("sub_business_unit_local_id")
        @NotNull
        private Integer subBusinessUnitLocalId;

        @JsonProperty("repair_type_id")
        @NotNull
        private Integer repairTypeId; // 48030 - Estructura, 48031 - Maquinaria

        @JsonProperty("repair_approval_status")
        @NotNull
        private Integer repairApprovalStatus; // 47507-Pendiente, 47508-Aprobado, 47509-Rechazado

        @JsonProperty("containers_list")
        @Size(max = 5000)
        private String containersList;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("in_stock")
        private Boolean inStock;

        @JsonProperty("cat_estimate_status_id")
        private Integer catEstimateStatusId;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId; // 1=en, 2=es, 3=pt

        @JsonProperty("page")
        @NotNull
        private Integer page;

        @JsonProperty("size")
        @NotNull
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private RepairManagementInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private RepairManagementInput.Prefix prefix;
    }
}
