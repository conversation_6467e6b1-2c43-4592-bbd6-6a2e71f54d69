package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.DriverGetInputDTO;
import com.maersk.sd1.sde.dto.DriverGetOutputDTO;
import com.maersk.sd1.sde.service.DriverGetService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEConductorServiceImp")
public class DriverGetController {
    private static final Logger logger = LogManager.getLogger(DriverGetController.class);

    private final DriverGetService driverGetService;

    public DriverGetController(DriverGetService driverGetService) {
        this.driverGetService = driverGetService;
    }

    @PostMapping("/sdeconductorObtener")
    public ResponseEntity<ResponseController<List<DriverGetOutputDTO>>> getDriver(@RequestBody @Valid DriverGetInputDTO.Root request) {
        try {
            DriverGetInputDTO.Input input = request.getPrefix().getInput();
            DriverGetOutputDTO output = driverGetService.getDriver(
                    input.getIdentityDocument(),
                    input.getBusinessUnitId()
            );

            if (output == null) {
                return ResponseEntity.ok(new ResponseController<>(List.of()));
            }

            return ResponseEntity.ok(new ResponseController<>(List.of(output)));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            DriverGetOutputDTO output = new DriverGetOutputDTO();
            output.setResponseMessage(e.toString());
            output.setResponseStatus(0);
            return ResponseEntity.status(500).body(new ResponseController<>(List.of(output)));
        }
    }
}