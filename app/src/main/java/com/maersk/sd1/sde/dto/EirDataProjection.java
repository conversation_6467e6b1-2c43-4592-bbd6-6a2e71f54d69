package com.maersk.sd1.sde.dto;

public interface EirDataProjection {
    Integer getEirId();
    String getTipoGate();
    String getTipoMovimiento();
    String getFechaIngresoCamion();
    String getTruckInDate();
    String getFechaSalidaCamion();
    String getTruckOutDate();
    String getEirLocal();
    String getNaveViajeOperacion();
    Integer getContenedorId();
    String getNumeroContenedor();
    Integer getCntSizeCatalogId();
    String getTamanoCnt();
    Integer getCntTypeCatalogId();
    String getTipoContenedor();
    Integer getCodigoIsoId();
    String getCodigoIsoDesc();
    String getLineaNavieraCnt();
    String getClaseCnt();
    Integer getTaraCnt();
    Integer getCargaMaximaCnt();
    String getFFabricacion();
    Integer getTipoReeferId();
    String getTipoReefer();
    Integer getMarcaMotorId();
    String getMarcaMotor();
    String getPrecinto1();
    String getPrecinto2();
    String getPrecinto3();
    String getPrecinto4();
    String getCliente();
    String getVehiculo();
    Integer getEmpresaTransporteId();
    String getEmpresaTransporteNombre();
    String getConductor();
    String getDriverDocument();
    String getDriverName();
    String getDriverLastname();
    Boolean getEstructuraConDano();
    Boolean getMaquinariaConDano();
    String getComentarios();
    String getFFinalizeGrua();
    String getUbicacionPatio();
    String getFsobrestadia();
    String getFInspeccion();
    String getNombreInspector();
    String getNombreTecnico();
    String getFAsignacion();
    String getControlInspection();
    String getDocumentoReferencia();
    Integer getCita();
    Boolean getParaVenta();
    String getSituacionRepEstructura();
    String getObsAprobRepEstructura();
    String getSituacionRepMaquinaria();
    String getObsAprobRepMaquinaria();
    Boolean getEstructuraDanadaHistorico();
    Boolean getMaquinariaDanadaHistorico();
    String getCitasOnlineDpwCallao();
    String getCitasApmtCallaoExpo();
    String getNumeracion();
    String getRectificacion();
    String getEliminacion();
    Integer getWeightGoods();
    Integer getCatMeasureWeightId();
    String getTipoUnidadPeso();
    String getPeso();
    String getOperation();
    String getTara();
    String getCargaMaxima();
    Integer getCatMeansTransportId();
    String getMedioTransporte();
    Integer getTransportPlanningDetailFullId();
    Boolean getFlagChassisStayed();
    Boolean getFlagChassisPickup();
    String getEirChassisReference();
    String getDepartureCancelReason();
    Integer getCatMovementId();
    Integer getCatEmptyFullId();
    String getChassisNumber();
    Integer getEirChassisId();
}
