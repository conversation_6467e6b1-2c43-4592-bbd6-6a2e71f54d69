package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

//[sde].[documentation_empty_list]
@Service
@Log4j2
@RequiredArgsConstructor
public class DocumentationEmptyListService {
    private final VesselProgrammingRepository vesselProgrammingRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private final CatalogRepository catalogRepository;
    private final ContainerRepository containerRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final BookingDetailRepository bookingDetailRepository;
    private final BookingRepository bookingRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final EirRepository eirRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Transactional(readOnly = true)
    public DocumentationEmptyListOutput documentationEmptyList(DocumentationEmptyListInput.Input input) {
        Integer documentTypeBookingId = catalogRepository.findCatalogIdByAliasAndStatus("cat_48734_bk", true);
        Integer documentTypeBlId = catalogRepository.findCatalogIdByAliasAndStatus("cat_48734_bl", true);

        Integer movementTypeGateInId = catalogRepository.findCatalogIdByAliasAndStatus("43080", true);
        Integer movementTypeGateOutId = catalogRepository.findCatalogIdByAliasAndStatus("43081", true);
        String movementTypeGateInDesc = catalogRepository.findVariable2ByAliasAndStatus("43080", true);
        String movementTypeGateOutDesc = catalogRepository.findVariable2ByAliasAndStatus("43081", true);

        Integer isOperationExport = catalogRepository.findCatalogIdByAliasAndStatus("cat_48789_export", true);
        Integer isOperationImport = catalogRepository.findCatalogIdByAliasAndStatus("cat_48789_import", true);

        String messageType = messageLanguageRepository.fnTranslatedMessage("GENERAL", 30, input.getLanguageId());
        String messageRequested = messageLanguageRepository.fnTranslatedMessage("GENERAL", 31, input.getLanguageId());
        String messageAssigned = messageLanguageRepository.fnTranslatedMessage("GENERAL", 32, input.getLanguageId());
        String messagePending = messageLanguageRepository.fnTranslatedMessage("GENERAL", 33, input.getLanguageId());
        String messageReceived = messageLanguageRepository.fnTranslatedMessage("GENERAL", 34, input.getLanguageId());

        Integer completedDocumentId = catalogRepository.findCatalogIdByAliasAndStatus("sd1_status_completed", true);
        Integer inProcessDocumentId = catalogRepository.findCatalogIdByAliasAndStatus("sd1_status_in_progress", true);
        Integer pendingDocumentId = catalogRepository.findCatalogIdByAliasAndStatus("sd1_status_pending", true);

        Integer catEstadoCanceladoId = catalogRepository.findCatalogIdByAliasAndStatus("43062", true);

        Integer catOperationId = catalogRepository.findCatalogIdByAliasAndStatus("cat_42992_simple_storage", true);

        Integer isTypeContainerDry = catalogRepository.findCatalogIdByAlias("31049");
        Integer isTypeContainerHc = catalogRepository.findCatalogIdByAlias("31053");

        List<DocumentationEmptyListTbList> Tlist = new ArrayList<>();

        List<DocumentationEmptyListTContainers> tContainers = new ArrayList<>();

        if (input.getContainers() != null) {
            tContainers = processContainers(input.getContainers()); // No need to declare again
        }

        if (Objects.equals(input.getMovementTypeId(), movementTypeGateOutId)) {
            List<Integer> bookingBlIds = processBookingBlIds(tContainers, input.getMovementTypeId(), movementTypeGateOutId, input.getSubBusinessUnitId());

            List<DocumentationListEmptyTbListProjection> tlist = bookingRepository.findDocumentationList(documentTypeBookingId, input.getLanguageId(), movementTypeGateOutId, movementTypeGateOutDesc, isOperationExport, input.getSubBusinessUnitId(), input.getDocumentNumber(), input.getRegisterDateMin(), input.getRegisterDateMax(), input.getShippingLineId(), input.getVesselName(), input.getVoyageNumber(), bookingBlIds);

            for (DocumentationListEmptyTbListProjection t : tlist) {
                DocumentationEmptyListTbList tbList = new DocumentationEmptyListTbList();

                tbList.setEmptyDocumentId(t.getEmptyDocumentId() != null ? t.getEmptyDocumentId() : null);
                tbList.setDocumentTypeId(t.getDocumentTypeId() != null ? t.getDocumentTypeId() : null);
                tbList.setDocumentTypeAlias(t.getDocumentTypeAlias() != null ? t.getDocumentTypeAlias() : null);
                tbList.setDocumentType(t.getDocumentType() != null ? t.getDocumentType() : null);
                tbList.setDocumentNumber(t.getDocumentNumber() != null ? t.getDocumentNumber() : null);
                tbList.setMovementTypeId(t.getMovementTypeId() != null ? t.getMovementTypeId() : null);
                tbList.setMovementType(t.getMovementType() != null ? t.getMovementType() : null);
                tbList.setShippingLine(t.getShippingLine() != null ? t.getShippingLine() : null);
                tbList.setCommodity(t.getCommodity() != null ? t.getCommodity() : null);
                tbList.setProduct(t.getProduct() != null ? t.getProduct() : null);
                tbList.setOperationTypeId(t.getOperationTypeId() != null ? t.getOperationTypeId() : null);
                tbList.setOperationType(t.getOperationType() != null ? t.getOperationType() : null);
                tbList.setConsignee(t.getConsignee() != null ? t.getConsignee() : null);
                tbList.setShipper(t.getShipper() != null ? t.getShipper() : null);
                tbList.setVessel(t.getVessel() != null ? t.getVessel() : null);
                tbList.setVoyage(t.getVoyage() != null ? t.getVoyage() : null);
                tbList.setUserRegistrationId(t.getUserRegistrationId() != null ? t.getUserRegistrationId() : null);
                tbList.setUserModificationId(t.getUserModificationId() != null ? t.getUserModificationId() : null);
                tbList.setUserRegistrationDate(t.getUserRegistrationDate() != null ? t.getUserRegistrationDate() : null);
                tbList.setUserModificationDate(t.getUserModificationDate() != null ? t.getUserModificationDate() : null);
                tbList.setUserRegistrationName(t.getUserRegistrationName() != null ? t.getUserRegistrationName() : null);
                tbList.setUserRegistrationLastName(t.getUserRegistrationLastName() != null ? t.getUserRegistrationLastName() : null);
                tbList.setUserModificationName(t.getUserModificationName() != null ? t.getUserModificationName() : null);
                tbList.setUserModificationLastName(t.getUserModificationLastName() != null ? t.getUserModificationLastName() : null);
                tbList.setCreationSource(t.getCreationSource() != null ? t.getCreationSource() : null);
                tbList.setQuantityRequested(t.getQuantityRequested() != null ? t.getQuantityRequested() : null);
                tbList.setQuantityAssigned(t.getQuantityAssigned() != null ? t.getQuantityAssigned() : null);
                tbList.setQuantityPending(t.getQuantityPending() != null ? t.getQuantityPending() : null);
                tbList.setDetail(t.getDetail() != null ? t.getDetail() : null);
                tbList.setStatusId(t.getStatusId() != null ? t.getStatusId() : null);
                tbList.setStatus(t.getStatus() != null ? t.getStatus() : null);
                tbList.setCatEstado(t.getCatEstado() != null ? t.getCatEstado() : null);
                tbList.setStatusAlias(t.getStatusAlias() != null ? t.getStatusAlias() : null);
                tbList.setMoveType(t.getMoveType() != null ? t.getMoveType() : null);
                tbList.setRemarkRule(t.getRemarkRule() != null ? t.getRemarkRule() : null);

                Tlist.add(tbList);
            }

            List<Integer> emptyDocumentIds = Tlist.stream()
                    .map(DocumentationEmptyListTbList::getEmptyDocumentId)
                    .collect(Collectors.toList());

            List<DocumentationEmptyListTb01Projection> tb01List = bookingDetailRepository.getDocumentationEmptyListTb01(isTypeContainerDry, emptyDocumentIds);

            List<DocumentationEmptyListTb01> tb01 = new ArrayList<>();
            for (DocumentationEmptyListTb01Projection t : tb01List) {
                DocumentationEmptyListTb01 tb01x = new DocumentationEmptyListTb01();
                tb01x.setBookingBlId(t.getBookingBlId());
                tb01x.setSizeContainerId(t.getSizeContainerId());
                tb01x.setTypeContainerId(t.getTypeContainerId());
                tb01x.setTotalQuantity(t.getTotalQuantity());
                tb01x.setRemarkRule(t.getRemarkRule());
                tb01.add(tb01x);
            }

            Integer isGateOut = catalogRepository.findCatalogIdByAlias("43081");
            Integer containerNoCntId = containerRepository.findIdByContainerNumber("NO-CNT");

            List<DocumentationEmptyListTb02> tb02 = processEirData(Tlist, isGateOut, containerNoCntId, isTypeContainerDry, isTypeContainerHc);

            List<Integer> sizeContainerIds = tb01.stream()
                    .map(DocumentationEmptyListTb01::getSizeContainerId)
                    .collect(Collectors.toList());

            List<Integer> typeContainerIds = tb01.stream()
                    .map(DocumentationEmptyListTb01::getTypeContainerId)
                    .collect(Collectors.toList());

            List<DocumentationEmptyListTDetailList> TDetailList = processBookingDetails(tb01, tb02, sizeContainerIds, typeContainerIds);

            updateTList(Tlist, TDetailList, messageType, messageRequested, messageAssigned, messagePending);

            updateStatusId(Tlist, inProcessDocumentId, completedDocumentId, pendingDocumentId, catEstadoCanceladoId);

        }
        if (Objects.equals(input.getMovementTypeId(), movementTypeGateInId)) {
            List<Integer> bookingBlIds = processBookingBlIdsGateIn(tContainers, input.getMovementTypeId(), movementTypeGateInId, input.getSubBusinessUnitId());

            List<DocumentationListEmptyTbListProjection> tList = cargoDocumentRepository.findDocumentationList(documentTypeBlId, input.getLanguageId(), movementTypeGateInId, movementTypeGateInDesc, isOperationImport, input.getSubBusinessUnitId(), input.getDocumentNumber(), input.getRegisterDateMin(), input.getRegisterDateMax(), input.getShippingLineId(), input.getVesselName(), input.getVoyageNumber(), bookingBlIds, catOperationId);

            for (DocumentationListEmptyTbListProjection t : tList) {
                DocumentationEmptyListTbList tbList = new DocumentationEmptyListTbList();

                tbList.setEmptyDocumentId(t.getEmptyDocumentId() != null ? t.getEmptyDocumentId() : null);
                tbList.setDocumentTypeId(t.getDocumentTypeId() != null ? t.getDocumentTypeId() : null);
                tbList.setDocumentTypeAlias(t.getDocumentTypeAlias() != null ? t.getDocumentTypeAlias() : null);
                tbList.setDocumentType(t.getDocumentType() != null ? t.getDocumentType() : null);
                tbList.setDocumentNumber(t.getDocumentNumber() != null ? t.getDocumentNumber() : null);
                tbList.setMovementTypeId(t.getMovementTypeId() != null ? t.getMovementTypeId() : null);
                tbList.setMovementType(t.getMovementType() != null ? t.getMovementType() : null);
                tbList.setShippingLine(t.getShippingLine() != null ? t.getShippingLine() : null);
                tbList.setCommodity(t.getCommodity() != null ? t.getCommodity() : null);
                tbList.setProduct(t.getProduct() != null ? t.getProduct() : null);
                tbList.setOperationTypeId(t.getOperationTypeId() != null ? t.getOperationTypeId() : null);
                tbList.setOperationType(t.getOperationType() != null ? t.getOperationType() : null);
                tbList.setConsignee(t.getConsignee() != null ? t.getConsignee() : null);
                tbList.setShipper(t.getShipper() != null ? t.getShipper() : null);
                tbList.setVessel(t.getVessel() != null ? t.getVessel() : null);
                tbList.setVoyage(t.getVoyage() != null ? t.getVoyage() : null);
                tbList.setUserRegistrationId(t.getUserRegistrationId() != null ? t.getUserRegistrationId() : null);
                tbList.setUserModificationId(t.getUserModificationId() != null ? t.getUserModificationId() : null);
                tbList.setUserRegistrationDate(t.getUserRegistrationDate() != null ? t.getUserRegistrationDate() : null);
                tbList.setUserModificationDate(t.getUserModificationDate() != null ? t.getUserModificationDate() : null);
                tbList.setUserRegistrationName(t.getUserRegistrationName() != null ? t.getUserRegistrationName() : null);
                tbList.setUserRegistrationLastName(t.getUserRegistrationLastName() != null ? t.getUserRegistrationLastName() : null);
                tbList.setUserModificationName(t.getUserModificationName() != null ? t.getUserModificationName() : null);
                tbList.setUserModificationLastName(t.getUserModificationLastName() != null ? t.getUserModificationLastName() : null);
                tbList.setCreationSource(t.getCreationSource() != null ? t.getCreationSource() : null);
                tbList.setQuantityRequested(t.getQuantityRequested() != null ? t.getQuantityRequested() : null);
                tbList.setQuantityAssigned(t.getQuantityAssigned() != null ? t.getQuantityAssigned() : null);
                tbList.setQuantityPending(t.getQuantityPending() != null ? t.getQuantityPending() : null);
                tbList.setDetail(t.getDetail() != null ? t.getDetail() : null);
                tbList.setStatusId(t.getStatusId() != null ? t.getStatusId() : null);
                tbList.setStatus(t.getStatus() != null ? t.getStatus() : null);
                tbList.setCatEstado(t.getCatEstado() != null ? t.getCatEstado() : null);
                tbList.setStatusAlias(t.getStatusAlias() != null ? t.getStatusAlias() : null);
                tbList.setMoveType(t.getMoveType() != null ? t.getMoveType() : null);
                tbList.setRemarkRule(t.getRemarkRule() != null ? t.getRemarkRule() : null);

                Tlist.add(tbList);
            }

            List<Integer> emptyDocumentIds = (Tlist == null) ? new ArrayList<>() :
                    Tlist.stream()
                            .map(DocumentationEmptyListTbList::getEmptyDocumentId)
                            .collect(Collectors.toList());

            List<DocumentationEmptyListTb02Projection> tb02List = cargoDocumentDetailRepository.findEmptyListTb02(emptyDocumentIds, movementTypeGateInId);

            List<DocumentationEmptyListTb02> tb02 = new ArrayList<>();

            // Loop through each item in the projectionList
            for (DocumentationEmptyListTb02Projection projection : tb02List) {
                // Create a new DocumentationEmptyListTb02 object and set its fields based on the projection
                DocumentationEmptyListTb02 mappedItem = new DocumentationEmptyListTb02(
                        projection.getBookingBlId(),
                        projection.getSizeContainerId(),
                        projection.getTypeContainerId(),
                        projection.getEirsInProgressQuantity(),
                        projection.getEirsCompletedQuantity(),
                        projection.getCommodity(),
                        projection.getProductId()
                );

                // Add the mapped item to the result list
                tb02.add(mappedItem);
            }


            List<DocumentationEmptyListTDetailList> tDetailList = convertToTDetailList(tb02);

            updateTList(Tlist, tDetailList, messageType, messageRequested, messageReceived, messagePending);

            updateStatusIdGateIn(Tlist, inProcessDocumentId, completedDocumentId, pendingDocumentId);

        }

        List<Integer> catalogIds = Tlist.stream()
                .map(DocumentationEmptyListTbList::getStatusId)
                .collect(Collectors.toList());

        List<Catalog> catalogList = catalogRepository.findByIds(catalogIds);

        updateStatusAndAlias(Tlist, catalogList, input.getLanguageId());

        long totalRecords = countRecords(Tlist, input.getStatusId());

        List<DocumentationEmptyListTbList> paginatedRecords = getPaginatedRecords(Tlist, input.getStatusId(), input.getPage(), input.getSize());
        List<DocumentationEmptyListResult> resultRows = new ArrayList<>();
        AtomicInteger rowIdGenerator = new AtomicInteger(1);

        for (DocumentationEmptyListTbList t : paginatedRecords) {
            DocumentationEmptyListResult result = convertToResult(t);
            // Assign the sequential row id here
            result.setRowId(rowIdGenerator.getAndIncrement());
            resultRows.add(result);
        }

        DocumentationEmptyListOutput output = new DocumentationEmptyListOutput();
        output.setTotalRecords(List.of(List.of(totalRecords)));
        output.setRows(resultRows);

        return output;
    }

    // Method to update the status and status_alias
    public void updateStatusAndAlias(List<DocumentationEmptyListTbList> tList,
                                     List<Catalog> catalogoList, Integer languageId) {
        // Join with catalogo data based on status_id and update status and status_alias
        Map<Integer, Catalog> catalogoMap = catalogoList.stream()
                .collect(Collectors.toMap(Catalog::getId, catalogo -> catalogo, (existing, replacement) -> existing));

        for (DocumentationEmptyListTbList t : tList) {
            Integer statusId = t.getStatusId();
            Catalog gx = catalogoMap.get(statusId);
            if (gx != null) {
                t.setStatus(catalogLanguageRepository.fnCatalogTranslationDesc(statusId, languageId));  // Translate status using a mock function
                t.setStatusAlias(gx.getAlias());  // Set status_alias from catalogo
            }
        }
    }

    // Method to count the total records where status_id matches a given status_id (or all if null)
    public long countRecords(List<DocumentationEmptyListTbList> tList, Integer statusId) {
        return tList.stream()
                .filter(t -> statusId == null || t.getStatusId().equals(statusId))
                .count();
    }

    // Method to retrieve paginated records based on status_id and page/size parameters
    public List<DocumentationEmptyListTbList> getPaginatedRecords(List<DocumentationEmptyListTbList> tList,
                                                                  Integer statusId, int page, int size) {
        return tList.stream()
                .filter(t -> statusId == null || t.getStatusId().equals(statusId))
                .sorted(Comparator.comparing(DocumentationEmptyListTbList::getUserRegistrationDate).reversed())
                .skip((long) (page - 1) * size)
                .limit(size)
                .collect(Collectors.toList());
    }

    private DocumentationEmptyListResult convertToResult(DocumentationEmptyListTbList t) {
        DocumentationEmptyListResult result = new DocumentationEmptyListResult();
        result.setEmptyDocumentId(t.getEmptyDocumentId() != null ? t.getEmptyDocumentId() : null);
        result.setDocumentTypeId(t.getDocumentTypeId() != null ? t.getDocumentTypeId() : null);
        result.setDocumentTypeAlias(t.getDocumentTypeAlias() != null ? t.getDocumentTypeAlias() : null);
        result.setDocumentTypeDesc(t.getDocumentType() != null ? t.getDocumentType() : null);
        result.setDocumentNumber(t.getDocumentNumber() != null ? t.getDocumentNumber() : null);
        result.setMovementTypeId(t.getMovementTypeId() != null ? t.getMovementTypeId() : null);
        result.setMovementTypeDesc(t.getMovementType() != null ? t.getMovementType() : null);
        result.setShippingLine(t.getShippingLine() != null ? t.getShippingLine() : null);
        result.setCommodity(t.getCommodity() != null ? t.getCommodity() : null);
        result.setProduct(t.getProduct() != null ? t.getProduct() : null);
        result.setOperationTypeId(t.getOperationTypeId() != null ? t.getOperationTypeId() : null);
        result.setOperationType(t.getOperationType() != null ? t.getOperationType() : null);
        result.setConsignee(t.getConsignee() != null ? t.getConsignee() : null);
        result.setShipper(t.getShipper() != null ? t.getShipper() : null);
        result.setVessel(t.getVessel() != null ? t.getVessel() : null);
        result.setVoyage(t.getVoyage() != null ? t.getVoyage() : null);
        result.setUserRegistrationId(t.getUserRegistrationId() != null ? t.getUserRegistrationId() : null);
        result.setUserModificationId(t.getUserModificationId() != null ? t.getUserModificationId() : null);
        result.setUserRegistrationDate(t.getUserRegistrationDate() != null ? t.getUserRegistrationDate().toString() : null);
        result.setUserModificationDate(t.getUserModificationDate() != null ? t.getUserModificationDate().toString() : null);
        result.setUserRegistrationName(t.getUserRegistrationName() != null ? t.getUserRegistrationName() : null);
        result.setUserRegistrationLastname(t.getUserRegistrationLastName() != null ? t.getUserRegistrationLastName() : null);
        result.setUserModificationName(t.getUserModificationName() != null ? t.getUserModificationName() : null);
        result.setUserModificationLastname(t.getUserModificationLastName() != null ? t.getUserModificationLastName() : null);
        result.setCreationSource(t.getCreationSource() != null ? t.getCreationSource() : null);
        result.setQuantityRequested(t.getQuantityRequested() != null ? t.getQuantityRequested() : null);
        result.setQuantityAssigned(t.getQuantityAssigned() != null ? t.getQuantityAssigned() : null);
        result.setQuantityPending(t.getQuantityPending() != null ? t.getQuantityPending() : null);
        result.setDetail(t.getDetail() != null ? t.getDetail() : null);
        result.setStatusId(t.getStatusId() != null ? t.getStatusId() : null);
        result.setStatus(t.getStatus() != null ? t.getStatus() : null);
        result.setStatusAlias(t.getStatusAlias() != null ? t.getStatusAlias() : null);
        result.setMoveType(t.getMoveType() != null ? t.getMoveType() : null);
        result.setRemarkRule(t.getRemarkRule() != null ? t.getRemarkRule() : null);
        result.setCatEstado(t.getCatEstado() != null ? t.getCatEstado() : null);
        return result;
    }

    //----------------------------------------------------------------------
    public List<DocumentationEmptyListTContainers> processContainers(String containersJson) {
        List<DocumentationEmptyListTContainers> tContainers = new ArrayList<>();

        // Parse the input JSON array using Jackson
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(containersJson);

            // Convert JSON to TContainer objects
            for (JsonNode node : jsonNode) {
                DocumentationEmptyListTContainers tContainer = new DocumentationEmptyListTContainers();
                tContainer.setContainerId(null);  // Start with null for the container_id field
                tContainer.setContainer(node.asText()); // Set the container value from JSON

                tContainers.add(tContainer);
            }

            // Extract the list of container numbers from the tContainers
            List<String> containerNumbers = tContainers.stream()
                    .map(DocumentationEmptyListTContainers::getContainer)
                    .collect(Collectors.toList());

            // Fetch only the containers from the database that match the list of container numbers
            List<Container> containersFromDb = containerRepository.findByContainerNumbers(containerNumbers);

            // Update the TContainer list with container_id from the "contenedor" table
            for (DocumentationEmptyListTContainers tContainer : tContainers) {
                for (Container container : containersFromDb) {
                    if (container.getContainerNumber().equals(tContainer.getContainer())) {
                        tContainer.setContainerId(container.getId());
                        break;
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();  // Handle exception appropriately
        }

        return tContainers;
    }

    public List<Integer> processBookingBlIds(List<DocumentationEmptyListTContainers> tContainers, Integer movementTypeId, Integer movementTypeGateOutId, Integer subUnidadNegocioId) {
        List<Integer> bookingBlIds = new ArrayList<>();

        // Step 1: Check if movement type is "gate out"
        if (movementTypeId.equals(movementTypeGateOutId)) {
            // Step 2: If T_containers is not empty, perform the query
            if (!tContainers.isEmpty()) {
                // Step 3: Using JPA to simulate the SQL query for getting distinct booking_ids
                List<CargoDocumentDetail> documentoCargaDetalles = cargoDocumentDetailRepository.findByContainerIdIn(
                        tContainers.stream().map(DocumentationEmptyListTContainers::getContainerId).collect(Collectors.toList())
                );

                // Step 4: Filter the relevant documentoCargaDetalles and join with other entities
                List<Booking> bookings = new ArrayList<>();
                for (CargoDocumentDetail dcd : documentoCargaDetalles) {
                    // Get the associated DocumentoCarga and its related BookingDetalle
                    CargoDocument documentoCarga = cargoDocumentRepository.findById(dcd.getId()).orElse(null);
                    if (documentoCarga != null && documentoCarga.getActive()) {
                        BookingDetail bookingDetalle = bookingDetailRepository.findById(dcd.getId()).orElse(null);
                        if (bookingDetalle != null && bookingDetalle.getActive()) {
                            Booking booking = bookingRepository.findById(bookingDetalle.getId()).orElse(null);
                            if (booking != null && booking.getActive() && booking.getSubBusinessUnit().getId().equals(subUnidadNegocioId)) {
                                bookings.add(booking);
                            }
                        }
                    }
                }

                // Step 5: Collect distinct booking_id from the filtered bookings
                bookingBlIds = bookings.stream()
                        .map(Booking::getId)
                        .distinct()
                        .collect(Collectors.toList());
            }
        }

        // Step 6: Return the list of distinct booking IDs
        return bookingBlIds;
    }

    public List<DocumentationEmptyListTb02> processEirData(List<DocumentationEmptyListTbList> tList, Integer isGateOut, Integer containerNoCntId,
                                                           Integer isTypeContainerDry, Integer isTypeContainerHC) {
        // Extract all emptyDocumentIds from TListDTO to match against booking_id_gout_light
        List<Integer> emptyDocumentIds = tList.stream()
                .map(DocumentationEmptyListTbList::getEmptyDocumentId)
                .collect(Collectors.toList());

        // Fetch all matching records from the EirRepository
        List<Eir> eirRecords = eirRepository.getEirByBookingGout(emptyDocumentIds, isGateOut);

        // Now perform the in-memory operations
        Map<String, DocumentationEmptyListTb02> resultMap = new HashMap<>();

        for (Eir eirx : eirRecords) {
            // Create the key for aggregation (e.g., bookingIdGoutLight + sizeContainerId + typeContainerId)
            String key = eirx.getBookingGout().getId() + "_" + eirx.getCatSizeCnt().getId() + "_"
                    + determineTypeContainer(eirx, tList, isTypeContainerDry, isTypeContainerHC);

            // Initialize or retrieve the result DTO for this combination
            DocumentationEmptyListTb02 resultDTO = resultMap.getOrDefault(key, new DocumentationEmptyListTb02());

            // Set or aggregate values
            resultDTO.setBookingBlId(eirx.getBookingGout().getId());
            resultDTO.setSizeContainerId(eirx.getCatSizeCnt().getId());
            resultDTO.setTypeContainerId(determineTypeContainer(eirx, tList, isTypeContainerDry, isTypeContainerHC));

            // Calculate eirsInProgressQuantity
            if (eirx.getContainer().getId().equals(containerNoCntId)) {
                resultDTO.setEirsInProgressQuantity(Optional.ofNullable(resultDTO.getEirsInProgressQuantity()).orElse(0)  + 1);
            } else {
                resultDTO.setEirsCompletedQuantity(
                        Optional.ofNullable(resultDTO.getEirsCompletedQuantity()).orElse(0) + 1);
            }

            // Store back the aggregated result
            resultMap.put(key, resultDTO);
        }

        // Convert the results map to a list
        return new ArrayList<>(resultMap.values());
    }

    private Integer determineTypeContainer(Eir eirx, List<DocumentationEmptyListTbList> tList, Integer isTypeContainerDry, Integer isTypeContainerHC) {
        // Find the remarkRule for the given emptyDocumentId
        String remarkRule = tList.stream()
                .filter(t -> t.getEmptyDocumentId().equals(eirx.getBookingGout().getId()))
                .map(DocumentationEmptyListTbList::getRemarkRule)
                .findFirst()
                .orElse("");

        // Apply the same logic from the SQL code
        if ("FLAG_TO_FLEX".equals(remarkRule) && Arrays.asList(isTypeContainerDry, isTypeContainerHC).contains(eirx.getCatContainerType().getId())) {
            return isTypeContainerDry; // Map to single type
        } else {
            return eirx.getCatContainerType().getId(); // Original container type
        }
    }

    public List<DocumentationEmptyListTDetailList> processBookingDetails(List<DocumentationEmptyListTb01> tb01,
                                                                         List<DocumentationEmptyListTb02> tb02,
                                                                         List<Integer> sizeIds,
                                                                         List<Integer> typeIds) {
        // Fetch the catalog data for size and type containers
        List<Catalog> sizeCatalogs = catalogRepository.findByIds(sizeIds);
        List<Catalog> typeCatalogs = catalogRepository.findByIds(typeIds);

        // Create a map for quick lookup of descriptions by catalog ID
        Map<Integer, String> sizeDescriptionMap = sizeCatalogs.stream()
                .collect(Collectors.toMap(Catalog::getId, Catalog::getDescription, (existing, replacement) -> existing));

        Map<Integer, String> typeDescriptionMap = typeCatalogs.stream()
                .collect(Collectors.toMap(Catalog::getId, Catalog::getDescription, (existing, replacement) -> existing));

        // Create a map for tb02 data based on the join conditions
        Map<String, DocumentationEmptyListTb02> bkdMap = tb02.stream()
                .collect(Collectors.toMap(
                        bkd -> bkd.getBookingBlId() + "_" + bkd.getSizeContainerId() + "_" + bkd.getTypeContainerId(),
                        bkd -> bkd,
                        // Merge function to handle duplicate keys - keep the first entry
                        (existing, replacement) -> existing
                ));

        // Prepare the result list for T_detail_list
        List<DocumentationEmptyListTDetailList> detailList = new ArrayList<>();

        // Iterate over tb01 (bkt) and perform necessary calculations
        for (DocumentationEmptyListTb01 bkt : tb01) {
            String key = bkt.getBookingBlId() + "_" + bkt.getSizeContainerId() + "_" + bkt.getTypeContainerId();

            // Get the size and type descriptions from the catalog data
            String sizeDescription = sizeDescriptionMap.getOrDefault(bkt.getSizeContainerId(), "Unknown");
            String typeDescription = typeDescriptionMap.getOrDefault(bkt.getTypeContainerId(), "Unknown");

            // Lookup the matching assigned quantity from tb02 (bkd)
            DocumentationEmptyListTb02 bkd = bkdMap.getOrDefault(key, new DocumentationEmptyListTb02());

            // Calculate the requested, assigned, and pending quantities
            Integer quantityRequested = bkt.getTotalQuantity() != null ? bkt.getTotalQuantity() : 0;
            Integer quantityAssigned = (bkd.getEirsInProgressQuantity() != null ? bkd.getEirsInProgressQuantity() : 0) +
                    (bkd.getEirsCompletedQuantity() != null ? bkd.getEirsCompletedQuantity() : 0);
            Integer quantityPending = quantityRequested - quantityAssigned;

            // Create a new result DTO for the detail list
            DocumentationEmptyListTDetailList detail = new DocumentationEmptyListTDetailList();
            detail.setEmptyDocumentId(bkt.getBookingBlId());
            detail.setSizeContainer(sizeDescription);
            detail.setTypeContainer(typeDescription);
            detail.setQuantityRequested(quantityRequested);
            detail.setQuantityAssigned(quantityAssigned);
            detail.setQuantityPending(quantityPending);
            detail.setRemarkRule(bkt.getRemarkRule());

            // Add the result to the list
            detailList.add(detail);
        }

        return detailList;
    }

    public void updateTList(List<DocumentationEmptyListTbList> tList, List<DocumentationEmptyListTDetailList> tDetailList,
                            String messageType, String messageRequested, String messageAssigned, String messagePending) {

        // Step 1: Aggregate the quantities for each empty_document_id in tDetailList
        Map<Integer, AggregatedData> aggregatedDataMap = new HashMap<>();

        for (DocumentationEmptyListTDetailList detail : tDetailList) {
            Integer emptyDocumentId = detail.getEmptyDocumentId();

            AggregatedData data = aggregatedDataMap.getOrDefault(emptyDocumentId, new AggregatedData());
            data.setTQuantityRequested(data.getTQuantityRequested() + (detail.getQuantityRequested() != null ? detail.getQuantityRequested() : 0));
            data.setTQuantityAssigned(data.getTQuantityAssigned() + (detail.getQuantityAssigned() != null ? detail.getQuantityAssigned() : 0));
            data.setTQuantityPending(data.getTQuantityPending() + (detail.getQuantityPending() != null ? detail.getQuantityPending() : 0));

            // Generate the JSON-like detail for the empty_document_id with escaped backslashes
            String jsonDetail = "{\"item\":\"" + messageType + " " + detail.getSizeContainer() + " " +
                    (detail.getRemarkRule().equals("FLAG_TO_FLEX") ? "DC/HC" : detail.getTypeContainer()) + " | " +
                    messageRequested + formatNumber(detail.getQuantityRequested()) + " | " +
                    messageAssigned + formatNumber(detail.getQuantityAssigned()) + " | " +
                    messagePending + formatNumber(detail.getQuantityPending()) + "\"}";

            // Add the JSON detail to the list for this empty_document_id
            data.getTDetail().add(jsonDetail);

            aggregatedDataMap.put(emptyDocumentId, data);
        }

        // Step 2: Update the tList based on aggregated data
        for (DocumentationEmptyListTbList t : tList) {
            AggregatedData aggregatedData = aggregatedDataMap.get(t.getEmptyDocumentId());

            if (aggregatedData != null) {
                t.setQuantityRequested(aggregatedData.getTQuantityRequested());
                t.setQuantityAssigned(aggregatedData.getTQuantityAssigned());
                t.setQuantityPending(aggregatedData.getTQuantityPending());
                t.setDetail(generateDetailJson(aggregatedData.getTDetail()));
            } else {
                // In case of no aggregated data, set a default detail with zeros
                String defaultJsonDetail = "{\"item\":\"" + messageType + " 0 " +
                        " | " + messageRequested + formatNumber(0) + " | " +
                        messageAssigned + formatNumber(0) + " | " +
                        messagePending + formatNumber(0) + "\"}";

                t.setQuantityRequested(0);
                t.setQuantityAssigned(0);
                t.setQuantityPending(0);
                t.setDetail("[" + defaultJsonDetail + "]");
            }
        }
    }

    // Utility method to format numbers like in SQL (e.g., FORMAT(number, '0'))
    private String formatNumber(Integer number) {
        return String.format("%d", number != null ? number : 0);
    }

    // Helper method to generate the final JSON-like string for the detail
    private String generateDetailJson(List<String> tDetail) {
        return tDetail.isEmpty() ? "[]" : "[" + String.join(",", tDetail) + "]";
    }

    // AggregatedData class to hold the aggregated values and JSON details
    private static class AggregatedData {
        private Integer tQuantityRequested = 0;
        private Integer tQuantityAssigned = 0;
        private Integer tQuantityPending = 0;
        private List<String> tDetail = new ArrayList<>();

        // Getters and Setters
        public Integer getTQuantityRequested() {
            return tQuantityRequested;
        }

        public void setTQuantityRequested(Integer tQuantityRequested) {
            this.tQuantityRequested = tQuantityRequested;
        }

        public Integer getTQuantityAssigned() {
            return tQuantityAssigned;
        }

        public void setTQuantityAssigned(Integer tQuantityAssigned) {
            this.tQuantityAssigned = tQuantityAssigned;
        }

        public Integer getTQuantityPending() {
            return tQuantityPending;
        }

        public void setTQuantityPending(Integer tQuantityPending) {
            this.tQuantityPending = tQuantityPending;
        }

        public List<String> getTDetail() {
            return tDetail;
        }

        public void setTDetail(List<String> tDetail) {
            this.tDetail = tDetail;
        }
    }


    public void updateStatusId(List<DocumentationEmptyListTbList> tList,
                               Integer inProcessDocumentId, Integer completedDocumentId,
                               Integer pendingDocumentId, Integer catEstadoCanceladoId) {

        for (DocumentationEmptyListTbList t : tList) {
            // First UPDATE: Set status_id based on quantity_requested, quantity_assigned, and quantity_pending
            if (t.getQuantityRequested() > 0 && t.getQuantityAssigned() > 0 && t.getQuantityPending() > 0) {
                t.setStatusId(inProcessDocumentId); // In Process
            } else if (t.getQuantityRequested() > 0 && t.getQuantityAssigned() > 0 && t.getQuantityPending() == 0) {
                t.setStatusId(completedDocumentId); // Completed
            } else {
                t.setStatusId(pendingDocumentId); // Pending
            }

            // Second UPDATE: Set status_id to cat_estado if cat_estado matches cancelado_id
            if (t.getCatEstado() != null && t.getCatEstado().equals(catEstadoCanceladoId)) {
                t.setStatusId(t.getCatEstado()); // Cancelado
            }
        }
    }

    //--------------------GATE IN ------------------
    public List<Integer> processBookingBlIdsGateIn(List<DocumentationEmptyListTContainers> tContainers, Integer movementTypeId, Integer movementTypeGateInId, Integer subUnidadNegocioId) {
        List<Integer> bookingBlIds = new ArrayList<>();

        // Step 1: Check if movement type is "Gate In"
        if (movementTypeId.equals(movementTypeGateInId)) {
            // Step 2: If T_containers is not empty, proceed with the logic
            if (!tContainers.isEmpty()) {
                // Step 3: Extract container IDs from T_containers list
                List<Integer> containerIds = tContainers.stream()
                        .map(DocumentationEmptyListTContainers::getContainerId)
                        .collect(Collectors.toList());

                // Step 4: Query documento_carga_detalle based on container_ids
                List<CargoDocumentDetail> documentDetails = cargoDocumentDetailRepository.findByContainerIdIn(containerIds);

                // Step 5: Filter the relevant DocumentoCarga based on the conditions
                Set<Integer> documentoCargaIds = new HashSet<>();
                for (CargoDocumentDetail documentDetail : documentDetails) {
                    // Query for DocumentoCarga related to the documentDetail
                    CargoDocument documentoCarga = cargoDocumentRepository.findById(documentDetail.getId()).orElse(null);
                    if (documentoCarga != null && documentoCarga.getActive()) {
                        // Check the relationship with ProgramacionNaveDetalle and ProgramacionNave
                        VesselProgrammingDetail pnd = vesselProgrammingDetailRepository
                                .findById(documentoCarga.getVesselProgrammingDetail().getId())
                                .orElse(null);
                        if (pnd != null) {
                            VesselProgramming pna = vesselProgrammingRepository.findById(pnd.getVesselProgramming().getId()).orElse(null);
                            if (pna != null && pna.getSubBusinessUnit().getId().equals(subUnidadNegocioId)) {
                                // Check if booking_detalle_id is NULL
                                if (documentDetail.getBookingDetail().getId() == null) {
                                    documentoCargaIds.add(documentoCarga.getId());
                                }
                            }
                        }
                    }
                }

                // Step 6: Collect distinct documento_carga_id into the result list
                bookingBlIds.addAll(documentoCargaIds);
            }
        }

        // Step 7: Return the list of distinct documento_carga_ids
        return new ArrayList<>(bookingBlIds);
    }

    public List<DocumentationEmptyListTDetailList> convertToTDetailList(List<DocumentationEmptyListTb02> tb02) {
        // Fetch the catalog data for size and type containers
        List<Catalog> sizeCatalogs = catalogRepository.findByIds(
                tb02.stream().map(DocumentationEmptyListTb02::getSizeContainerId).distinct().collect(Collectors.toList())
        );
        List<Catalog> typeCatalogs = catalogRepository.findByIds(
                tb02.stream().map(DocumentationEmptyListTb02::getTypeContainerId).distinct().collect(Collectors.toList())
        );

        // Create a map for quick lookup of descriptions by catalog ID
        Map<Integer, String> sizeDescriptionMap = sizeCatalogs.stream()
                .collect(Collectors.toMap(Catalog::getId, Catalog::getDescription, (existing, replacement) -> existing));

        Map<Integer, String> typeDescriptionMap = typeCatalogs.stream()
                .collect(Collectors.toMap(Catalog::getId, Catalog::getDescription, (existing, replacement) -> existing));

        // Prepare the result list for T_detail_list
        List<DocumentationEmptyListTDetailList> detailList = new ArrayList<>();

        for (DocumentationEmptyListTb02 bkd : tb02) {
            String sizeDescription = sizeDescriptionMap.getOrDefault(bkd.getSizeContainerId(), "Unknown");
            String typeDescription = typeDescriptionMap.getOrDefault(bkd.getTypeContainerId(), "Unknown");

            Integer quantityRequested = bkd.getEirsInProgressQuantity() != null ? bkd.getEirsInProgressQuantity() : 0;
            Integer quantityAssigned = bkd.getEirsCompletedQuantity() != null ? bkd.getEirsCompletedQuantity() : 0;
            Integer quantityPending = quantityRequested - quantityAssigned;

            DocumentationEmptyListTDetailList detail = new DocumentationEmptyListTDetailList();
            detail.setEmptyDocumentId(bkd.getBookingBlId());
            detail.setSizeContainer(sizeDescription);
            detail.setTypeContainer(typeDescription);
            detail.setQuantityRequested(quantityRequested);
            detail.setQuantityAssigned(quantityAssigned);
            detail.setQuantityPending(quantityPending);
            detail.setRemarkRule("");
            detailList.add(detail);
        }

        return detailList;
    }

    public void updateStatusIdGateIn(List<DocumentationEmptyListTbList> tList,
                                     Integer inProcessDocumentId, Integer completedDocumentId,
                                     Integer pendingDocumentId) {

        for (DocumentationEmptyListTbList t : tList) {
            int quantityRequested = (t.getQuantityRequested() != null) ? t.getQuantityRequested() : 0;
            int quantityAssigned = (t.getQuantityAssigned() != null) ? t.getQuantityAssigned() : 0;
            int quantityPending = (t.getQuantityPending() != null) ? t.getQuantityPending() : 0;

            // First UPDATE: Set status_id based on quantity_requested,
            // quantity_assigned, and quantity_pending
            if (quantityRequested > 0 && quantityAssigned > 0 && quantityPending > 0) {
                t.setStatusId(inProcessDocumentId); // In Process
            } else if (quantityRequested > 0 && quantityAssigned > 0 && quantityPending == 0) {
                t.setStatusId(completedDocumentId); // Completed
            } else {
                t.setStatusId(pendingDocumentId); // Pending
            }
        }
    }

}