package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class BookingDetailsListInput {

    @Data
    public static class Input {
        @JsonProperty("booking_id")
        @NotNull(message = "booking_id cannot be null")
        private Integer bookingId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}