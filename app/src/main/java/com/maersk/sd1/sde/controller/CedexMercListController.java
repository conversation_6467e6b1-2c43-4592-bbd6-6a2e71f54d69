package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.CedexMercListInput;
import com.maersk.sd1.sde.dto.CedexMercListOutputDTO;
import com.maersk.sd1.sde.service.CedexMercListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Controller to handle requests for listing CedexMerc data.
 * This replicates the logic of the stored procedure sde.cedex_merc_list.
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDE/module/sde/SDECedexMercServiceImp")
public class CedexMercListController {

    private static final Logger logger = LogManager.getLogger(CedexMercListController.class);
    private final CedexMercListService cedexMercListService;

    @PostMapping("/sdecedexMercList")
    public ResponseEntity<ResponseController<CedexMercListOutputDTO>> cedexMercList(@RequestBody @Valid CedexMercListInput.Root request) {
        logger.info("Request received cedexMercList: {}", request);
        CedexMercListOutputDTO outputDTO;
        try {
            CedexMercListInput.Input in = request.getPrefix().getInput();

            outputDTO = cedexMercListService.listCedexMerc(in);
        } catch (Exception e) {
            logger.error("An error occurred while processing cedexMercList request.", e);
            outputDTO = new CedexMercListOutputDTO();
            List<List<Long>> totalRegistros = new ArrayList<>();
            totalRegistros.add(List.of(0L));
            outputDTO.setTotalRegistros(totalRegistros);
        }
        return ResponseEntity.ok(new ResponseController<>(outputDTO));
    }
}