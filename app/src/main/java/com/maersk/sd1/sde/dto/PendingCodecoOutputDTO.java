package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PendingCodecoOutputDTO {

    @JsonProperty("seteo_edi_codeco_id")
    private Integer setupEdiCodecoId;

    @JsonProperty("lista_edi_codeco_id")
    private String ediCodecoListId;

    @JsonProperty("nombre_archivo")
    private String fileName;

    @JsonProperty("contenido")
    private String content;

    @JsonProperty("correo_destinatario")
    private String recipientEmail;

    @JsonProperty("tipo_estructura_id")
    private Integer structureTypeId;
}
