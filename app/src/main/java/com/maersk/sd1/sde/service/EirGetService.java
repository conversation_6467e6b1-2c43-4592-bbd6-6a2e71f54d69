package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class EirGetService {

    private final ContainerRepository containerRepository;
    private final CatalogRepository catalogRepository;
    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final EirRepository eirRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final ConteconLogRepository conteconLogRepository;
    private final EirMultipleRepository eirMultipleRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final EirZoneRepository eirZoneRepository;
    private final EirObservationInspectorRepository eirObservationInspectorRepository;
    private final GateTransmissionRepository gateTransmissionRepository;

    private String formatDateTime;
    private String formatDate;

    public EirGetOutput getEirData(Integer subBusinessUnitId, Integer eirId, Integer languageId) {

        Integer containerDummyId= containerRepository.findContainerDummyId();
        Integer isFull= catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);

        EirGetOutput output= new EirGetOutput();

        List<DocumentDTO> docs=eirDocumentCargoDetailRepository.findDocumentsByEirId(eirId);

        log.info("Documents found: {}", docs);

        Optional<Eir> optionalEir = eirRepository.findById(eirId);
        if (optionalEir.isEmpty()) {
            return output;
        }
        String checksRevisionLight=optionalEir.get().getChecksRevisionLight();

        Integer containerId = optionalEir.map(Eir::getContainer)
                .map(Container::getId)
                .orElse(null);

        Integer businessUnitId = optionalEir.map(Eir::getBusinessUnit)
                .map(BusinessUnit::getId)
                .orElse(null);

        if (containerId == null || businessUnitId == null) {
            log.warn("EIR {} is missing container or business unit", eirId);
            return output;
        }

        log.info("Checks revision light: {}", checksRevisionLight);
        log.info("Container id: {}", containerId);

        getFormats(businessUnitId);

        List<TbRestrictionsProjection> restrictions= containerRestrictionRepository.findRestrictionsByContainerId(containerId,subBusinessUnitId,formatDate);

        Integer idPreGateReception=conteconLogRepository.findTopByEirIdAndConditions(eirId);

        List<EirGetOutput.EirMainData> eirMainDataList= generateEirMainOutput(eirId,languageId,containerDummyId,subBusinessUnitId,isFull,idPreGateReception,docs,restrictions);
        output.setEirMainData(eirMainDataList);

        List<String> containerAssociations= eirMultipleRepository.findAssosciatedContainerByEirId(eirId);
        output.setContenedoresAsociados(containerAssociations);

        List<String> checksRevisionLightList= catalogLanguageRepository.getCheckRevisionLight(checksRevisionLight,languageId);
        output.setRevisionLightOptions(checksRevisionLightList);

        List<EirGetOutput.EirActivityZoneData> activities = generateEirActivityZoneOutput(eirId, languageId, subBusinessUnitId, formatDateTime);
        output.setActivities(activities);

        List<EirGetOutput.EirZoneData> zones = generateEirZoneOutput(eirId, languageId, subBusinessUnitId, formatDateTime);
        output.setZones(zones);

        List<EirGetOutput.EirObservationInspectorData> observations = generateObservationOutput(eirId, languageId, 0);
        output.setObservacionesInspectorSecos(observations);

        List<EirGetOutput.EirObservationInspectorData> observationsReefer= generateObservationOutput(eirId,languageId,1);
        output.setObservacionesInspectorReefer(observationsReefer);

        List<EirGetOutput.EirEdiCodecoData> ediCodecos= generateEdicodecoOutput(eirId,subBusinessUnitId,languageId);
        output.setEdiCodecoList(ediCodecos);

        List<EirGetOutput.EirGateDriverPhotoDTO> driverPhotos= generateDriverPhotosOutput(eirId);
        output.setDriverPhotos(driverPhotos);

        return output;
    }

    private void getFormats(Integer businessUnitId) {
        formatDateTime = businessUnitRepository.getFormatoDateTime(businessUnitId);
        formatDate = businessUnitRepository.getFormatoDate(businessUnitId);
    }

    List<EirGetOutput.EirMainData> generateEirMainOutput(Integer eirId, Integer languageId, Integer containerDummyId, Integer subBusinessUnitId, Integer isFull, Integer idPreGateReception, List<DocumentDTO> docs, List<TbRestrictionsProjection> restrictions) {

        List<EirDataProjection> eirData= eirRepository.findEirData(eirId,subBusinessUnitId,languageId,formatDateTime,formatDate,isFull,containerDummyId,idPreGateReception);

        List<EirGetOutput.EirMainData> eirMainDataList= new ArrayList<>();

        for(EirDataProjection data:eirData){
            EirGetOutput.EirMainData eirMainData= new EirGetOutput.EirMainData();
            eirMainData.setEirId(data.getEirId());
            eirMainData.setTipoGate(data.getTipoGate());
            eirMainData.setTipoMovimiento(data.getTipoMovimiento());
            eirMainData.setFechaIngresoCamion(data.getFechaIngresoCamion());
            eirMainData.setTruckInDate(data.getTruckInDate());
            eirMainData.setFechaSalidaCamion(data.getFechaSalidaCamion());
            eirMainData.setTruckOutDate(data.getTruckOutDate());
            eirMainData.setEirLocal(data.getEirLocal());
            eirMainData.setNaveViajeOperacion(data.getNaveViajeOperacion());
            eirMainData.setContenedorId(data.getContenedorId());
            eirMainData.setNumeroContenedor(data.getNumeroContenedor());
            eirMainData.setCntSizeCatalogId(data.getCntSizeCatalogId());
            eirMainData.setTamanoCnt(data.getTamanoCnt());
            eirMainData.setCntTypeCatalogId(data.getCntTypeCatalogId());
            eirMainData.setTipoContenedor(data.getTipoContenedor());
            eirMainData.setCodigoIsoId(data.getCodigoIsoId());
            eirMainData.setCodigoIsoDesc(data.getCodigoIsoDesc());
            eirMainData.setLineaNavieraCnt(data.getLineaNavieraCnt());
            eirMainData.setClaseCnt(data.getClaseCnt());
            eirMainData.setTaraCnt(data.getTaraCnt());
            eirMainData.setCargaMaximaCnt(data.getCargaMaximaCnt());
            eirMainData.setFFabricacion(data.getFFabricacion());
            eirMainData.setTipoReeferId(data.getTipoReeferId());
            eirMainData.setTipoReefer(data.getTipoReefer());
            eirMainData.setMarcaMotorId(data.getMarcaMotorId());
            eirMainData.setMarcaMotor(data.getMarcaMotor());
            eirMainData.setPrecinto1(data.getPrecinto1());
            eirMainData.setPrecinto2(data.getPrecinto2());
            eirMainData.setPrecinto3(data.getPrecinto3());
            eirMainData.setPrecinto4(data.getPrecinto4());
            eirMainData.setCliente(data.getCliente());
            eirMainData.setVehiculo(data.getVehiculo());
            eirMainData.setEmpresaTransporteId(data.getEmpresaTransporteId());
            eirMainData.setEmpresaTransporteNombre(data.getEmpresaTransporteNombre());
            eirMainData.setConductor(data.getConductor());
            eirMainData.setDriverDocument(data.getDriverDocument());
            eirMainData.setDriverName(data.getDriverName());
            eirMainData.setDriverLastname(data.getDriverLastname());
            eirMainData.setEstructuraConDano(data.getEstructuraConDano());
            eirMainData.setMaquinariaConDano(data.getMaquinariaConDano());
            eirMainData.setComentarios(data.getComentarios());
            eirMainData.setFFinalizeGrua(data.getFFinalizeGrua());
            eirMainData.setUbicacionPatio(data.getUbicacionPatio());
            eirMainData.setFsobrestadia(data.getFsobrestadia());
            eirMainData.setFInspeccion(data.getFInspeccion());
            eirMainData.setNombreInspector(data.getNombreInspector());
            eirMainData.setNombreTecnico(data.getNombreTecnico());
            eirMainData.setFAsignacion(data.getFAsignacion());
            eirMainData.setControlInspection(data.getControlInspection());
            eirMainData.setDocumentoReferencia(data.getDocumentoReferencia());
            eirMainData.setCita(data.getCita());
            eirMainData.setParaVenta(data.getParaVenta());
            eirMainData.setSituacionRepEstructura(data.getSituacionRepEstructura());
            eirMainData.setObsAprobRepEstructura(data.getObsAprobRepEstructura());
            eirMainData.setSituacionRepMaquinaria(data.getSituacionRepMaquinaria());
            eirMainData.setObsAprobRepMaquinaria(data.getObsAprobRepMaquinaria());
            eirMainData.setEstructuraDanadaHistorico(data.getEstructuraDanadaHistorico());
            eirMainData.setMaquinariaDanadaHistorico(data.getMaquinariaDanadaHistorico());
            eirMainData.setCitasOnlineDpwCallao(data.getCitasOnlineDpwCallao());
            eirMainData.setCitasApmtCallaoExpo(data.getCitasApmtCallaoExpo());
            eirMainData.setNumeracion(data.getNumeracion());
            eirMainData.setRectificacion(data.getRectificacion());
            eirMainData.setEliminacion(data.getEliminacion());
            eirMainData.setWeightGoods(data.getWeightGoods());
            eirMainData.setCatMeasureWeightId(data.getCatMeasureWeightId());
            eirMainData.setTipoUnidadPeso(data.getTipoUnidadPeso());
            eirMainData.setPeso(data.getPeso());
            eirMainData.setOperation(data.getOperation());
            eirMainData.setTara(data.getTara());
            eirMainData.setCargaMaxima(data.getCargaMaxima());
            eirMainData.setCatMeansTransportId(data.getCatMeansTransportId());
            eirMainData.setMedioTransporte(data.getMedioTransporte());
            eirMainData.setTransportPlanningDetailFullId(data.getTransportPlanningDetailFullId());
            eirMainData.setFlagChassisStayed(data.getFlagChassisStayed());
            eirMainData.setFlagChassisPickup(data.getFlagChassisPickup());
            eirMainData.setEirChassisReference(data.getEirChassisReference());
            eirMainData.setDepartureCancelReason(data.getDepartureCancelReason());
            eirMainData.setCatMovementId(data.getCatMovementId());
            eirMainData.setCatEmptyFullId(data.getCatEmptyFullId());
            eirMainData.setChassisNumber(data.getChassisNumber());
            eirMainData.setEirChassisId(data.getEirChassisId());

            if (!docs.isEmpty()) {
                DocumentDTO topDoc = docs.getFirst();
                String documentoTipo = topDoc.getDocumentType() + " " + docs.stream()
                        .map(DocumentDTO::getDocument)
                        .sorted(Comparator.reverseOrder())
                        .collect(Collectors.joining(", "));
                eirMainData.setDocumentoTipo(documentoTipo);
                eirMainData.setDocumento(topDoc.getDocument());
                eirMainData.setLineaNavieraDoc(topDoc.getLine());
            }

            if (!restrictions.isEmpty()) {
                String restrictionDetails = restrictions.stream()
                        .map(TbRestrictionsProjection::getRestrictionDetails)
                        .collect(Collectors.joining(", "));
                eirMainData.setRestringido("Yes " + restrictionDetails);
            } else {
                eirMainData.setRestringido("No");
            }

            eirMainDataList.add(eirMainData);
        }

        return eirMainDataList;

    }

    List<EirGetOutput.EirActivityZoneData> generateEirActivityZoneOutput(Integer eirId, Integer languageId, Integer subBusinessUnitId, String formatDateTime) {
        List<EirActivityZoneProjection> eirActivityZones = eirActivityZoneRepository.findEirActivityZoneByEirId(eirId,languageId,subBusinessUnitId,formatDateTime);
        List<EirGetOutput.EirActivityZoneData> eirActivityZoneDataList = new ArrayList<>();
        for (EirActivityZoneProjection eirActivityZone : eirActivityZones) {
            EirGetOutput.EirActivityZoneData eirActivityZoneData = new EirGetOutput.EirActivityZoneData();

            eirActivityZoneData.setActividad(eirActivityZone.getActividad());
            eirActivityZoneData.setConcluido(eirActivityZone.getConcluido());
            eirActivityZoneData.setEstructuraDanada(eirActivityZone.getEstructuraDanada());
            eirActivityZoneData.setMaquinariaDanada(eirActivityZone.getMaquinariaDanada());
            eirActivityZoneData.setZonaResultado(eirActivityZone.getZonaResultado());
            eirActivityZoneData.setFechaInicio(eirActivityZone.getFInicio());
            eirActivityZoneData.setFechaTermino(eirActivityZone.getFTermino());
            eirActivityZoneData.setSensor(eirActivityZone.getSensor());
            eirActivityZoneData.setRcd(eirActivityZone.getRcd());
            eirActivityZoneData.setInspeccionCompleja(eirActivityZone.getInspeccionCompleja());
            eirActivityZoneData.setComplejidadDano(eirActivityZone.getComplejidadDano());
            eirActivityZoneData.setInspeccionParcial(eirActivityZone.getInspeccionParcial());
            eirActivityZoneData.setLavadoEspecial(eirActivityZone.getLavadoEspecial());
            eirActivityZoneData.setBloquearContenedor(eirActivityZone.getBloquearContenedor());
            eirActivityZoneData.setPrecinto(eirActivityZone.getPrecinto());
            eirActivityZoneData.setUsuarioInicio(eirActivityZone.getUsuarioInicio());
            eirActivityZoneData.setUsuarioTermino(eirActivityZone.getUsuarioTermino());
            eirActivityZoneData.setFechaCreacion(eirActivityZone.getFCreacion());

            eirActivityZoneDataList.add(eirActivityZoneData);
        }
        return eirActivityZoneDataList;
    }

    List<EirGetOutput.EirZoneData> generateEirZoneOutput(Integer eirId, Integer languageId, Integer subBusinessUnitId, String formatDateTime) {
        List<EirZoneProjection> eirZones = eirZoneRepository.findEirZoneByEirId(eirId, languageId, subBusinessUnitId, formatDateTime);
        List<EirGetOutput.EirZoneData> eirZoneDataList = new ArrayList<>();
        for (EirZoneProjection eirZone : eirZones) {
            EirGetOutput.EirZoneData eirZoneData = new EirGetOutput.EirZoneData();
            eirZoneData.setZona(eirZone.getZona());
            eirZoneData.setFechaMovimiento(eirZone.getFechaMovimiento());
            eirZoneDataList.add(eirZoneData);
        }
        return eirZoneDataList;
    }

    List<EirGetOutput.EirObservationInspectorData> generateObservationOutput(Integer eirId, Integer languageId, Integer forReefer) {

        List<EirObservationInspectorProjection> eirObservations = eirObservationInspectorRepository.findObservationsByEirIdAndType(eirId, languageId, forReefer);
        List<EirGetOutput.EirObservationInspectorData> eirObservationList = new ArrayList<>();
        for (EirObservationInspectorProjection eirObservation : eirObservations) {
            EirGetOutput.EirObservationInspectorData eirObservationData = new EirGetOutput.EirObservationInspectorData();
            eirObservationData.setRow(eirObservation.getRow());
            eirObservationData.setObservationDescription(eirObservation.getObservacionDescripcion());
            eirObservationList.add(eirObservationData);

        }
        return eirObservationList;
    }

    List<EirGetOutput.EirEdiCodecoData> generateEdicodecoOutput(Integer eirId, Integer subBusinessUnitId, Integer languageId){

        List<EdiCodecoProjection> ediCodecos= gateTransmissionRepository.findByEirId(eirId,subBusinessUnitId,languageId);
        List<EirGetOutput.EirEdiCodecoData> ediCodecoDataList= new ArrayList<>();

        for(EdiCodecoProjection ediCodeco:ediCodecos){
            EirGetOutput.EirEdiCodecoData ediCodecoData= new EirGetOutput.EirEdiCodecoData();

            ediCodecoData.setEirId(ediCodeco.getEirId());
            ediCodecoData.setIdTransmision(ediCodeco.getIdTransmision());
            ediCodecoData.setEstadoTransmision(ediCodeco.getEstadoTransmision());
            ediCodecoData.setSistemaEntrega(ediCodeco.getSistemaEntrega());
            ediCodecoData.setInOut(ediCodeco.getInOut());
            ediCodecoData.setEmptyFull(ediCodeco.getEmptyFull());
            ediCodecoData.setLineaDo(ediCodeco.getLineaDo());
            ediCodecoData.setFechaActividad(ediCodeco.getFechaActividad());
            ediCodecoData.setNumeroContenedor(ediCodeco.getNumeroContenedor());
            ediCodecoData.setCodigoIsoContenedor(ediCodeco.getCodigoIsoContenedor());
            ediCodecoData.setTaraContenedor(ediCodeco.getTaraContenedor());
            ediCodecoData.setDocumento(ediCodeco.getDocumento());
            ediCodecoData.setNaveViaje(ediCodeco.getNaveViaje());
            ediCodecoData.setOperacion(ediCodeco.getOperacion());
            ediCodecoData.setPesoMercaderia(ediCodeco.getPesoMercaderia());
            ediCodecoData.setClienteRazonSocial(ediCodeco.getClienteRazonSocial());
            ediCodecoData.setPrecinto1(ediCodeco.getPrecinto1());
            ediCodecoData.setPrecinto2(ediCodeco.getPrecinto2());
            ediCodecoData.setPrecinto3(ediCodeco.getPrecinto3());
            ediCodecoData.setPrecinto4(ediCodeco.getPrecinto4());
            ediCodecoData.setManifiesto(ediCodeco.getManifiesto());
            ediCodecoData.setSituacionCaja(ediCodeco.getSituacionCaja());
            ediCodecoData.setSituacionMaquina(ediCodeco.getSituacionMaquina());
            ediCodecoData.setPuertoDescarga(ediCodeco.getPuertoDescarga());
            ediCodecoData.setPuertoEmbarque(ediCodeco.getPuertoEmbarque());
            ediCodecoData.setVehiculoPlaca(ediCodeco.getVehiculoPlaca());
            ediCodecoData.setTipoMovimiento(ediCodeco.getTipoMovimiento());
            ediCodecoData.setTransaccion(ediCodeco.getTransaccion());
            ediCodecoData.setFechaRegistro(ediCodeco.getFechaRegistro());
            ediCodecoData.setEstadoArchivo(ediCodeco.getEstadoArchivo());
            ediCodecoData.setFechaEnvio(ediCodeco.getFechaEnvio());
            ediCodecoData.setCanalEnvio(ediCodeco.getCanalEnvio());
            ediCodecoData.setNombreArchivo(ediCodeco.getNombreArchivo());
            ediCodecoData.setReenvio(ediCodeco.getReenvio());
            ediCodecoData.setUsuarioReenvio(ediCodeco.getUsuarioReenvio());
            ediCodecoData.setOrigenCreacionEir(ediCodeco.getOrigenCreacionEir());
            ediCodecoData.setLocal(ediCodeco.getLocal());
            ediCodecoData.setComentarioEdi(ediCodeco.getEdiCodecoComentario());
            ediCodecoData.setSeteoEdiCodecoId(ediCodeco.getSeteoEdiCodecoId());
            ediCodecoData.setActivo(ediCodeco.getActivo());
            ediCodecoData.setContenidoArchivoCodeco(ediCodeco.getContenidoArchivoCodeco());

            ediCodecoDataList.add(ediCodecoData);
        }

        return ediCodecoDataList;

    }

    List<EirGetOutput.EirGateDriverPhotoDTO> generateDriverPhotosOutput(Integer eirId){

        List<EirGateDriverPhotoProjection> driverPhotos= eirRepository.findDriverPhotosByEirId(eirId);

        List<EirGetOutput.EirGateDriverPhotoDTO> driverPhotosList= new ArrayList<>();

        for(EirGateDriverPhotoProjection driverPhoto:driverPhotos){
            EirGetOutput.EirGateDriverPhotoDTO driverPhotoData= new EirGetOutput.EirGateDriverPhotoDTO();

            driverPhotoData.setEirId(driverPhoto.getEirId());
            driverPhotoData.setEirGateDriverPhotoId(driverPhoto.getEirGateDriverPhotoId());
            driverPhotoData.setAdjuntoId(driverPhoto.getAdjuntoId());
            driverPhotoData.setId(driverPhoto.getId());
            driverPhotoData.setUrl(driverPhoto.getUrl());

            driverPhotosList.add(driverPhotoData);
        }

        return driverPhotosList;

    }

}
