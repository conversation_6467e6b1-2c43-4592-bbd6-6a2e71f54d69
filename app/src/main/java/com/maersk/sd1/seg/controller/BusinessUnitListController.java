package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.BusinessUnitListInput;
import com.maersk.sd1.seg.dto.BusinessUnitListOutput;
import com.maersk.sd1.seg.service.BusinessUnitListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUnidadNegocioServiceImp")
public class BusinessUnitListController {

    private static final Logger logger = LogManager.getLogger(BusinessUnitListController.class);

    private final BusinessUnitListService businessUnitListService;

    public BusinessUnitListController(BusinessUnitListService businessUnitListService) {
        this.businessUnitListService = businessUnitListService;
    }

    @PostMapping("/segunidadNegocioListar")
    public ResponseEntity<ResponseController<BusinessUnitListOutput>> listBusinessUnits(
            @Valid @RequestBody BusinessUnitListInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                BusinessUnitListOutput result = businessUnitListService.listBusinessUnits(null);
                return ResponseEntity.ok(new ResponseController<>(result));
            }
            BusinessUnitListInput.Input inputParams = request.getPrefix().getInput();
            BusinessUnitListOutput result = businessUnitListService.listBusinessUnits(inputParams);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while listing business units.", e);
            BusinessUnitListOutput output = new BusinessUnitListOutput();
            output.setTotalRecords(List.of(List.of(0L)));
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}