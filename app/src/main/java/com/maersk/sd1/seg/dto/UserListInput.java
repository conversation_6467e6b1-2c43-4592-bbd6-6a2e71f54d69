package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class UserListInput {

    @Data
    public static class Input {

        @JsonProperty("usuarios_id")
        private String userIds;

        @JsonProperty("usuario_id")
        private Integer userId;

        @JsonProperty("id")
        private String alias;

        @JsonProperty("correo")
        private String email;

        @JsonProperty("nombres")
        private String names;

        @JsonProperty("estado")
        private Character status;

        @JsonProperty("roles")
        private String roles;

        @JsonProperty("empresas")
        private String companies;

        @JsonProperty("Page")
        @Min(1)
        private Integer page;

        @JsonProperty("Size")
        @Min(1)
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ADM")
        private Prefix prefix;
    }
}
