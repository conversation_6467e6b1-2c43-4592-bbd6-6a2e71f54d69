package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.seg.dto.CatalogOptionDto;
import com.maersk.sd1.seg.dto.CurrencyOptionDto;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BusinessUnitOptionOutput {

    @JsonProperty("catalog_options")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<CatalogOptionDto> catalogOptions;

    @JsonProperty("currency_options")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<CurrencyOptionDto> currencyOptions;

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;
}