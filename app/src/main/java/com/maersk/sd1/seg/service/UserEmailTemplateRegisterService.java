package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.EmailTemplate;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.UserEmailTemplate;
import com.maersk.sd1.common.model.UserEmailTemplateId;
import com.maersk.sd1.common.repository.EmailTemplateRepository;
import com.maersk.sd1.common.repository.UserEmailTemplateRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.controller.dto.UserEmailTemplateRegisterInput;
import com.maersk.sd1.seg.controller.dto.UserEmailTemplateRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.StringReader;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Transactional
public class UserEmailTemplateRegisterService {

    private static final Logger logger = LogManager.getLogger(UserEmailTemplateRegisterService.class);

    private final UserEmailTemplateRepository userEmailTemplateRepository;

    private final UserRepository userRepository;

    private final EmailTemplateRepository emailTemplateRepository;

    public UserEmailTemplateRegisterOutput registerUserEmailPlantilla(Integer usuarioId, String xmlEmailConfigs) {
        UserEmailTemplateRegisterOutput output = new UserEmailTemplateRegisterOutput();
        try {
            userEmailTemplateRepository.deleteAllByUserId(usuarioId);

            User user = userRepository.findById(usuarioId)
                    .orElseThrow(() -> new RuntimeException("User not found for ID: " + usuarioId));

            List<UserEmailTemplateRegisterInput.EmailConfig> emailConfigs = parseEmailConfigFromXml(xmlEmailConfigs);

            for (UserEmailTemplateRegisterInput.EmailConfig config : emailConfigs) {

                EmailTemplate emailTemplate = emailTemplateRepository.findById(config.getEmailTemplateId())
                        .orElseThrow(() -> new RuntimeException("EmailTemplate not found for ID: " + config.getEmailTemplateId()));

                UserEmailTemplate entity = new UserEmailTemplate();
                UserEmailTemplateId entityId = new UserEmailTemplateId();
                entityId.setUserId(usuarioId);
                entityId.setEmailTemplateId(config.getEmailTemplateId());

                entity.setId(entityId);
                entity.setUser(user);
                entity.setEmailTemplate(emailTemplate);
                entity.setStatus(config.getStatus());
                entity.setEnabled(config.getHabilitado());
                entity.setRegistrationDate(LocalDateTime.now());
                entity.setRegistrationUser(user);
                entity.setModificationDate(null);
                entity.setModificationUser(null);

                userEmailTemplateRepository.save(entity);
            }

            output.setRespStatus(1);
            output.setRespMessage("Emails registrados correctamente");
        } catch (Exception ex) {
            logger.error("Error registering user email templates", ex);
            output.setRespStatus(0);
            output.setRespMessage(ex.getMessage());
        }
        return output;
    }

    public static List<UserEmailTemplateRegisterInput.EmailConfig> parseEmailConfigFromXml(String xmlString) throws UserEmailPlantillaRegisterException, ParserConfigurationException, IOException, SAXException {
        List<UserEmailTemplateRegisterInput.EmailConfig> emailConfigs = new ArrayList<>();

        String wrappedXmlString = "<Emails>" + xmlString + "</Emails>";

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

        factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        factory.setXIncludeAware(false);
        factory.setExpandEntityReferences(false);

        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(wrappedXmlString)));

        NodeList nodeList = document.getElementsByTagName("Email");

        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);

            if (node.getNodeType() == Node.ELEMENT_NODE) {
                Element element = (Element) node;

                UserEmailTemplateRegisterInput.EmailConfig emailConfig = new UserEmailTemplateRegisterInput.EmailConfig();
                emailConfig.setEmailTemplateId(Integer.parseInt(element.getElementsByTagName("email_plantilla_id").item(0).getTextContent()));
                emailConfig.setHabilitado(element.getElementsByTagName("habilitado").item(0).getTextContent().charAt(0));
                emailConfig.setStatus(element.getElementsByTagName("estado").item(0).getTextContent().charAt(0));

                emailConfigs.add(emailConfig);
            }
        }
        return emailConfigs;
    }

    public class UserEmailPlantillaRegisterException extends RuntimeException {
        public UserEmailPlantillaRegisterException(String message) {
            super(message);
        }
    }
}