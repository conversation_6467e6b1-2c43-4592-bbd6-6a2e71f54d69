package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.BusinessUnitObtenerInput;
import com.maersk.sd1.seg.dto.BusinessUnitObtenerOutput;
import com.maersk.sd1.seg.service.BusinessUnitObtenerService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleADM/module/adm/ADMUnidadNegocioServiceImp")
public class BusinessUnitObtenerController {

    private static final Logger logger = LogManager.getLogger(BusinessUnitObtenerController.class);

    private final BusinessUnitObtenerService businessUnitObtenerService;

    @PostMapping("/segunidadNegocioObtener")
    public ResponseEntity<ResponseController<BusinessUnitObtenerOutput>> segBusinessUnitObtener(
            @RequestBody @Valid BusinessUnitObtenerInput.Root request) {
        try {
            Integer businessUnitId = request.getPrefix().getInput().getBusinessUnitId();
            BusinessUnitObtenerOutput output = businessUnitObtenerService.obtenerBusinessUnit(businessUnitId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing segBusinessUnitObtener request.", e);
            BusinessUnitObtenerOutput output = new BusinessUnitObtenerOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
