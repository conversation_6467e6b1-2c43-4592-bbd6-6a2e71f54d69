package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.BusinessUnitSearchDetailInput;
import com.maersk.sd1.seg.controller.dto.BusinessUnitSearchDetailOutput;
import com.maersk.sd1.seg.service.BusinessUnitSearchDetailService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUnidadNegocioServiceImp")
public class BusinessUnitSearchDetailController {

    private static final Logger logger = LogManager.getLogger(BusinessUnitSearchDetailController.class);

    private final BusinessUnitSearchDetailService businessUnitSearchDetailService;

    public BusinessUnitSearchDetailController(BusinessUnitSearchDetailService businessUnitSearchDetailService) {
        this.businessUnitSearchDetailService = businessUnitSearchDetailService;
    }

    @PostMapping("/segbusinessUnitSearchDetail")
    public ResponseEntity<ResponseController<List<BusinessUnitSearchDetailOutput>>> searchBusinessUnit(
            @RequestBody @Valid BusinessUnitSearchDetailInput.Root request) {

        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            BusinessUnitSearchDetailInput.Input input = request.getPrefix().getInput();
            Integer systemId = input.getSystemId();
            List<BusinessUnitSearchDetailOutput> output = businessUnitSearchDetailService.searchBusinessUnit(systemId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(Collections.emptyList()));
        }
    }
}