package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class CurrencyOptionDto {

    @JsonProperty("moneda_id")
    private Integer currencyId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("abbreviation")
    private String abbreviation;
}