package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BusinessUnitSearchDetailOutput {

    @JsonProperty("business_unit_id")
    private Integer businessUnitId;

    @JsonProperty("business_unit")
    private String businessUnit;

    @JsonProperty("system_id")
    private Integer systemId;

    @JsonProperty("system_name")
    private String systemName;
}