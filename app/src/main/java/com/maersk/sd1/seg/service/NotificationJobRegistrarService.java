package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.NotificationJobRepository;
import com.maersk.sd1.seg.controller.dto.NotificationJobRegistrarInput;
import com.maersk.sd1.seg.controller.dto.NotificationJobRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class NotificationJobRegistrarService {


    private final NotificationJobRepository notificationJobRepository;

    @Transactional
    public NotificationJobRegisterOutput processNotificationJobRegistrar(NotificationJobRegistrarInput.Root inputRoot) {

        NotificationJobRegisterOutput output = NotificationJobRegisterOutput.builder()
                .id(0)
                .statusCode(0)
                .build();

        try {
            NotificationJob notificationJob = NotificationJob.builder()
                    .projectMenu(inputRoot.getInput().getMenuProjectId() != null ? Menu.builder().id(inputRoot.getInput().getMenuProjectId()).build() : null)
                    .description(inputRoot.getInput().getDescription())
                    .period(inputRoot.getInput().getPeriod())
                    .validitySince(inputRoot.getInput().getValidityFrom())
                    .validityUntil(inputRoot.getInput().getValidityTo())
                    .emailTemplate(inputRoot.getInput().getEmailTemplateId() != null
                            ? EmailTemplate.builder().id(inputRoot.getInput().getEmailTemplateId()).build()
                            : null)
                    .emailProcedure(inputRoot.getInput().getEmailProcedure())
                    .webNotificationTemplate(inputRoot.getInput().getWebNotificationTemplateId() != null
                            ? NotificationTemplate.builder().id(inputRoot.getInput().getWebNotificationTemplateId()).build()
                            : null)
                    .webNotificationProcedure(inputRoot.getInput().getWebNotificationProcedure())
                    .pushNotificationTemplate(inputRoot.getInput().getPushNotificationTemplateId() != null
                            ? NotificationTemplate.builder().id(inputRoot.getInput().getPushNotificationTemplateId()).build()
                            : null)
                    .pushNotificationProcedure(inputRoot.getInput().getPushNotificationProcedure())
                    .catStatus(Catalog.builder().id(inputRoot.getInput().getStatus()).build())
                    .registrationUser(User.builder().id(inputRoot.getInput().getRegisteredUserId()).build())
                    .registrationDate(LocalDateTime.now())
                    .build();
            notificationJob = notificationJobRepository.save(notificationJob);

            output.setId(notificationJob.getId());
            output.setStatusCode(1);
            output.setMessage("Record successfully created");

        } catch (Exception ex) {
            output.setMessage("Error occurred while creating the record: " + ex.getMessage());
        }
        return output;
    }
}
