package com.maersk.sd1.adm.controller;


import com.maersk.sd1.adm.dto.PersonByBusinessUnitListInput;
import com.maersk.sd1.adm.dto.PersonByBusinessUnitListOutput;
import com.maersk.sd1.adm.service.PersonByBusinessUnitListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * Controller exposing an endpoint to list persons by business unit.
 */
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMPersonaServiceImp")
@RequiredArgsConstructor
public class PersonByBusinessUnitListController {

    private static final Logger logger = LogManager.getLogger(PersonByBusinessUnitListController.class);

    private final PersonByBusinessUnitListService personByBusinessUnitListService;

    @PostMapping("/gespersonByBusinessUnitList")
    public ResponseEntity<ResponseController<List<PersonByBusinessUnitListOutput.PersonItemDto>>> personByBusinessUnitList(
            @RequestBody @Valid PersonByBusinessUnitListInput.Root request) {
        try {
            PersonByBusinessUnitListInput.Input input = request.getPrefix().getInput();

            // Call the service method
            PersonByBusinessUnitListOutput output = personByBusinessUnitListService.getPersonList(input);
            List<PersonByBusinessUnitListOutput.PersonItemDto> personList = output.getPersonList();

            return ResponseEntity.ok(new ResponseController<>(personList));
        } catch (Exception e) {
            logger.error("An error occurred while processing personByBusinessUnitList request.", e);
            ResponseController<List<PersonByBusinessUnitListOutput.PersonItemDto>> errorResponse =
                    new ResponseController<>(Collections.emptyList());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}

