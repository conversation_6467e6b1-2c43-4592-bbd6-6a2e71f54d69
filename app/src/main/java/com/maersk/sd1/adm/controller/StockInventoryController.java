package com.maersk.sd1.adm.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.adm.dto.StockInventoryReportInput;
import com.maersk.sd1.adm.dto.StockInventoryReportOutput;
import com.maersk.sd1.adm.service.StockInventoryService;


import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/inlandnet/Inlandnet/gestion/ReporteConsultaServiceImp")
public class StockInventoryController {

    private static final Logger logger = LogManager.getLogger(StockInventoryController.class);
    private final StockInventoryService stockInventoryService;

    @PostMapping("/consultarReporte06")
    public ResponseEntity<ResponseController<StockInventoryReportOutput>> report06StockInventory(@RequestBody @Valid StockInventoryReportInput.Root request) {
        try {
            logger.info("Request received report06StockInventory: {}", request);
            StockInventoryReportInput.Input input = request.getPrefix().getInput();
            StockInventoryReportOutput reportOutput = stockInventoryService.getStockInventoryReport(input);
            return ResponseEntity.ok(new ResponseController<>(reportOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            StockInventoryReportOutput errorOutput = new StockInventoryReportOutput();
            errorOutput.setTotalRecords(0L);
            errorOutput.setInventoryList(null);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}
