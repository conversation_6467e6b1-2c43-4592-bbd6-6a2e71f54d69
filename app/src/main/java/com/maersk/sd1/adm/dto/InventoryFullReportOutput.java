package com.maersk.sd1.adm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class InventoryFullReportOutput {

    @JsonProperty("equipment_type")
    private String equipmentType;

    @JsonProperty("equipment_number")
    private String equipmentNumber;

    @JsonProperty("size_eir")
    private String sizeEir;

    @JsonProperty("container_type_eir")
    private String containerTypeEir;

    @JsonProperty("grade_eir")
    private String gradeEir;

    @JsonProperty("iso_code_eir")
    private String isoCodeEir;

    @JsonProperty("shipping_line_eir")
    private String shippingLineEir;

    @JsonProperty("payload")
    private Integer payload;

    @JsonProperty("mu_payload")
    private String muPayload;

    @JsonProperty("tare")
    private Integer tare;

    @JsonProperty("mu_tare")
    private String muTare;

    @JsonProperty("weight")
    private String weight;

    @JsonProperty("mu_weight")
    private String muWeight;

    @JsonProperty("gate_in_eir")
    private Integer gateInEir;

    @JsonProperty("movement_type")
    private String movementType;

    @JsonProperty("gate_in_date")
    private String gateInDate;

    @JsonProperty("permanence")
    private Long permanence;

    @JsonProperty("restrictions")
    private String restrictions;

    @JsonProperty("vessel_name")
    private String vesselName;

    @JsonProperty("voyage")
    private String voyage;

    @JsonProperty("depot_operation")
    private String depotOperation;

    @JsonProperty("seals")
    private String seals;

    @JsonProperty("document_type")
    private String documentType;

    @JsonProperty("document_number")
    private String documentNumber;

    @JsonProperty("shipper_name")
    private String shipperName;

    @JsonProperty("consignee_name")
    private String consigneeName;

    @JsonProperty("local")
    private String local;

    @JsonProperty("state_equipment")
    private String stateEquipment;

    @JsonProperty("means_transport")
    private String meansTransport;

    @JsonProperty("register_date")
    private String registerDate;
}
