package com.maersk.sd1.adm.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Null;
import lombok.Data;

import java.time.LocalDateTime;

public class SignatureRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("id")
        @Size(max = 50)
        private String uid;

        @JsonProperty("user_id")
        private Long userId;

        @JsonProperty("person_id")
        private Integer personId;

        @JsonProperty("url")
        @Size(max = 500)
        private String url;

        @JsonProperty("user_registration_id")
        @NotNull
        private Long userRegistrationId;

        /* Additional example demonstrating date usage if needed
           though the stored procedure did not specifically mention a date on input:
        */
        @JsonProperty("fecha_registro")
        @PastOrPresent
        @Null // example if not provided, but can be adjusted if needed
        private LocalDateTime registrationDate;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("IND")
        private Prefix prefix;
    }
}
