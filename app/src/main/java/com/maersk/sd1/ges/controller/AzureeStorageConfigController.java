package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.AzureStorageConfigListInput;
import com.maersk.sd1.ges.dto.AzureStorageConfigListInput.Root;
import com.maersk.sd1.ges.dto.AzureStorageConfigListOutput;
import com.maersk.sd1.ges.service.AzureeStorageConfigService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMAzureStorageConfigServiceImp")
public class AzureeStorageConfigController {

    private static final Logger logger = LogManager.getLogger(AzureeStorageConfigController.class);

    private final AzureeStorageConfigService azureStorageConfigService;

    @Autowired
    public AzureeStorageConfigController(AzureeStorageConfigService azureStorageConfigService) {
        this.azureStorageConfigService = azureStorageConfigService;
    }

    @PostMapping("/gesazureStorageConfigListar")
    public ResponseEntity<ResponseController<AzureStorageConfigListOutput>> listarAzureConfig(@RequestBody @Valid Root request) {
        try {
            AzureStorageConfigListInput.Input input = request.getPrefix().getInput();
            AzureStorageConfigListOutput output = azureStorageConfigService.listAzureStorageConfig(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("Ocurrió un error al procesar la solicitud.", e);
            AzureStorageConfigListOutput output = new AzureStorageConfigListOutput();
            output.setTotalRegistros(List.of(List.of(0L)));
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}