package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;

import com.maersk.sd1.ges.dto.EditCompanyInputDTO;
import com.maersk.sd1.ges.dto.EditCompanyOutputDTO;
import com.maersk.sd1.ges.service.CompanyEditService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMEmpresaServiceImp")
public class CompanyEditController {

    private static final Logger logger = LogManager.getLogger(CompanyEditController.class);

    private final CompanyEditService companyEditService;

    @Autowired
    public CompanyEditController(CompanyEditService companyEditService)
    {
        this.companyEditService = companyEditService;
    }

    @PostMapping("/gesempresaEditar")
    public ResponseEntity<ResponseController<EditCompanyOutputDTO>> editCompany(@RequestBody @Valid EditCompanyInputDTO.Root request) {
        try {
            EditCompanyInputDTO.Input input = request.getPrefix().getInput();
            EditCompanyOutputDTO output = companyEditService.editCompany(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the editCompany request.", e);
            EditCompanyOutputDTO output = new EditCompanyOutputDTO();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

