package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController; // Assuming you have a similar generic response wrapper
import com.maersk.sd1.ges.controller.dto.CatalogRegisterInput;
import com.maersk.sd1.ges.controller.dto.CatalogRegisterOutput;
import com.maersk.sd1.ges.service.CatalogRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMCatalogoServiceImp")
public class CatalogRegisterController {

    private static final Logger logger = LogManager.getLogger(CatalogRegisterController.class);

    private final CatalogRegisterService catalogRegisterService;

    @PostMapping("/gescatalogoRegistrar")
    public ResponseEntity<ResponseController<CatalogRegisterOutput>> sdgcatalogRegister(@RequestBody @Valid CatalogRegisterInput.Root request) {
        try {
            logger.info("Request received sdgcatalogRegister: {}", request);
            CatalogRegisterInput.Input input = request.getPrefix().getInput();
            CatalogRegisterOutput response = catalogRegisterService.registerCatalog(input);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("An error occurred while processing sdgcatalogRegister.", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(e.toString()));
        }
    }
}
