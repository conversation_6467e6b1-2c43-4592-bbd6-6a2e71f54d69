package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ReportDTO {

    @JsonProperty("reportId")
    private Integer reportId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("storeName")
    private String storeName;

    @JsonProperty("parameters")
    private String parameters;

    public ReportDTO(Integer reportId, String name, String description, String storeName, String parameters) {
        this.reportId = reportId;
        this.name = name;
        this.description = description;
        this.storeName = storeName;
        this.parameters = parameters;
    }

}