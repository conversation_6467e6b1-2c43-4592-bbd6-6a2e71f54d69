package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class ReportObtainOutput {
    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("reporte_id")
    private Integer reporteId;

    @JsonProperty("menu_id")
    private Integer menuId;

    @JsonProperty("nombre")
    private String name;

    @JsonProperty("descripcion")
    private String description;

    @JsonProperty("nombre_store")
    private String nameStore;

    @JsonProperty("parametros")
    private String parameters;

    @JsonProperty("columns")
    private String columns;

    @JsonProperty("estado")
    private Boolean status;

    @JsonProperty("roles")
    private List<Integer> roles;
}

