package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.util.List;

@UtilityClass
public class EmailTemplateEditInput {

    @Data
    public static class EmailAttributeItem {
        @JsonProperty("field")
        @NotNull
        @Size(max = 80)
        private String field;
    }

    @Data
    public static class EmailTemplateRoleItem {
        @JsonProperty("id")
        @NotNull
        private Integer id;
    }

    @Data
    public static class Input {
        @JsonProperty("email_plantilla_id")
        @NotNull
        private Integer emailPlantillaId;

        @JsonProperty("email_plantilla_padre_id")
        private Integer emailPlantillaPadreId;

        @JsonProperty("contenido")
        private String content;

        @JsonProperty("remitente")
        private String sender;

        @JsonProperty("destinatario")
        private String recipient;

        @JsonProperty("copia")
        private String copy;

        @JsonProperty("copia_oculta")
        private String copyHidden;

        @JsonProperty("titulo")
        private String title;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Integer userModificationId;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("menu_proyecto_id")
        private Integer menuProjectId;

        // JSON list of attributes
        @JsonProperty("email_atributo")
        private List<EmailAttributeItem> emailAtribute;

        // JSON list of roles
        @JsonProperty("email_plantilla_rol")
        private List<EmailTemplateRoleItem> emailTemplateRole;

        @JsonProperty("id")
        private String uniqueId;

        @JsonProperty("evento_despues_enviar")
        private String eventAfterSend;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        @NotNull
        private Prefix prefix;
    }
}
