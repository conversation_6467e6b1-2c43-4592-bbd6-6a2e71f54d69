package com.maersk.sd1.ges.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.IOException;
import java.util.List;

@Data
public class CompanySearchInput {

    @Data
    public static class Input {

        @JsonProperty("business_unit_id")
        @NotNull(message = "business_unit_id cannot be null")
        private Integer businessUnitId;

        @JsonProperty("company_roles")
        @NotNull(message = "company_roles list cannot be null")
        private List<String> companyRoles;

        @JsonProperty("flag_null")
        @NotNull(message = "flag_null cannot be null")
        private String flagNull;

        @JsonProperty("company_name")
        private String companyName;

        @JsonSetter("company_roles")
        public void setCompanyRoles(String companyRolesJson) throws JsonProcessingException {
                ObjectMapper objectMapper = new ObjectMapper();
                this.companyRoles = objectMapper.readValue(companyRolesJson, List.class);
        }
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ADM")
        private Prefix prefix;
    }
}