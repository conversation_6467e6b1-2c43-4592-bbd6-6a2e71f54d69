package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.dto.SftpConfigObtenerInput;
import com.maersk.sd1.ges.dto.SftpConfigObtenerOutput;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.service.SftpConfigObtenerService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RestController
@RequestMapping("moduleadm/ModuleADM/module/adm/ADMSftpConfigService")
public class SftpConfigObtenerController {

    private static final Logger logger = LogManager.getLogger(SftpConfigObtenerController.class);

    private final SftpConfigObtenerService sftpConfigObtenerService;

    @Autowired
    public SftpConfigObtenerController(SftpConfigObtenerService sftpConfigObtenerService) {
        this.sftpConfigObtenerService = sftpConfigObtenerService;
    }

    @PostMapping("/gessftpConfigObtener")
    public ResponseEntity<ResponseController<SftpConfigObtenerOutput>> sftpConfigObtener(@RequestBody @Valid SftpConfigObtenerInput.Root request) {
        try {
            SftpConfigObtenerInput.Input input = request.getPrefix().getInput();
            SftpConfigObtenerOutput output = sftpConfigObtenerService.sftpConfigObtener(input.getSftpConfigId());
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing sftpConfigObtener request.", e);
            SftpConfigObtenerOutput output = new SftpConfigObtenerOutput();
            output.setRespResultCode(0);
            output.setRespResultMessage("Error: " + e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

