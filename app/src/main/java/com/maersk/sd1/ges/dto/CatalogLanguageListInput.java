package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class CatalogLanguageListInput {

    @Data
    public static class Input {

        @JsonProperty("catalogo_idioma_id")
        private Integer catalogLanguageId;

        @JsonProperty("catalogo_id")
        private java.math.BigDecimal catalogId;

        @JsonProperty("idioma_id")
        private Integer languageId;

        @JsonProperty("descripcion")
        @Size(max = 100)
        private String description;

        @JsonProperty("descricion_larga")
        @Size(max = 200)
        private String longDescription;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}

