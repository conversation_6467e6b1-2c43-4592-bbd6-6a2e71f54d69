package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;


import java.util.List;

@Data
public class ConsultarServicioInputDTO {

    private ConsultarServicioInputDTO() {}

    @Data
    public static class Input {
        @JsonProperty
        private String nombreStore;

        @JsonProperty
        private List<Parametro> parametros;
    }
    @Data
    public static class Parametro {
        @JsonProperty
        private String parameter;

        @JsonProperty
        private String tipo;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ConsultarServicioInputDTO.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private ConsultarServicioInputDTO.Prefix prefix;
    }

}