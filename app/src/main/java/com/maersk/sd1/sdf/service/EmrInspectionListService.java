package com.maersk.sd1.sdf.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParseException;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdf.dto.*;
import com.maersk.sd1.sdf.repository.SdfEirRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@RequiredArgsConstructor
public class EmrInspectionListService {

    private final CatalogRepository catalogRepository;
    private final EmrInspectionRepository emrInspectionRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final ParametrizationRepository parametrizationRepository;
    private final UserRepository userRepository;
    private final SdfEirRepository sdfEirRepository;

    @Transactional
    public EmrInspectionListOutput emrInspectionList(EmrInspectionListInput.Input input) {

        List<TableContainersDTO> tableContainersDTOS = new ArrayList<>();

        Integer isFull = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        Integer isGateIn = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);

        List<TableEmrInspectionListDTO> tableEmrInspectionListDTOS;

        AtomicInteger idGenerator = new AtomicInteger(1);
        if (input.getContainer() != null) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(input.getContainer());

                List<TableContainersDTO> newContainers = jsonNode.findValues("value").stream()
                        .map(JsonNode::asText)
                        .filter(value -> value.length() < 12)
                        .map(value -> new TableContainersDTO(idGenerator.getAndIncrement(), value))
                        .toList();

                tableContainersDTOS.addAll(newContainers);
            } catch (Exception e) {
                throw new JsonParseException(e);
            }
        }
        List<String> containers = tableContainersDTOS.stream()
                .map(TableContainersDTO::getContainer)
                .toList();

        tableEmrInspectionListDTOS = emrInspectionRepository.findTableEmrInspectionList(input.getSubBusinessUnitLocalId(), isGateIn, isFull, input.getEmrInspectionId(),
                input.getGateInDateMin(), input.getGateInDateMax(),
                input.getInStock(), input.getStatusId(), containers.isEmpty() ? null : containers);

        AtomicInteger idGenerator2 = new AtomicInteger(1);
        for (TableEmrInspectionListDTO tableEmrInspectionListDTO : tableEmrInspectionListDTOS) {
            tableEmrInspectionListDTO.setKey(idGenerator2.getAndIncrement());
            tableEmrInspectionListDTO.setMinutesElapsed((int) ChronoUnit.MINUTES.between(tableEmrInspectionListDTO.getTruckEntryDate(), LocalDateTime.now()));
        }


        Integer quantityToShow = 0;

        Integer recordsCompletedByInspector = 0;
        LocalDateTime fromDate = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime toDate = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(999999999);

        quantityToShow = tableEmrInspectionListDTOS.size();

        List<Parametrization> parametrizations = parametrizationRepository.findPtiTimesByBusinessUnit(Long.valueOf(input.getBusinessUnitId()));
        Parametrization parametrization = parametrizations.getFirst();


        List<FilterBoxDTO> filterBoxDTOS;


        filterBoxDTOS = tableEmrInspectionListDTOS.stream()
                .map(tli -> new FilterBoxDTO(
                        tli.getEirId(),
                        (tli.getMinutesElapsed() <= parametrization.getMinutesInspectionATime() ? 1 :
                                (tli.getMinutesElapsed() > parametrization.getMinutesInspectionATime() && tli.getMinutesElapsed() <= parametrization.getMinutesInspection() ? 2 : 3))
                ))
                .toList();

        List<TableEmrInspectionListDTO> filteredList;

        if (input.getFilterBox() != null) {

            filteredList = tableEmrInspectionListDTOS.stream()
                    .filter(tlist -> filterBoxDTOS.stream()
                            .anyMatch(fil -> fil.getEirId().equals(tlist.getEirId()) && fil.getFilterBox().equals(input.getFilterBox())))
                    .map(tlist -> {
                        int controlTime = tlist.getMinutesElapsed() <= parametrization.getMinutesInspectionATime() ? 1 :
                                tlist.getMinutesElapsed() <= parametrization.getMinutesInspection() ? 2 : 3;
                        tlist.setControlTime(controlTime);
                        return tlist;
                    })
                    .toList();


        } else {
            filteredList = tableEmrInspectionListDTOS.stream()
                    .map(tlist -> {
                        int controlTime = tlist.getMinutesElapsed() <= parametrization.getMinutesInspectionATime() ? 1 :
                                tlist.getMinutesElapsed() <= parametrization.getMinutesInspection() ? 2 : 3;
                        tlist.setControlTime(controlTime);
                        return tlist;
                    })
                    .toList();


        }

        if (input.getPage() != null && input.getSize() != null) {
            int fromIndex = (input.getPage() - 1) * input.getSize();
            int toIndex = Math.min(fromIndex + input.getSize(), filteredList.size());
            filteredList = filteredList.subList(fromIndex, toIndex);
        }

        List<TableEmrInspectionListDTO> mutableList = new ArrayList<>(filteredList);
        mutableList.sort((a, b) -> b.getEmrInspectionId().compareTo(a.getEmrInspectionId()));

        for (TableEmrInspectionListDTO tableEmrInspectionListDTO : mutableList) {
            tableEmrInspectionListDTO.setStatus(catalogLanguageRepository.fnCatalogTranslationDescLong(Integer.valueOf(tableEmrInspectionListDTO.getStatus()), input.getLanguageId()));
            tableEmrInspectionListDTO.setTypeEquipment(catalogLanguageRepository.fnCatalogTranslationDesc(Integer.valueOf(tableEmrInspectionListDTO.getTypeEquipment()), input.getLanguageId()));
            tableEmrInspectionListDTO.setSizeEquipment(catalogLanguageRepository.fnCatalogTranslationDesc(Integer.valueOf(tableEmrInspectionListDTO.getSizeEquipment()), input.getLanguageId()));

            if (tableEmrInspectionListDTO.getTypeReefer() != null) {
                tableEmrInspectionListDTO.setTypeReefer(catalogLanguageRepository.fnCatalogTranslationDesc(Integer.valueOf(tableEmrInspectionListDTO.getTypeReefer()), input.getLanguageId()));
            }

            tableEmrInspectionListDTO.setWeightUnitType(catalogLanguageRepository.fnCatalogTranslationDesc(Integer.valueOf(tableEmrInspectionListDTO.getWeightUnitType()), input.getLanguageId()));
            tableEmrInspectionListDTO.setOperationType(catalogLanguageRepository.fnCatalogTranslationDescLong(Integer.valueOf(tableEmrInspectionListDTO.getOperationType()), input.getLanguageId()));
        }

        Optional<User> optionalPerson = userRepository.findById(input.getUserId());
        Integer personId;
        if (optionalPerson.isPresent()) {
            personId = optionalPerson.get().getPerson().getId();
        } else {
            personId = 0;
        }


        recordsCompletedByInspector = sdfEirRepository.countByLocalSubBusinessUnitAndTruckArrivalDatesAndInspectorPerson(input.getSubBusinessUnitLocalId(), fromDate, toDate, personId);
        long recordsOnTime = tableEmrInspectionListDTOS.stream()
                .filter(list -> list.getMinutesElapsed() <= parametrization.getMinutesInspectionATime())
                .count();

        long recordsOnAlert = tableEmrInspectionListDTOS.stream()
                .filter(list -> list.getMinutesElapsed() > parametrization.getMinutesInspectionATime() && list.getMinutesElapsed() <= parametrization.getMinutesInspection())
                .count();

        long recordsOnDelay = tableEmrInspectionListDTOS.stream()
                .filter(list -> list.getMinutesElapsed() >= parametrization.getMinutesInspectionWithDelay())
                .count();

        int recordsQuantity = quantityToShow;

        LocalDateTime systemDate = LocalDateTime.now();

        InspectionSummaryDTO inspectionSummaryDTO = new InspectionSummaryDTO(recordsCompletedByInspector, recordsOnTime, recordsOnAlert, recordsOnDelay, recordsQuantity, systemDate);

        EmrInspectionListOutput output = new EmrInspectionListOutput();
        output.setTlist(mutableList);
        output.setInspectionSummaryDTO(List.of(inspectionSummaryDTO));

        return output;
    }


}
