package com.maersk.sd1.sdf.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;

/**
 * Input DTO mirroring the parameters of the stored procedure [sdf].[gate_out_full_scheduling_list].
 * Includes a JSON string for containers that might be an array of objects.
 */
@UtilityClass
public class GateOutFullSchedulingListInput {

    @Data
    public static class Input {

        @JsonProperty("business_unit_id")
        @NotNull
        private Integer businessUnitId;

        @JsonProperty("sub_business_unit_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("etd_min")
        @PastOrPresent
        private LocalDate etdMin;

        @JsonProperty("etd_max")
        @PastOrPresent
        private LocalDate etdMax;

        /**
         * Expecting JSON string like:
         * [ { "container_number": "ABC1234567" }, { "container_number": "XYZ7654321" } ]
         */
        @JsonProperty("containers")
        private String containers;

        @JsonProperty("iso_code")
        @Size(max = 10)
        private String isoCode;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;

        @JsonProperty("shipper")
        @Size(max = 100)
        private String shipper;

        @JsonProperty("consignee")
        @Size(max = 100)
        private String consignee;

        @JsonProperty("reference")
        @Size(max = 25)
        private String reference;

        @JsonProperty("states")
        private Integer states;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("page")
        @NotNull
        private Integer page;

        @JsonProperty("size")
        @NotNull
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}
