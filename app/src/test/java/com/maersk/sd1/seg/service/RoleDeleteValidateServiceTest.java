package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.controller.dto.RoleDeleteValidateOutput;
import com.maersk.sd1.common.repository.NotificationRoleRepository;
import com.maersk.sd1.common.repository.ReportRoleRepository;
import com.maersk.sd1.common.repository.RoleBusinessUnitRepository;
import com.maersk.sd1.common.repository.RoleMenuRepository;
import com.maersk.sd1.common.repository.UserRoleRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class RoleDeleteValidateServiceTest {

    @Mock
    private RoleMenuRepository roleMenuRepository;

    @Mock
    private UserRoleRepository userRoleRepository;

    @Mock
    private NotificationRoleRepository notificationRoleRepository;

    @Mock
    private ReportRoleRepository reportRoleRepository;

    @Mock
    private RoleBusinessUnitRepository roleBusinessUnitRepository;

    @InjectMocks
    private RoleDeleteValidateService roleDeleteValidateService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidRoleId_When_ValidateRoleDelete_Then_ReturnLists() {
        Integer roleId = 100;
        List<String> menuTitles = List.of("Menu1", "Menu2");
        List<String> users = List.of("User A", "User B");
        List<String> notifications = List.of("Notification X");
        List<String> reports = List.of("Report Y", "Report Z");
        List<String> businessUnits = List.of("BU1");

        when(roleMenuRepository.findMenuTitlesByRoleId(roleId)).thenReturn(menuTitles);
        when(userRoleRepository.findUserFullNamesByRoleId(roleId)).thenReturn(users);
        when(notificationRoleRepository.findNotificationTitlesByRoleId(roleId)).thenReturn(notifications);
        when(reportRoleRepository.findReportNamesByRoleId(roleId)).thenReturn(reports);
        when(roleBusinessUnitRepository.findBusinessUnitNamesByRoleId(roleId)).thenReturn(businessUnits);

        RoleDeleteValidateOutput result = roleDeleteValidateService.validateRoleDelete(roleId);

        assertEquals(menuTitles, result.getMenus());
        assertEquals(users, result.getUsers());
        assertEquals(notifications, result.getNotifications());
        assertEquals(reports, result.getReports());
        assertEquals(businessUnits, result.getBusinessUnits());
        assertEquals(1, result.getRespEstado());
        assertEquals("Validation completed successfully.", result.getRespMensaje());
    }

    @Test
    void given_ExceptionOccurs_When_ValidateRoleDelete_Then_ReturnError() {
        Integer roleId = 999;
        when(roleMenuRepository.findMenuTitlesByRoleId(roleId)).thenThrow(new RuntimeException("Test Exception"));

        RoleDeleteValidateOutput result = roleDeleteValidateService.validateRoleDelete(roleId);

        assertEquals(0, result.getRespEstado());
        assertEquals("Test Exception", result.getRespMensaje());
    }

    @Test
    void given_EmptyData_When_ValidateRoleDelete_Then_ListsAreEmpty() {
        Integer roleId = 101;
        when(roleMenuRepository.findMenuTitlesByRoleId(roleId)).thenReturn(Collections.emptyList());
        when(userRoleRepository.findUserFullNamesByRoleId(roleId)).thenReturn(Collections.emptyList());
        when(notificationRoleRepository.findNotificationTitlesByRoleId(roleId)).thenReturn(Collections.emptyList());
        when(reportRoleRepository.findReportNamesByRoleId(roleId)).thenReturn(Collections.emptyList());
        when(roleBusinessUnitRepository.findBusinessUnitNamesByRoleId(roleId)).thenReturn(Collections.emptyList());

        RoleDeleteValidateOutput result = roleDeleteValidateService.validateRoleDelete(roleId);

        assertEquals(1, result.getRespEstado());
        assertEquals("Validation completed successfully.", result.getRespMensaje());
        assertEquals(0, result.getMenus().size());
        assertEquals(0, result.getUsers().size());
        assertEquals(0, result.getNotifications().size());
        assertEquals(0, result.getReports().size());
        assertEquals(0, result.getBusinessUnits().size());
    }

}