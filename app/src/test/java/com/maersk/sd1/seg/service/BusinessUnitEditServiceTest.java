package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.dto.BusinessUnitEditInput;
import com.maersk.sd1.seg.dto.BusinessUnitEditOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BusinessUnitEditServiceTest {

    @InjectMocks
    private BusinessUnitEditService businessUnitEditService;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private BusinessUnitConfigRepository businessUnitConfigRepository;

    @Mock
    private BusinessUnitCurrencyRepository businessUnitCurrencyRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    private BusinessUnitEditInput.Input input;
    private BusinessUnit existingBusinessUnit;

    @BeforeEach
    void setUp() {
        input = new BusinessUnitEditInput.Input();
        input.setBusinessUnitId(1);
        input.setAlias("NewAlias");
        input.setName("Updated BU Name");
        input.setStatus("1");
        input.setUserId("101");
        input.setParentBusinessUnitId(null);
        input.setSystemId(null);
        input.setLanguageId(1);

        existingBusinessUnit = new BusinessUnit();
        existingBusinessUnit.setId(1);
        existingBusinessUnit.setName("Old BU Name");
        existingBusinessUnit.setBusinesUnitAlias("OldAlias");
        existingBusinessUnit.setStatus(true);
        existingBusinessUnit.setModificationDate(LocalDateTime.now());
    }

    @Test
    void editBusinessUnitSuccessfulUpdate() {
        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(existingBusinessUnit));
        when(messageLanguageRepository.findAdmMessage(anyString(), anyInt(), any())).thenReturn("Success");

        BusinessUnitEditOutput output = businessUnitEditService.editBusinessUnit(input);

        assertEquals(1, output.getRespEstado());
        assertEquals("Success", output.getRespMensaje());
        verify(businessUnitRepository).save(any(BusinessUnit.class));
    }

    @Test
    void editBusinessUnitBusinessUnitNotFound() {
        when(businessUnitRepository.findById(1)).thenReturn(Optional.empty());

        BusinessUnitEditOutput output = businessUnitEditService.editBusinessUnit(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Business Unit not found", output.getRespMensaje());
    }

    @Test
    void editBusinessUnitAliasNotUnique() {
        when(businessUnitRepository.findFirstByBusinesUnitAliasAndIdNot("NewAlias", 1))
                .thenReturn(Optional.of(new BusinessUnit()));
        when(messageLanguageRepository.findAdmMessage("BUN_ALIAS_ERROR", 1, null)).thenReturn("Alias already in use");

        BusinessUnitEditOutput output = businessUnitEditService.editBusinessUnit(input);

        assertEquals(2, output.getRespEstado());
        assertEquals("Alias already in use", output.getRespMensaje());
    }

    @Test
    void editBusinessUnitHandlesException() {
        when(businessUnitRepository.findById(1)).thenThrow(new RuntimeException("Database error"));

        BusinessUnitEditOutput output = businessUnitEditService.editBusinessUnit(input);

        assertEquals(0, output.getRespEstado());
        assertTrue(output.getRespMensaje().contains("Error: Database error"));
    }
}
