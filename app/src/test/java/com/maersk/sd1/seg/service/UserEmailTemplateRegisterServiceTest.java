package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.EmailTemplate;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.EmailTemplateRepository;
import com.maersk.sd1.common.repository.UserEmailTemplateRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.controller.dto.UserEmailTemplateRegisterInput;
import com.maersk.sd1.seg.controller.dto.UserEmailTemplateRegisterOutput;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
class UserEmailTemplateRegisterServiceTest {

    @Mock
    private UserEmailTemplateRepository userEmailTemplateRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private EmailTemplateRepository emailTemplateRepository;

    @InjectMocks
    private UserEmailTemplateRegisterService userEmailTemplateRegisterService;

    private User user;
    private EmailTemplate emailTemplate;

    @BeforeEach
    void setUp() {
        user = new User();
        user.setId(1001);
        userTemplate();
        emailTemplate = new EmailTemplate();
        emailTemplate.setId(2002);
    }

    private void userTemplate(){
        user.setId(1);
    }

    @Test
    void givenUserExists_WhenEmailTemplateNotFound_ThenReturnError() {
        String config1 = "<Email>\n    <email_plantilla_id>1</email_plantilla_id>\n    <habilitado>N</habilitado>\n    <estado>1</estado>\n  </Email>\n  <Email>\n    <email_plantilla_id>2</email_plantilla_id>\n    <habilitado>N</habilitado>\n    <estado>1</estado>\n  </Email>";

        Mockito.when(userRepository.findById(1001)).thenReturn(Optional.of(user));
        Mockito.when(emailTemplateRepository.findById(2002)).thenReturn(Optional.empty());

        List<UserEmailTemplateRegisterInput.EmailConfig> configs = new ArrayList<>();
        UserEmailTemplateRegisterInput.EmailConfig config = new UserEmailTemplateRegisterInput.EmailConfig();
        config.setEmailTemplateId(2002);
        config.setStatus('1');
        config.setHabilitado('1');
        configs.add(config);

        UserEmailTemplateRegisterOutput output = userEmailTemplateRegisterService.registerUserEmailPlantilla(1001, config1);

        Assertions.assertEquals(0, output.getRespStatus());
    }
}