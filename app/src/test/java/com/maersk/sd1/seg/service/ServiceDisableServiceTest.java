package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.ServiceRepository;
import com.maersk.sd1.seg.dto.ServiceDisableOutputDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ServiceDisableServiceTest {

    @Mock
    private ServiceRepository serviceRepository;

    @InjectMocks
    private ServiceDisableService serviceDisableService;


    @Test
    void testDisableServiceSuccess() {
        when(serviceRepository.disableService(anyInt(), anyInt(), any(LocalDateTime.class))).thenReturn(1);

        ServiceDisableOutputDto output = serviceDisableService.disableService(1, 1);

        assertEquals(1, output.getRespResult());
        assertEquals("Record successfully disabled", output.getRespMessage());
        verify(serviceRepository, times(1)).disableService(anyInt(), anyInt(), any(LocalDateTime.class));
    }

    @Test
    void testDisableServiceRecordNotFound() {
        when(serviceRepository.disableService(anyInt(), anyInt(), any(LocalDateTime.class))).thenReturn(0);

        ServiceDisableOutputDto output = serviceDisableService.disableService(1, 1);

        assertEquals(0, output.getRespResult());
        assertEquals("Record not found with the given ID", output.getRespMessage());
        verify(serviceRepository, times(1)).disableService(anyInt(), anyInt(), any(LocalDateTime.class));
    }

    @Test
    void testDisableServiceException() {
        when(serviceRepository.disableService(anyInt(), anyInt(), any(LocalDateTime.class))).thenThrow(new RuntimeException("Database error"));

        ServiceDisableOutputDto output = serviceDisableService.disableService(1, 1);

        assertEquals(0, output.getRespResult());
        assertEquals("Database error", output.getRespMessage());
        verify(serviceRepository, times(1)).disableService(anyInt(), anyInt(), any(LocalDateTime.class));
    }
}
