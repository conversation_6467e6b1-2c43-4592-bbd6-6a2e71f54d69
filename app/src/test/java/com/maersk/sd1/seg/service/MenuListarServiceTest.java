package com.maersk.sd1.seg.service;

import com.maersk.sd1.adm.service.MenuListarService;
import com.maersk.sd1.common.repository.MenuRepository;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.adm.dto.MenuListarDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MenuListarServiceTest {

    @Mock
    private MenuRepository menuRepository;

    @InjectMocks
    private MenuListarService menuListarService;

    private Menu mockMenu;

    @BeforeEach
    void setUp() {
        mockMenu = new Menu();
        mockMenu.setId(1);
        mockMenu.setTitle("Test Menu");
        mockMenu.setDescription("Test Description");
        mockMenu.setTemplate("Test Template");
        mockMenu.setIcon("Test Icon");
        mockMenu.setOrder(1);
        mockMenu.setStatus(true);
        mockMenu.setHasId(true);
    }

    @Test
    void GivenMenusExist_WhenGetAllMenusWithRolesAndActionsIsCalled_ThenMenusShouldBeReturnedWithRolesAndActions() {
        Object[] mockResult = new Object[12];
        mockResult[0] = new BigDecimal(1);
        mockResult[10] = "Test Role";
        mockResult[11] = "Test Action";

        when(menuRepository.findMenusWithRolesAndActions()).thenReturn(Arrays.<Object[]>asList(mockResult));
        when(menuRepository.findById(1)).thenReturn(Optional.of(mockMenu));

        List<MenuListarDTO.Menu> menus = menuListarService.getAllMenusWithRolesAndActions();

        assertNotNull(menus);
        assertEquals(1, menus.size());

        MenuListarDTO.Menu menu = menus.get(0);
        assertEquals(1, menu.getMenuId());
        assertEquals("Test Menu", menu.getTitulo());
        assertEquals("Test Description", menu.getDescripcion());
        assertEquals("Test Template", menu.getPlantilla());
        assertEquals("Test Icon", menu.getIcono());
        assertTrue(menu.getEstado());
        assertTrue(menu.getTieneId());

        assertNotNull(menu.getNombre());
        assertEquals("Test Role", menu.getNombre());

        assertNotNull(menu.getDefectoActivo());
        assertEquals("Test Action", menu.getDefectoActivo());
    }

    @Test
    void GivenNoMenusExist_WhenGetAllMenusWithRolesAndActionsIsCalled_ThenEmptyListShouldBeReturned() {
        when(menuRepository.findMenusWithRolesAndActions()).thenReturn(Arrays.asList());

        List<MenuListarDTO.Menu> menus = menuListarService.getAllMenusWithRolesAndActions();

        assertNotNull(menus);
        assertTrue(menus.isEmpty());
    }

    @Test
    void GivenInvalidMenuId_WhenGetAllMenusWithRolesAndActionsIsCalled_ThenIllegalArgumentExceptionShouldBeThrown() {
        Object[] mockResult = new Object[12];
        mockResult[0] = new BigDecimal(999);

        when(menuRepository.findMenusWithRolesAndActions()).thenReturn(Arrays.<Object[]>asList(mockResult));
        when(menuRepository.findById(999)).thenReturn(Optional.empty());

        assertThrows(IllegalArgumentException.class, () -> menuListarService.getAllMenusWithRolesAndActions());
    }

    @Test
    void GivenRoleIsNull_WhenGetAllMenusWithRolesAndActionsIsCalled_ThenRoleShouldBeNull() {
        Object[] mockResult = new Object[12];
        mockResult[0] = new BigDecimal(1);
        mockResult[10] = null;
        mockResult[11] = "Test Action";

        when(menuRepository.findMenusWithRolesAndActions()).thenReturn(Arrays.<Object[]>asList(mockResult));
        when(menuRepository.findById(1)).thenReturn(Optional.of(mockMenu));

        List<MenuListarDTO.Menu> menus = menuListarService.getAllMenusWithRolesAndActions();

        assertNotNull(menus);
        MenuListarDTO.Menu menu = menus.get(0);

        assertNotNull(menu.getDefectoActivo());
        assertNull(menu.getNombre());
    }

    @Test
    void GivenActionIsNull_WhenGetAllMenusWithRolesAndActionsIsCalled_ThenActionShouldBeNull() {
        Object[] mockResult = new Object[12];
        mockResult[0] = new BigDecimal(1);
        mockResult[10] = "Test Role";
        mockResult[11] = null;

        when(menuRepository.findMenusWithRolesAndActions()).thenReturn(Arrays.<Object[]>asList(mockResult));
        when(menuRepository.findById(1)).thenReturn(Optional.of(mockMenu));

        List<MenuListarDTO.Menu> menus = menuListarService.getAllMenusWithRolesAndActions();

        assertNotNull(menus);
        MenuListarDTO.Menu menu = menus.get(0);

        assertNotNull(menu.getNombre());
        assertNull(menu.getDefectoActivo());
    }
}
