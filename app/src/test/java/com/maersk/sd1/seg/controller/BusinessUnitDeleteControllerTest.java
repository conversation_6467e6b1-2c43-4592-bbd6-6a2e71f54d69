package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.BusinessUnitDeleteInput;
import com.maersk.sd1.seg.dto.BusinessUnitDeleteOutput;
import com.maersk.sd1.seg.service.BusinessUnitDeleteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BusinessUnitDeleteControllerTest {

    @Mock
    private BusinessUnitDeleteService businessUnitDeleteService;

    @InjectMocks
    private BusinessUnitDeleteController businessUnitDeleteController;

    private BusinessUnitDeleteInput.Root request;

    @BeforeEach
    void setUp() {
        BusinessUnitDeleteInput.Input input = new BusinessUnitDeleteInput.Input();
        input.setUnidadNegocioId(1);
        input.setUsuarioId(1);

        BusinessUnitDeleteInput.Prefix prefix = new BusinessUnitDeleteInput.Prefix();
        prefix.setInput(input);

        request = new BusinessUnitDeleteInput.Root();
        request.setPrefix(prefix);
    }

    @Test
    void testDeleteBusinessUnitSuccess() {
        BusinessUnitDeleteOutput output = new BusinessUnitDeleteOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Business unit deleted successfully");

        when(businessUnitDeleteService.deleteBusinessUnit(any(Integer.class), any(Integer.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<BusinessUnitDeleteOutput>> response = businessUnitDeleteController.deleteBusinessUnit(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getResult().getRespEstado());
        assertEquals("Business unit deleted successfully", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testDeleteBusinessUnitInvalidRequest() {
        request.setPrefix(null);

        ResponseEntity<ResponseController<BusinessUnitDeleteOutput>> response = businessUnitDeleteController.deleteBusinessUnit(request);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Invalid request", Objects.requireNonNull(response.getBody()).getMessage());
    }

    @Test
    void testDeleteBusinessUnitException() {
        when(businessUnitDeleteService.deleteBusinessUnit(any(Integer.class), any(Integer.class)))
                .thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<BusinessUnitDeleteOutput>> response = businessUnitDeleteController.deleteBusinessUnit(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(0, Objects.requireNonNull(response.getBody()).getResult().getRespEstado());
        assertEquals("java.lang.RuntimeException: Service error", response.getBody().getResult().getRespMensaje());
    }
}