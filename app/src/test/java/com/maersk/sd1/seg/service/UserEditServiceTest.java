package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.CompanyUserRepository;
import com.maersk.sd1.common.repository.OauthAccessTokenRepository;
import com.maersk.sd1.common.repository.OauthAccessHistoryRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.dto.UserEditOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserEditServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private CompanyUserRepository companyUserRepository;

    @Mock
    private OauthAccessTokenRepository oauthAccessTokenRepository;

    @Mock
    private OauthAccessHistoryRepository oauthAccessHistoryRepository;

    @InjectMocks
    private UserEditService userEditService;

    private User user;

    @BeforeEach
    public void setUp() {
        user = new User();
        user.setId(1);
        user.setAlias("oldAlias");
        user.setMail("<EMAIL>");
    }

    @Test
    void testEditUser_UserNotFound() {
        when(userRepository.findById(anyInt())).thenReturn(Optional.empty());

        UserEditOutputDTO output = userEditService.editUser(1, "newId", "editId", "<EMAIL>", "names", "firstLastName", "secondLastName", 1, "A", 1, Arrays.asList(1, 2));

        assertEquals(0, output.getRespEstado());
        assertEquals("Usuario no encontrado", output.getRespMensaje());
    }

    @Test
    void testEditUser_Success() {
        when(userRepository.findById(anyInt())).thenReturn(Optional.of(user));
        when(userRepository.existsByAliasIgnoreCase(anyString())).thenReturn(false);

        UserEditOutputDTO output = userEditService.editUser(1, "newId", "1", "<EMAIL>", "names", "firstLastName", "secondLastName", 1, "A", 1, Arrays.asList(1, 2));

        assertEquals(1, output.getRespEstado());
        assertEquals("Usuario actualizado correctamente", output.getRespMensaje());
    }

    @Test
    void testEditUser_AliasExists() {
        when(userRepository.existsByAliasIgnoreCase(anyString())).thenReturn(true);

        UserEditOutputDTO output = userEditService.editUser(1, "existingAlias", "1", "<EMAIL>", "names", "firstLastName", "secondLastName", 1, "A", 1, Arrays.asList(1, 2));

        assertEquals(2, output.getRespEstado());
        assertEquals("El Id existe, ingrese otro", output.getRespMensaje());
    }

    @Test
    void testEditUser_Exception() {
        when(userRepository.findById(anyInt())).thenThrow(new RuntimeException("Database error"));

        UserEditOutputDTO output = userEditService.editUser(1, "newId", "editId", "<EMAIL>", "names", "firstLastName", "secondLastName", 1, "A", 1, Arrays.asList(1, 2));

        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
    }
}