package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.dto.MenuEditResponseDTO;
import com.maersk.sd1.seg.dto.MenuEditRequestDTO;
import com.maersk.sd1.common.repository.MenuRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;

class ListMenuServiceTest {

    @Mock
    private MenuRepository menuRepository;

    @InjectMocks
    private MenuService menuService;

    @BeforeEach
    public void setup() {

        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidMenuId_When_ExecuteMenuEditProcedure_Then_StatusIs1AndMessageIndicatesSuccess() {

        Integer menuId = 1;
        MenuEditRequestDTO.Root request = new MenuEditRequestDTO.Root();
        request.getPrefix().getInput().setMenuId(menuId);

        doNothing().when(menuRepository).executeMenuEditProcedure(menuId);

        MenuEditResponseDTO.Root response = menuService.editMenu(request);
        assertEquals(1, response.getPrefix().getOutput().getStatus());
        assertEquals("Registro bajado correctamente", response.getPrefix().getOutput().getMessage());
    }

    @Test
    void Given_InvalidMenuId_When_ExecuteMenuEditProcedure_Then_StatusIs0AndMessageIndicatesError() {

        Integer menuId = 1;
        MenuEditRequestDTO.Root request = new MenuEditRequestDTO.Root();
        request.getPrefix().getInput().setMenuId(menuId);

        doThrow(new RuntimeException("Database error")).when(menuRepository).executeMenuEditProcedure(menuId);

        MenuEditResponseDTO.Root response = menuService.editMenu(request);
        assertEquals(0, response.getPrefix().getOutput().getStatus());
        assertEquals("Error: Database error", response.getPrefix().getOutput().getMessage());
    }
}
