package com.maersk.sd1.seg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.maersk.sd1.common.model.NotificationJob;
import com.maersk.sd1.common.model.NotificationJobHistory;
import com.maersk.sd1.common.repository.NotificationJobHistoryRepository;
import com.maersk.sd1.common.repository.NotificationJobRepository;
import com.maersk.sd1.seg.dto.NotificationJobProcessRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
class NotificationJobProcessRegisterServiceTest {

    @Mock
    private NotificationJobRepository notificationJobRepository;

    @Mock
    private NotificationJobHistoryRepository notificationJobHistoryRepository;

    @InjectMocks
    private NotificationJobProcessRegisterService notificationJobProcessRegisterService;

    private NotificationJob notificationJob;
    private NotificationJobHistory notificationJobHistory;

    @BeforeEach
    void setup() {
        notificationJob = new NotificationJob();
        notificationJob.setId(1);
        notificationJob.setLastExecutionDate(LocalDateTime.now());

        notificationJobHistory = new NotificationJobHistory();
        notificationJobHistory.setId(1);
        notificationJobHistory.setNotificacionJob(notificationJob);
        notificationJobHistory.setDate(LocalDateTime.now());
        notificationJobHistory.setIndicatorExecutedEmail('Y');
        notificationJobHistory.setIndicatorWebExecuted('N');
        notificationJobHistory.setIndicatorAppExecuted('N');
    }

    @Test
    void testRegisterNotificationJobProcessSuccess() {
        when(notificationJobRepository.findById(1)).thenReturn(Optional.of(notificationJob));
        when(notificationJobHistoryRepository.saveAndFlush(any(NotificationJobHistory.class))).thenReturn(notificationJobHistory);

        NotificationJobProcessRegisterOutput result = notificationJobProcessRegisterService.registerNotificationJobProcess(
                1, 'Y', 'N', 'N', 1);

        assertNotNull(result);
        assertEquals(1, result.getRespEstado(), "Expected respEstado to be 1");
        assertEquals("Record successfully created.", result.getRespMensaje(), "Expected respMensaje to be 'Record successfully created.'");

        verify(notificationJobRepository, times(1)).saveAndFlush(notificationJob);
        verify(notificationJobHistoryRepository, times(1)).saveAndFlush(any(NotificationJobHistory.class));
    }

    @Test
    void testRegisterNotificationJobProcessJobNotFound() {
        when(notificationJobRepository.findById(1)).thenReturn(Optional.empty());

        NotificationJobProcessRegisterOutput result = notificationJobProcessRegisterService.registerNotificationJobProcess(
                1, 'Y', 'N', 'N', 1);

        assertNotNull(result);
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("NotificationJob not found with id"));
        assertEquals(0, result.getRespNewId());

        verify(notificationJobRepository, never()).saveAndFlush(any(NotificationJob.class));
        verify(notificationJobHistoryRepository, never()).saveAndFlush(any(NotificationJobHistory.class));
    }

    @Test
    void testRegisterNotificationJobProcessException() {
        when(notificationJobRepository.findById(1)).thenThrow(new RuntimeException("Database error"));

        NotificationJobProcessRegisterOutput result = notificationJobProcessRegisterService.registerNotificationJobProcess(
                1, 'Y', 'N', 'N', 1);

        assertNotNull(result);
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Database error"));
        assertEquals(0, result.getRespNewId());

        verify(notificationJobRepository, never()).saveAndFlush(any(NotificationJob.class));
        verify(notificationJobHistoryRepository, never()).saveAndFlush(any(NotificationJobHistory.class));
    }
}
