package com.maersk.sd1.seg.service.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.RoleRegisterController;
import com.maersk.sd1.seg.dto.RoleRegisterInput;
import com.maersk.sd1.seg.dto.RoleRegisterOutput;
import com.maersk.sd1.seg.service.RoleRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RoleRegisterControllerTest {

    @Mock
    private RoleRegisterService roleRegisterService;

    @InjectMocks
    private RoleRegisterController roleRegisterController;

    private RoleRegisterInput.Root request;
    private RoleRegisterInput.Input input;

    @BeforeEach
    void setUp() {
        input = new RoleRegisterInput.Input();
        input.setRoleId("testRoleId");
        input.setRoleName("testRoleName");
        input.setRoleState('1');
        input.setUserRegistrationId(1);
        input.setMenuProjectDefaultId(1);

        request = new RoleRegisterInput.Root();
        RoleRegisterInput.Prefix prefix = new RoleRegisterInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void Given_ValidInput_When_RegisterRole_Then_ReturnSuccessResponse() {
        RoleRegisterOutput output = new RoleRegisterOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Rol registrado correctamente");

        when(roleRegisterService.registerRole(input)).thenReturn(output);

        ResponseEntity<ResponseController<RoleRegisterOutput>> response = roleRegisterController.registerRole(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Rol registrado correctamente", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void Given_Exception_When_RegisterRole_Then_ReturnErrorResponse() {
        when(roleRegisterService.registerRole(input)).thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<RoleRegisterOutput>> response = roleRegisterController.registerRole(request);

        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Database error", response.getBody().getResult().getRespMensaje());
    }
}
