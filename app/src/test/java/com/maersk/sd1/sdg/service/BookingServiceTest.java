package com.maersk.sd1.sdg.service;

import com.maersk.sd1.sdg.dto.GateoutGeneralAssignmentContainerFindBooking;
import com.maersk.sd1.sdg.repository.SdgBookingDetailRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class BookingServiceTest {

    @InjectMocks
    private BookingService bookingService;

    @Mock
    private SdgBookingDetailRepository sdgBookingDetailRepository;

    @BeforeEach
    void setup() {
        openMocks(this);
    }

    @Test
    void givenBookingIdWithDetails_whenFindBookingDetailsByBookingId_thenReturnDetailsWithAssignedQuantities() {
        // GIVEN
        Integer bookingId = 1;
        List<GateoutGeneralAssignmentContainerFindBooking> mockDetails = Arrays.asList(
                new GateoutGeneralAssignmentContainerFindBooking(101, 1, 40, 1, 20000, 10, "Rule 1"),
                new GateoutGeneralAssignmentContainerFindBooking(102, 2, 20, 2, 10000, 5, "Rule 2")
        );
        when(sdgBookingDetailRepository.findBookingDetailsByBookingId(bookingId)).thenReturn(mockDetails);
        when(sdgBookingDetailRepository.countActiveContainerPreassignments(101)).thenReturn(5);
        when(sdgBookingDetailRepository.countActiveContainerPreassignments(102)).thenReturn(3);

        // WHEN
        List<GateoutGeneralAssignmentContainerFindBooking> result = bookingService.findBookingDetailsByBookingId(bookingId);

        // THEN
        assertEquals(2, result.size());
        assertEquals(5, result.get(0).getQuantityAssigned());
        assertEquals(3, result.get(1).getQuantityAssigned());
        verify(sdgBookingDetailRepository, times(1)).findBookingDetailsByBookingId(bookingId);
        verify(sdgBookingDetailRepository, times(1)).countActiveContainerPreassignments(101);
        verify(sdgBookingDetailRepository, times(1)).countActiveContainerPreassignments(102);
    }

    @Test
    void givenBookingIdWithoutDetails_whenFindBookingDetailsByBookingId_thenReturnEmptyList() {
        // GIVEN
        Integer bookingId = 2;
        when(sdgBookingDetailRepository.findBookingDetailsByBookingId(bookingId)).thenReturn(Collections.emptyList());

        // WHEN
        List<GateoutGeneralAssignmentContainerFindBooking> result = bookingService.findBookingDetailsByBookingId(bookingId);

        // THEN
        assertEquals(0, result.size());
        verify(sdgBookingDetailRepository, times(1)).findBookingDetailsByBookingId(bookingId);
        verify(sdgBookingDetailRepository, never()).countActiveContainerPreassignments(anyInt());
    }

    @Test
    void givenBookingDetailsWithNullAssignedQuantity_whenFindBookingDetailsByBookingId_thenHandleNullProperly() {
        // GIVEN
        Integer bookingId = 3;
        List<GateoutGeneralAssignmentContainerFindBooking> mockDetails = Collections.singletonList(
                new GateoutGeneralAssignmentContainerFindBooking(103, 3, 40, 3, 30000, 20, "Rule 3")
        );
        when(sdgBookingDetailRepository.findBookingDetailsByBookingId(bookingId)).thenReturn(mockDetails);
        when(sdgBookingDetailRepository.countActiveContainerPreassignments(103)).thenReturn(null);

        // WHEN
        List<GateoutGeneralAssignmentContainerFindBooking> result = bookingService.findBookingDetailsByBookingId(bookingId);

        // THEN
        assertEquals(1, result.size());
        assertNull(result.getFirst().getQuantityAssigned());
        verify(sdgBookingDetailRepository, times(1)).findBookingDetailsByBookingId(bookingId);
        verify(sdgBookingDetailRepository, times(1)).countActiveContainerPreassignments(103);
    }
}
