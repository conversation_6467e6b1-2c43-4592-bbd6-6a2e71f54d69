package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.repository.ResourceRepository;
import com.maersk.sd1.sdg.dto.GateoutGeneralDocumentFindInputDTO;
import com.maersk.sd1.sdg.dto.GateoutGeneralDocumentFindOutputDTO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.StoredProcedureQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GateoutGeneralDocumentFindServiceTest {

    @Mock
    private ResourceRepository resourceRepository;

    @Mock
    private EntityManager entityManager;

    @InjectMocks
    private GateoutGeneralDocumentFindService gateoutGeneralDocumentFindService;

    private GateoutGeneralDocumentFindInputDTO.Input validInput;
    private StoredProcedureQuery storedProcedureQuery;

    @BeforeEach
    void setUp() {
        validInput = new GateoutGeneralDocumentFindInputDTO.Input();
        validInput.setSubBusinessUnitLocalId(1L);
        validInput.setEquipmentNumber("EQ123");
        validInput.setDocumentNumber("DOC123");
        validInput.setUserRegistrationId(1L);
        validInput.setLanguajeId(1);
        validInput.setTypeProcess("PROCESS");
        validInput.setApsId(1);
        validInput.setApsData("DATA");

        storedProcedureQuery = mock(StoredProcedureQuery.class);
        when(entityManager.createStoredProcedureQuery(anyString())).thenReturn(storedProcedureQuery);
        when(storedProcedureQuery.registerStoredProcedureParameter(anyString(), any(), any())).thenReturn(storedProcedureQuery);
        when(storedProcedureQuery.setParameter(anyString(), any())).thenReturn(storedProcedureQuery);
    }

    @Test
    void Given_ValidInput_When_FindGateoutGeneralDocument_Then_ReturnsSuccess() {
        List<Object[]> resultState = Collections.singletonList(new Object[]{1, "Success"});
        List<Object[]> documentDetails = Collections.singletonList(new Object[]{
                BigDecimal.ONE, "Alias", "RefDoc", "Vessel", "OpType", "ShipLine", "Shipper", "Consignee",
                1, "EQ123", "Size", "Type", 1, "ISO", BigDecimal.ONE, true, true, true, true,
                1, 1, 1, 1, "Merch", "Item", "Depot"
        });
        List<Object[]> appointmentDetails = Collections.singletonList(new Object[]{
                "EQ123", "APS1", 'A', 'B', 1, "ETD", 1, "TruckCompany"
        });
        List<Object[]> apsData = Collections.singletonList(new Object[]{
                1L, "2023-01-01", "DOC123", "Driver", "License", "Plate", "Process", "Value", "Code"
        });
        List<Object[]> gateoutGeneralAppointmentValidate = Collections.singletonList(new Object[]{"Type", "Alias"});

        when(storedProcedureQuery.execute()).thenReturn(true);
        when(storedProcedureQuery.getResultList())
                .thenReturn(resultState)
                .thenReturn(documentDetails)
                .thenReturn(appointmentDetails)
                .thenReturn(apsData)
                .thenReturn(gateoutGeneralAppointmentValidate);
        when(storedProcedureQuery.hasMoreResults()).thenReturn(true, true, true, true, false);

        GateoutGeneralDocumentFindOutputDTO result = gateoutGeneralDocumentFindService.findGateoutGeneralDocument(validInput);

        assertNotNull(result);
        assertEquals(1, result.getResultState().size());
        assertEquals(1, result.getDocumentDetails().size());
        assertEquals(1, result.getAppointmentDetails().size());
        assertEquals(1, result.getApsData().size());
        assertEquals(1, result.getGateoutGeneralAppointmentValidate().size());
    }

}