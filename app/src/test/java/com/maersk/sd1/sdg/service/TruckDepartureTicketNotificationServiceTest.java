package com.maersk.sd1.sdg.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdg.dto.EirNotificationsDetailsDTO;
import com.maersk.sd1.sdg.dto.EmailProcessResponseDTO;
import com.maersk.sd1.sdg.dto.EmailTemplateDetailsDTO;
import com.maersk.sd1.sdg.repository.SdgEirRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

class TruckDepartureTicketNotificationServiceTest {

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private EirNotificationRepository eirNotificationRepository;

    @Mock
    private SdgEirRepository eirRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private SystemRepository systemRepository;

    @Mock
    private EmailTemplateRepository emailTemplateRepository;

    @Mock
    private CompanyContactRepository companyContactRepository;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private TruckDepartureTicketNotificationService truckDepartureTicketNotificationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidNotificationId_When_NotificationProcessingSuccessful_Then_ReturnSuccessResponse() throws Exception {
        // Given
        int eirNotificationId = 1;
        List<String> notificationAliases = List.of(
                Parameter.SD1_EIR_NOTIFICATION_ALIAS,
                Parameter.SD1_EIR_NOTIFICATION_PENDING_EMAIL_ALIAS
        );

        when(catalogRepository.findIdsByAliases(notificationAliases)).thenReturn(
                List.of(
                        new Object[]{Parameter.SD1_EIR_NOTIFICATION_ALIAS, 101},
                        new Object[]{Parameter.SD1_EIR_NOTIFICATION_PENDING_EMAIL_ALIAS, 102}
                )
        );
        when(eirNotificationRepository.findEirIdByEirNotificationId(eirNotificationId)).thenReturn(123);

        EirNotificationsDetailsDTO detailsDTO = EirNotificationsDetailsDTO.builder()
                .businessUnitId(1)
                .containerNumber("C123")
                .chassisNumber("CH123")
                .build();
        when(eirRepository.findEirDetailsByEirNotificationIdAndEirId(123)).thenReturn(detailsDTO);
        when(businessUnitRepository.findAliasByBusinessUnitId(1)).thenReturn("BU_ALIAS");
        when(eirNotificationRepository.ruleGeneralGetOut(eq("BU_ALIAS"), any(), any()))
                .thenReturn(Map.of("r_action", "true"));
        when(systemRepository.findSystemIdByName("SD1")).thenReturn(10);

        EmailTemplateDetailsDTO emailTemplate = EmailTemplateDetailsDTO.builder()
                .copy("copy")
                .copyHidden("copyHidden")
                .title("Title with ${EIR_ID}")
                .build();
        when(emailTemplateRepository.findEmailTemplateDetailsById("sds_truck_departure_ticket_notification"))
                .thenReturn(emailTemplate);

        when(companyContactRepository.findDistinctEmailsByCompanyAndContactType(any(), any()))
                .thenReturn(List.of("<EMAIL>", "<EMAIL>"));

        when(objectMapper.writeValueAsString(any())).thenReturn("{}", "[]");

        // When
        EmailProcessResponseDTO response = truckDepartureTicketNotificationService.processTicketEditNotification(eirNotificationId);

        // Then
        assertNotNull(response);
        verify(eirNotificationRepository).processEmail("sds_truck_departure_ticket_notification",
                "<EMAIL>;<EMAIL>", "copy","copyHidden","Title with 123",
                "{}",10,"sdg.truck_departure_ticket_notification",123,"1",""
        );

        // Verify additional methods were called once
        verify(catalogRepository).findIdsByAliases(notificationAliases);
        verify(eirNotificationRepository).findEirIdByEirNotificationId(eirNotificationId);
        verify(eirRepository).findEirDetailsByEirNotificationIdAndEirId(123);
        verify(businessUnitRepository).findAliasByBusinessUnitId(1);
        verify(emailTemplateRepository).findEmailTemplateDetailsById("sds_truck_departure_ticket_notification");
        verify(companyContactRepository).findDistinctEmailsByCompanyAndContactType(any(), any());
        verify(systemRepository).findSystemIdByName("SD1");
    }

    @Test
    void Given_InvalidNotificationId_When_NotificationNotFound_Then_ReturnDefaultResponse() {
        // Given
        int eirNotificationId = 999;
        when(eirNotificationRepository.findEirIdByEirNotificationId(eirNotificationId)).thenReturn(null);

        // When
        EmailProcessResponseDTO response = truckDepartureTicketNotificationService.processTicketEditNotification(eirNotificationId);

        // Then
        assertNotNull(response);
        assertEquals(0, response.getResponseStatus());
        assertEquals("No action taken", response.getResponseMessage());
        assertEquals("", response.getResponseEmail());

        verify(eirRepository, never()).findEirDetailsByEirNotificationIdAndEirId(anyInt());
        verify(businessUnitRepository, never()).findAliasByBusinessUnitId(anyInt());
        verify(companyContactRepository, never()).findDistinctEmailsByCompanyAndContactType(any(), any());
    }

    @Test
    void Given_EmptyRecipientList_When_NotificationCannotBeSent_Then_UpdateNotificationStatus() {
        // Given
        int eirNotificationId = 2;
        EmailTemplateDetailsDTO emailTemplate = EmailTemplateDetailsDTO.builder()
                .copy("copy")
                .copyHidden("copyHidden")
                .title("Title with ${EIR_ID}")
                .build();
        List<String> notificationAliases = List.of(
                Parameter.SD1_EIR_NOTIFICATION_ALIAS,
                Parameter.SD1_EIR_NOTIFICATION_PENDING_EMAIL_ALIAS
        );
        when(catalogRepository.findIdsByAliases(notificationAliases)).thenReturn(
                List.of(
                        new Object[]{Parameter.SD1_EIR_NOTIFICATION_ALIAS, 101},
                        new Object[]{Parameter.SD1_EIR_NOTIFICATION_PENDING_EMAIL_ALIAS, 102}
                )
        );
        when(eirNotificationRepository.findEirIdByEirNotificationId(eirNotificationId)).thenReturn(124);

        EirNotificationsDetailsDTO detailsDTO = EirNotificationsDetailsDTO.builder()
                .businessUnitId(1)
                .containerNumber("C124")
                .chassisNumber("CH124")
                .build();
        when(eirRepository.findEirDetailsByEirNotificationIdAndEirId(124)).thenReturn(detailsDTO);
        when(businessUnitRepository.findAliasByBusinessUnitId(1)).thenReturn("BU_ALIAS");
        when(companyContactRepository.findDistinctEmailsByCompanyAndContactType(any(), any()))
                .thenReturn(Collections.emptyList());
        when(eirNotificationRepository.ruleGeneralGetOut(eq("BU_ALIAS"), any(), any()))
                .thenReturn(Map.of("r_action", "true"));
        when(systemRepository.findSystemIdByName("SD1")).thenReturn(10);
        when(emailTemplateRepository.findEmailTemplateDetailsById("sds_truck_departure_ticket_notification"))
                .thenReturn(emailTemplate);

        // When
        EmailProcessResponseDTO response = truckDepartureTicketNotificationService.processTicketEditNotification(eirNotificationId);

        // Then
        assertNotNull(response);
        verify(eirNotificationRepository).updateEirNotificationStatus(102, eirNotificationId);
        verify(eirNotificationRepository, never()).processEmail(anyString(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyInt(), anyString(), anyInt(), anyString(), anyString());
    }

    @Test
    void Given_EmailTemplateWithNullFields_When_Processed_Then_ReturnErrorStatus() throws Exception {
        // Given
        int eirNotificationId = 3;
        List<String> notificationAliases = List.of(
                Parameter.SD1_EIR_NOTIFICATION_ALIAS,
                Parameter.SD1_EIR_NOTIFICATION_PENDING_EMAIL_ALIAS
        );
        when(catalogRepository.findIdsByAliases(notificationAliases)).thenReturn(
                List.of(new Object[]{Parameter.SD1_EIR_NOTIFICATION_ALIAS, 101},
                        new Object[]{Parameter.SD1_EIR_NOTIFICATION_PENDING_EMAIL_ALIAS, 102})
        );

        when(eirNotificationRepository.findEirIdByEirNotificationId(eirNotificationId)).thenReturn(125);
        EirNotificationsDetailsDTO eirDetails = EirNotificationsDetailsDTO.builder()
                .businessUnitId(2)
                .containerNumber("C125")
                .chassisNumber("CH125")
                .build();
        when(eirRepository.findEirDetailsByEirNotificationIdAndEirId(125)).thenReturn(eirDetails);

        when(businessUnitRepository.findAliasByBusinessUnitId(2)).thenReturn("BU_ALIAS");
        when(eirNotificationRepository.ruleGeneralGetOut(eq("BU_ALIAS"), any(), any()))
                .thenReturn(Map.of("r_action", "true"));

        when(systemRepository.findSystemIdByName("SD1")).thenReturn(10);

        EmailTemplateDetailsDTO emailTemplate = EmailTemplateDetailsDTO.builder()
                .copy(null)
                .copyHidden(null)
                .title(null)
                .build();
        when(emailTemplateRepository.findEmailTemplateDetailsById("sds_truck_departure_ticket_notification"))
                .thenReturn(emailTemplate);

        when(companyContactRepository.findDistinctEmailsByCompanyAndContactType(any(), any()))
                .thenReturn(Collections.emptyList());

        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(eirNotificationRepository.processEmail(
                eq("sds_truck_departure_ticket_notification"),
                eq(""),
                eq(""),
                eq(""),
                eq(""),
                eq("{}"),
                eq(10),
                eq("sdg.truck_departure_ticket_notification"),
                eq(125),
                eq("1"),
                eq("")
        )).thenReturn(Map.of(
                "resp_estado", 2,
                "resp_mensaje", "No recipients found for notification",
                "resp_correo", ""
        ));

        // When
        EmailProcessResponseDTO response = truckDepartureTicketNotificationService.processTicketEditNotification(eirNotificationId);

        // Then
        assertNotNull(response);
        assertEquals(2, response.getResponseStatus()); // Error status
        assertEquals("No recipients found for notification", response.getResponseMessage());
        assertEquals("", response.getResponseEmail());

        verify(eirNotificationRepository).updateEirNotificationStatus(102, eirNotificationId);

        verify(eirNotificationRepository).processEmail(
                eq("sds_truck_departure_ticket_notification"),
                eq(""),
                eq(""),
                eq(""),
                eq(""),
                eq("{}"),
                eq(10),
                eq("sdg.truck_departure_ticket_notification"),
                eq(125),
                eq("1"),
                eq("")
        );
    }
}
