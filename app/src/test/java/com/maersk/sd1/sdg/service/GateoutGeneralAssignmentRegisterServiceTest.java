package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralAssignmentRegisterInput;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralAssignmentRegisterOutput;
import com.maersk.sd1.sdg.controller.dto.SdggateoutGeneralAssignmentContainerFindOutput;
import com.maersk.sd1.sdg.controller.dto.SdggateoutGeneralAssignmentContainerFindOutputResult;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.maersk.sd1.common.Parameter.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class GateoutGeneralAssignmentRegisterServiceTest {

    @Mock
    private GESCatalogService catalogService;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private EirRepository eirRepository;
    @Mock
    private MessageLanguageService messagelanguageService;
    @Mock
    private GateoutGeneralAssignmentContainerFindService gateoutGeneralAssignmentContainerFindService;
    @Mock
    private StockEmptyRepository stockEmptyRepository;
    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;
    @Mock
    private BookingRepository bookingRepository;
    @Mock
    private BookingDetailRepository bookingDetailRepository;
    @Mock
    private SystemRuleRepository systemRuleRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private StockFullRepository stockFullRepository;
    @Mock
    private EirChassisRepository eirChassisRepository;
    @Mock
    private ChassisRepository chassisRepository;
    @Mock
    private ChassisDocumentRepository chassisDocumentRepository;
    @Mock
    private ChassisBookingDocumentRepository chassisBookingDocumentRepository;
    @Mock
    private StockChassisRepository stockChassisRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    @Mock
    private TransportPlanningDetailRepository transportPlanningDetailRepository;
    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    @Mock
    private InspectionGateRepository inspectionGateRepository;
    @Mock
    private AttachmentRepository attachmentRepository;
    @Mock
    private InspectionGatePhotoRepository inspectionGatePhotoRepository;
    @Mock
    private ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    @Mock
    private GateTransmissionSettingRepository gateTransmissionSettingRepository;
    @Mock
    private BusinessUnitRepository businessUnitRepository;
    @Mock
    private GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;
    @Mock
    private GateTransmissionRepository gateTransmissionRepository;
    @Mock
    private LogRepository logRepository;

    @InjectMocks
    private GateoutGeneralAssignmentRegisterService gateoutGeneralAssignmentRegisterService;

    private List<String> catalogAliases = List.of(
            CATALOG_TYPE_CONTAINER_DRY_ALIAS,
            CATALOG_TYPE_CONTAINER_HC_ALIAS,
            CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS,
            CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
            CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS,
            IS_DOCUMENT_TYPE_BK,
            IS_DOCUMENT_TYPE_BL,
            CATALOG_DOCUMENT_CREATION_ORIGIN_GATE_OUT_AUTOMATIC_ALIAS,
            CATALOG_MEASURE_WEIGHT_KG_ALIAS,
            CATALOG_TRK_PLAN_IN_PROCESS,
            CATALOG_CHASSIS_IN_PROGRESS,
            CATALOG_CHASSIS_IN_PENDING_ALIAS,
            CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS,
            IS_TELEX_FORMAT,
            GRADE_NO_CLASS,
            CATALOG_CONTAINER_TYPE
    );

    private List<String> dummyContainerList = List.of("NO-CNT", "NOT APPLICA");

    private HashMap<String, Integer> catalogIds;

    private Pageable pageable = PageRequest.of(0, 1);

    public void mockCatalogs() {
        catalogIds = new HashMap<>();
        for (String catalogAlias : catalogAliases.stream().distinct().toList()) {
            catalogIds.put(catalogAlias, catalogAliases.indexOf(catalogAlias) + 20001);
        }
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void givenValidInput_whenAssignGateOut_thenResultOk() throws Exception {

        this.mockCatalogs();

        // Constants
        final Integer USER_REGISTRATION_ID = 1;
        final Integer LANGUAGE_ID = 1;
        final Integer EIR_ID = 1001;
        final Integer CONTAINER_ID = 2001;
        final Integer DUMMY_CONTAINER_ID = 2002;
        final Integer NOT_APPLICA_CONTAINER_ID = 2003;
        final String SUCCESS_MESSAGE = "Success";
        final String SEAL1 = "Seal1";
        final String SEAL2 = "Seal2";
        final String SEAL3 = "Seal3";
        final String SEAL4 = "Seal4";
        final String CONTAINER_NUMBER = "TSTU4000001";
        final Integer CONTAINER_TARE = 1000;
        final Integer MAXIMUM_PAYLOAD = 2000;
        final String NO_CNT = "NO-CNT";
        final String NOT_APPLICA = "NOT APPLICA";
        final Integer CAT_CONTAINER_SIZE_ID = 20017;
        final Integer CAT_CONTAINER_TYPE_ID = catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS);
        final Integer CAT_EQUIP_MEASURE_TARE_ID = catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS);
        final Integer CAT_EQUIP_MEASURE_PAYLOAD_ID = catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS);
        final Integer ISO_CODE_ID = 5001;
        final Integer CAT_GRADE_ID = catalogIds.get(GRADE_NO_CLASS);
        final Integer CAT_REEFER_TYPE_ID = null;
        final Integer CAT_ENGINE_BRAND_ID = null;
        final Integer BOOKING_ID = 3001;
        final String BOOKING_REMARK_RULE = "";
        final Integer SUB_BUSINESS_UNIT_ID = 4001;
        final Integer CAT_ORIGIN_ID = 20018;
        final Integer BOOKING_DETAIL_ID = 5001;
        final Integer BOOKING_DETAIL_RESERVATION_QUANTITY = 10;
        final Integer SHIPPING_LINE_ID = 6001;
        final Integer VESSEL_PROGRAMMING_DETAIL_ID = 7001;
        final Integer PREASSIGNMENT_ID = 8001;
        final Integer CARGO_DOCUMENT_ID = 9001;
        final String CARGO_DOCUMENT = "TSTCD";
        final Integer CARGO_DOCUMENT_DETAIL_ID = 10001;
        final Integer ATTENDED_CARGO_DOCUMENT_ID = 10002;
        final Integer IMO_ID = 11001;
        final Integer INSPECTION_GATE_ID = 12001;
        final Integer ATTACHMENT1_ID = 13001;
        final Integer ATTACHMENT2_ID = 13002;
        final String SUB_BUSINESS_UNIT_ALIAS = "BUSINESS_UNIT_ALIAS";
        final Integer SYSTEM_RULE_ID = 14001;
        final String SYSTEM_RULES_ALIAS = "system_rule_id";
        JSONArray jsonArray = new JSONArray();
        jsonArray.put(new JSONObject()
                .put("sub_business_unit_local_alias", SUB_BUSINESS_UNIT_ALIAS)
                .put("type_product_integration", "sdy")
                .put("type_process", "ago")
        );
        final String SYSTEM_RULE = jsonArray.toString();
        final String PHOTOS = "[{\"nombre\":\"foto1.jpg\",\"peso\":\"2\",\"formato\":\"jpg\",\"ubicacion\":\"/imagenes/foto1.jpg\",\"url\":\"https://example.com/foto1.jpg\",\"id\":\"12345\",\"tipoAdjunto\":1}," +
                "{\"nombre\":\"foto2.png\",\"peso\":\"1\",\"formato\":\"png\",\"ubicacion\":\"/imagenes/foto2.png\",\"url\":\"https://example.com/foto2.png\",\"id\":\"67890\",\"tipoAdjunto\":2}]";

        //Mocking find service response
        SdggateoutGeneralAssignmentContainerFindOutput containerFindOutput = new SdggateoutGeneralAssignmentContainerFindOutput();
        SdggateoutGeneralAssignmentContainerFindOutputResult containerFindResponse = new SdggateoutGeneralAssignmentContainerFindOutputResult();
        containerFindResponse.setResultState(1);
        containerFindOutput.setResult(List.of(containerFindResponse));

        // Mocking entities
        Container container = Container.builder()
                .id(CONTAINER_ID)
                .containerNumber(CONTAINER_NUMBER)
                .catSize(Catalog.builder().id(CAT_CONTAINER_SIZE_ID).build())
                .catContainerType(Catalog.builder().id(CAT_CONTAINER_TYPE_ID).build())
                .containerTare(CONTAINER_TARE)
                .catEquipMeasureTare(Catalog.builder().id(CAT_EQUIP_MEASURE_TARE_ID).build())
                .maximunPayload(MAXIMUM_PAYLOAD)
                .catEquipMeasurePayload(Catalog.builder().id(CAT_EQUIP_MEASURE_PAYLOAD_ID).build())
                .isoCode(IsoCode.builder().id(ISO_CODE_ID).build())
                .catGrade(Catalog.builder().id(CAT_GRADE_ID).build())
                .catReeferType(null)
                .catEngineBrand(null)
                .manufactureDate(LocalDateTime.now())
                .build();

        Booking booking = Booking.builder().id(BOOKING_ID)
                .remarkRulesName(BOOKING_REMARK_RULE)
                .shippingLine(ShippingLine.builder().id(SHIPPING_LINE_ID).build())
                .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                        .id(VESSEL_PROGRAMMING_DETAIL_ID)
                        .build())
                .build();

        Eir eir = Eir.builder()
                .id(EIR_ID)
                .bookingGout(booking)
                .subBusinessUnit(BusinessUnit.builder()
                        .id(SUB_BUSINESS_UNIT_ID)
                        .businesUnitAlias(SUB_BUSINESS_UNIT_ALIAS)
                        .build())
                .catOrigin(Catalog.builder().id(CAT_ORIGIN_ID).build())
                .catEmptyFull(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).build())
                .container(Container.builder().id(DUMMY_CONTAINER_ID).containerNumber(NO_CNT).build())
                .build();

        Eir savedEir = Eir.builder()
                .id(EIR_ID)
                .bookingGout(booking)
                .subBusinessUnit(BusinessUnit.builder().id(SUB_BUSINESS_UNIT_ID).build())
                .catOrigin(Catalog.builder().id(CAT_ORIGIN_ID).build())
                .catEmptyFull(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).build())
                .container(Container.builder().id(CONTAINER_ID).containerNumber(NO_CNT).build())
                .controlAssignmentLight(Short.valueOf("1"))
                .build();

        ContainerPreassignment preassignment = ContainerPreassignment.builder()
                .id(PREASSIGNMENT_ID)
                .bookingDetail(BookingDetail.builder()
                        .id(BOOKING_DETAIL_ID)
                        .booking(Booking.builder()
                                .id(BOOKING_ID)
                                .shippingLine(null)
                                .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(VESSEL_PROGRAMMING_DETAIL_ID).build())
                                .imo(Imo.builder().id(IMO_ID).build())
                                .build())
                        .build())
                .build();

        BookingDetail bookingDetail = BookingDetail.builder()
                .id(BOOKING_DETAIL_ID)
                .reservationQuantity(BOOKING_DETAIL_RESERVATION_QUANTITY)
                .build();

        // Mocking repositories and services methods calls
        when(catalogService.findIdsByAliases(any()))
                .thenReturn(catalogIds);

        when(containerRepository.findByContainerNumbers(dummyContainerList)).thenReturn(
                List.of(Container.builder()
                                .id(DUMMY_CONTAINER_ID)
                                .containerNumber(NO_CNT)
                                .build(),
                        Container.builder()
                                .id(NOT_APPLICA_CONTAINER_ID)
                                .containerNumber(NOT_APPLICA)
                                .build())
        );

        when(containerRepository.findById(anyInt())).thenReturn(Optional.of(container));

        when(eirRepository.findById(anyInt()))
                .thenReturn(Optional.of(eir))
                .thenReturn(Optional.of(savedEir));

        when(gateoutGeneralAssignmentContainerFindService.execute(any())).thenReturn(containerFindOutput);

        when(stockEmptyRepository.findByContainerAndBusinessUnitAndInStockAndActive(anyInt(), anyInt(), eq(true), eq(true)))
                .thenReturn(Optional.of(StockEmpty.builder().build()));

        when(containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionOne(any(), anyInt(), anyInt(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());
        when(containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionTwo(any(), anyInt(), anyInt(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());

        when(bookingDetailRepository.findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListWithNoRemarkRule(BOOKING_ID, CAT_CONTAINER_SIZE_ID, List.of(CAT_CONTAINER_TYPE_ID)))
                .thenReturn(List.of(
                        bookingDetail
                ));

        when(containerPreassignmentRepository.findAllByBookingDetailIdAndActive(List.of(BOOKING_DETAIL_ID), true))
                .thenReturn(List.of(ContainerPreassignment.builder()
                        .id(PREASSIGNMENT_ID)
                        .bookingDetail(bookingDetail)
                        .build()));

        when(stockEmptyRepository.existsByContainerIdAndSubBusinessUnitIdAndInStockAndActive(CONTAINER_ID, SUB_BUSINESS_UNIT_ID, true, true))
                .thenReturn(true);

        when(systemRuleRepository.findRuleByIdAndActiveTrue(anyString())).thenReturn("[]");

        CargoDocument cargoDocument = CargoDocument.builder()
                .id(CARGO_DOCUMENT_ID)
                .cargoDocument(CARGO_DOCUMENT)
                .build();

        CargoDocumentDetail cargoDocumentDetail = CargoDocumentDetail.builder()
                .id(CARGO_DOCUMENT_DETAIL_ID)
                .cargoDocument(cargoDocument)
                .build();

        when(cargoDocumentDetailRepository.findFirstByBookingDetailIdAndActiveWithNoContainer(pageable, BOOKING_DETAIL_ID, true))
                .thenReturn(List.of(cargoDocumentDetail));

        when(eirRepository.save(any(Eir.class))).thenAnswer(invocation -> invocation.getArgument(0));

        when(vesselProgrammingContainerRepository.existsByContainerIdAndVesselProgrammingDetailId(CONTAINER_ID, VESSEL_PROGRAMMING_DETAIL_ID))
                .thenReturn(false);

        when(cargoDocumentDetailRepository.findByBookingDetailIdsAndContainorIdsAndActiveTrue(List.of(BOOKING_DETAIL_ID), List.of()))
                .thenReturn(List.of(CargoDocumentDetail.builder()
                        .id(ATTENDED_CARGO_DOCUMENT_ID)
                        .build()));

        when(inspectionGateRepository.saveAndFlush(any(InspectionGate.class)))
                .thenReturn(InspectionGate.builder().id(INSPECTION_GATE_ID).build());

        when(attachmentRepository.saveAllAndFlush(any()))
                .thenReturn(List.of(
                        Attachment.builder()
                                .id(ATTACHMENT1_ID)
                                .build(),
                        Attachment.builder()
                                .id(ATTACHMENT2_ID)
                                .build()
                ));

        when(inspectionGatePhotoRepository.saveAll(anyList())).thenReturn(List.of());

        when(messagelanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn(SUCCESS_MESSAGE);

        when(systemRuleRepository.findByAliasAndActiveTrue(SYSTEM_RULES_ALIAS))
                .thenReturn(SystemRule.builder()
                        .id(SYSTEM_RULE_ID)
                        .alias(SYSTEM_RULES_ALIAS)
                        .rule(SYSTEM_RULE)
                        .build());

        // Input setup
        GateOutGeneralAssignmentRegisterInput.Input input = GateOutGeneralAssignmentRegisterInput.Input.builder()
                .eirId(EIR_ID)
                .containerId(CONTAINER_ID)
                .seal1(SEAL1)
                .seal2(SEAL2)
                .seal3(SEAL3)
                .seal4(SEAL4)
                .userRegistrationId(USER_REGISTRATION_ID)
                .languageId(LANGUAGE_ID)
                .photos(PHOTOS)
                .systemRuleId(SYSTEM_RULES_ALIAS)
                .build();

        // WHEN
        GateOutGeneralAssignmentRegisterOutput result = gateoutGeneralAssignmentRegisterService.execute(input);

        // THEN
        assertNotNull(result);
        assertEquals(1, result.getResultState());
        verify(eirRepository, times(2)).save(any(Eir.class));
        assertEquals(SUCCESS_MESSAGE, result.getResultMessage());
    }

    @Test
    void givenValidInputForFullContainer_whenAssignGateOut_thenResultOk() throws Exception {

        this.mockCatalogs();

        // Constants
        final Integer USER_REGISTRATION_ID = 1;
        final Integer LANGUAGE_ID = 1;
        final Integer EIR_ID = 1001;
        final Integer CONTAINER_ID = 2001;
        final Integer DUMMY_CONTAINER_ID = 2002;
        final Integer NOT_APPLICA_CONTAINER_ID = 2003;
        final String NO_CNT = "NO-CNT";
        final String NOT_APPLICA = "NOT APPLICA";
        final Integer PLANNING_DETAIL_ID = 3001;
        final Integer BOOKING_ID = 4001;
        final Integer SUB_BUSINESS_UNIT_ID = 5001;
        final Integer VESSEL_PROGRAMMING_DETAIL_ID = 6001;
        final Integer CARGO_DOCUMENT_DETAIL_ID = 7001;
        final Integer CARGO_DOCUMENT_ID = 8001;
        final Integer INSPECTION_GATE_ID = 9001;
        final String SUCCESS_MESSAGE = "Success";
        final String SEAL1 = "Seal1";
        final String SEAL2 = "Seal2";
        final String SEAL3 = "Seal3";
        final String SEAL4 = "Seal4";

        //Mocking find service response
        SdggateoutGeneralAssignmentContainerFindOutput containerFindOutput = new SdggateoutGeneralAssignmentContainerFindOutput();
        SdggateoutGeneralAssignmentContainerFindOutputResult containerFindResponse = new SdggateoutGeneralAssignmentContainerFindOutputResult();
        containerFindResponse.setResultState(1);
        containerFindOutput.setResult(List.of(containerFindResponse));

        // Arrange
        GateOutGeneralAssignmentRegisterInput.Input input = GateOutGeneralAssignmentRegisterInput.Input.builder()
                .eirId(EIR_ID)
                .containerId(CONTAINER_ID)
                .seal1(SEAL1)
                .seal2(SEAL2)
                .seal3(SEAL3)
                .seal4(SEAL4)
                .userRegistrationId(USER_REGISTRATION_ID)
                .languageId(LANGUAGE_ID)
                .planningDetailId(PLANNING_DETAIL_ID)
                .documentCargaDetalleId(CARGO_DOCUMENT_DETAIL_ID) //NECESSARY FOR FULL
                .photos("[]")
                .build();


        Eir eir = Eir.builder()
                .id(EIR_ID)
                .bookingGout(Booking.builder().id(BOOKING_ID).build())
                .catEmptyFull(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).build())
                .container(Container.builder().id(DUMMY_CONTAINER_ID).build())
                .subBusinessUnit(BusinessUnit.builder().id(SUB_BUSINESS_UNIT_ID).build())
                .documentCargoGof(CargoDocument.builder()
                        .id(CARGO_DOCUMENT_ID)
                        .build())
                .build();

        CargoDocumentDetail cargoDocumentDetail = CargoDocumentDetail.builder()
                .id(CARGO_DOCUMENT_DETAIL_ID)
                .receivedWeight(BigDecimal.TEN)
                .catReceivedWeightMeasure(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .cargoDocument(CargoDocument.builder()
                        .catCargoDocumentType(Catalog.builder().id(catalogIds.get(IS_DOCUMENT_TYPE_BL)).build())
                        .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(VESSEL_PROGRAMMING_DETAIL_ID).build())
                        .build())
                .build();

        when(catalogService.findIdsByAliases(any())).thenReturn(catalogIds);
        when(containerRepository.findByContainerNumbers(dummyContainerList)).thenReturn(
                List.of(Container.builder()
                                .id(DUMMY_CONTAINER_ID)
                                .containerNumber(NO_CNT)
                                .build(),
                        Container.builder()
                                .id(NOT_APPLICA_CONTAINER_ID)
                                .containerNumber(NOT_APPLICA)
                                .build())
        );
        when(gateoutGeneralAssignmentContainerFindService.execute(any())).thenReturn(containerFindOutput);
        when(eirRepository.findById(EIR_ID)).thenReturn(Optional.of(eir));
        when(cargoDocumentDetailRepository.findByIdAndWithActiveCargoDocumentAndVesselProgramming(CARGO_DOCUMENT_DETAIL_ID))
                .thenReturn(Optional.of(cargoDocumentDetail));
        when(stockFullRepository.existsByContainerIdAndSubBusinessUnitIdAndInStockAndActive(CONTAINER_ID, SUB_BUSINESS_UNIT_ID, true, true))
                .thenReturn(true);
        /*when(transportPlanningDetailRepository.updateCatStateTrkPlanning(PLANNING_DETAIL_ID, catalogIds.get(CATALOG_TRK_PLAN_IN_PROCESS)))
                .thenReturn(1);*/
        when(eirRepository.save(any(Eir.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(inspectionGateRepository.saveAndFlush(any(InspectionGate.class)))
                .thenReturn(InspectionGate.builder().id(INSPECTION_GATE_ID).build());

        when(messagelanguageService.getMessage("PRC_GO_GENERAL", 14, input.getLanguageId()))
                .thenReturn(SUCCESS_MESSAGE);

        // Act
        GateOutGeneralAssignmentRegisterOutput result = gateoutGeneralAssignmentRegisterService.execute(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getResultState());
        assertEquals(SUCCESS_MESSAGE, result.getResultMessage());
        verify(eirRepository, times(2)).save(any(Eir.class));
        verify(transportPlanningDetailRepository, times(1)).updateCatStateTrkPlanning(PLANNING_DETAIL_ID, catalogIds.get(CATALOG_TRK_PLAN_IN_PROCESS));
    }

    @Test
    void givenValidInputForChassisOperation_whenAssignGateOut_thenResultOk() throws Exception {

        mockCatalogs();

        // Arrange
        final Integer USER_REGISTRATION_ID = 1;
        final Integer LANGUAGE_ID = 1;
        final Integer EIR_ID = 1001;
        final Integer CHASSIS_ID = 3001;
        final Integer SUB_BUSINESS_UNIT_ID = 5001;
        final Integer EIR_CHASSIS_ID = 6001;
        final Integer BOOKING_CHASSIS_ID = 7001;
        final Integer CHASSIS_DOCUMENT_ID = 8001;
        final String CHASSIS_DOCUMENT_NUMBER = "CHSUDOC";
        final Integer CHASSIS_BOOKING_DOCUMENT_ID = 9001;
        final Integer INSPECTION_GATE_ID = 10001;
        final Integer CATALOG_CHASSIS_IN_PROGRESS_ID = catalogIds.get(catalogIds.get(CATALOG_CHASSIS_IN_PROGRESS));
        final Integer CATALOG_CHASSIS_TYPE_ID = 20001;
        final Integer CONTAINER_ID = 2001;
        final Integer DUMMY_CONTAINER_ID = 2002;
        final Integer NOT_APPLICA_CONTAINER_ID = 2003;
        final String NO_CNT = "NO-CNT";
        final String NOT_APPLICA = "NOT APPLICA";
        final String SUCCESS_MESSAGE = "Success";
        final String SEAL1 = "Seal1";
        final String SEAL2 = "Seal2";
        final String SEAL3 = "Seal3";
        final String SEAL4 = "Seal4";


        ChassisDocument documentChassisGO = ChassisDocument.builder()
                .id(CHASSIS_DOCUMENT_ID)
                .documentChassisNumber(CHASSIS_DOCUMENT_NUMBER)
                .build();

        EirChassis eirChassis = EirChassis.builder()
                .id(EIR_CHASSIS_ID)
                .chassisDocumentGo(documentChassisGO)
                .chassisAllocationControl("0")
                .build();

        Eir eir = Eir.builder()
                .id(EIR_ID)
                .eirChassis(eirChassis)
                .subBusinessUnit(BusinessUnit.builder().id(SUB_BUSINESS_UNIT_ID).build())
                .catEmptyFull(Catalog.builder()
                        .id(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))
                        .build())
                .build();

        Chassis chassis = Chassis.builder()
                .id(CHASSIS_ID)
                .catChassisType(Catalog.builder().id(CATALOG_CHASSIS_TYPE_ID).build())
                .chassisNumber("CH12345")
                .build();

        ChassisBookingDocument chassisBookingDocument = ChassisBookingDocument.builder()
                .id(CHASSIS_BOOKING_DOCUMENT_ID)
                .chassisDocument(documentChassisGO)
                .build();

        when(catalogService.findIdsByAliases(any())).thenReturn(catalogIds);
        when(containerRepository.findByContainerNumbers(dummyContainerList)).thenReturn(
                List.of(Container.builder()
                                .id(DUMMY_CONTAINER_ID)
                                .containerNumber(NO_CNT)
                                .build(),
                        Container.builder()
                                .id(NOT_APPLICA_CONTAINER_ID)
                                .containerNumber(NOT_APPLICA)
                                .build())
        );

        when(eirRepository.findById(EIR_ID)).thenReturn(Optional.of(eir));
        when(eirChassisRepository.findById(EIR_CHASSIS_ID))
                .thenReturn(Optional.of(eirChassis));
        when(chassisRepository.findById(CHASSIS_ID)).thenReturn(Optional.of(chassis));
        when(chassisBookingDocumentRepository.findFirstByIdAndChassisTypeIdAndCatStatusIdAndActiveWithNoChassis(pageable, CHASSIS_DOCUMENT_ID, CATALOG_CHASSIS_TYPE_ID, catalogIds.get(CATALOG_CHASSIS_IN_PENDING_ALIAS), true))
                .thenReturn(List.of(
                        chassisBookingDocument
                ));
        when(stockChassisRepository.existsByChassisIdAndInStockAndSubBusinessUnitIdAndActive(CHASSIS_ID, true, SUB_BUSINESS_UNIT_ID, true))
                .thenReturn(true);
        when(inspectionGateRepository.saveAndFlush(any(InspectionGate.class)))
                .thenReturn(InspectionGate.builder().id(INSPECTION_GATE_ID).build());
        when(messagelanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn(SUCCESS_MESSAGE);

        // Input setup
        GateOutGeneralAssignmentRegisterInput.Input input = GateOutGeneralAssignmentRegisterInput.Input.builder()
                .eirId(EIR_ID)
                .chassisId(CHASSIS_ID)
                .userRegistrationId(USER_REGISTRATION_ID)
                .containerId(NOT_APPLICA_CONTAINER_ID)
                .languageId(LANGUAGE_ID)
                .seal1(SEAL1)
                .seal2(SEAL2)
                .seal3(SEAL3)
                .seal4(SEAL4)
                .photos("[]")
                .build();

        // Act
        GateOutGeneralAssignmentRegisterOutput result = gateoutGeneralAssignmentRegisterService.execute(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getResultState());
        assertEquals(SUCCESS_MESSAGE, result.getResultMessage());
        verify(eirRepository, times(1)).save(any(Eir.class));
    }

    @Test
    void givenValidInput_whenUpdateDamageCode_thenProcessSuccessfully() {

        mockCatalogs();

        // Arrange
        final Integer USER_REGISTRATION_ID = 1;
        final Integer LANGUAGE_ID = 1;
        final Integer EIR_ID = 1001;
        final Integer CONTAINER_ID = 2001;
        final Integer EIR_GATE_IN_ID = 1002;
        final Integer CONTAINER_TYPE_ID = 8001;
        final Integer CONTAINER_SIZE_ID = 9001;
        final Integer CATALOG_EMPTY_FULL_ID = catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        final Integer CATALOG_PROCEDENCIA_ID = catalogIds.get(CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS);
        final Integer CATALOG_CONTAINER_TYPE_ID = catalogIds.get(CATALOG_CONTAINER_TYPE);
        final Integer GATE_TRANSMISSION_SETTING_ID = 10001;
        final Short IS_REEFER = 1;
        final String ANNOTATION = "[GRAL G.Out " + EIR_ID + "] ";

        this.mockCatalogs();

        // Constants
        final String CONTAINER_NUMBER = "TSTU4000001";
        final Integer CONTAINER_TARE = 1000;
        final Integer MAXIMUM_PAYLOAD = 2000;
        final Integer CAT_CONTAINER_SIZE_ID = 20017;
        final Integer CAT_CONTAINER_TYPE_ID = catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS);
        final Integer CAT_EQUIP_MEASURE_TARE_ID = catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS);
        final Integer CAT_EQUIP_MEASURE_PAYLOAD_ID = catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS);
        final Integer ISO_CODE_ID = 5001;
        final Integer CAT_GRADE_ID = catalogIds.get(GRADE_NO_CLASS);
        final Integer BOOKING_ID = 3001;
        final String BOOKING_REMARK_RULE = "";
        final Integer SUB_BUSINESS_UNIT_ID = 4001;
        final Integer CAT_ORIGIN_ID = 20018;
        final Integer BOOKING_DETAIL_ID = 5001;
        final Integer BOOKING_DETAIL_RESERVATION_QUANTITY = 10;
        final Integer SHIPPING_LINE_ID = 6001;
        final Integer VESSEL_PROGRAMMING_DETAIL_ID = 7001;
        final Integer PREASSIGNMENT_ID = 8001;
        final Integer IMO_ID = 11001;
        final String SUB_BUSINESS_UNIT_ALIAS = "BUSINESS_UNIT_ALIAS";
        final Integer SYSTEM_RULE_ID = 14001;
        final String SYSTEM_RULES_ALIAS = "system_rule_id";
        final Integer SUB_BUSINESS_UNIT_LOCAL_ID = 15001;
        JSONArray jsonArray = new JSONArray();
        jsonArray.put(new JSONObject()
                .put("sub_line_id", SHIPPING_LINE_ID)
                .put("sub_line_name", "SLNAME")
                .put("line_main_id", SHIPPING_LINE_ID)
                .put("line_main_name", "SLNAME")
        );
        final String SYSTEM_RULE = jsonArray.toString();
        final String PHOTOS = "[{\"nombre\":\"foto1.jpg\",\"peso\":\"2\",\"formato\":\"jpg\",\"ubicacion\":\"/imagenes/foto1.jpg\",\"url\":\"https://example.com/foto1.jpg\",\"id\":\"12345\",\"tipoAdjunto\":1}," +
                "{\"nombre\":\"foto2.png\",\"peso\":\"1\",\"formato\":\"png\",\"ubicacion\":\"/imagenes/foto2.png\",\"url\":\"https://example.com/foto2.png\",\"id\":\"67890\",\"tipoAdjunto\":2}]";

        //Mocking find service response
        SdggateoutGeneralAssignmentContainerFindOutput containerFindOutput = new SdggateoutGeneralAssignmentContainerFindOutput();
        SdggateoutGeneralAssignmentContainerFindOutputResult containerFindResponse = new SdggateoutGeneralAssignmentContainerFindOutputResult();
        containerFindResponse.setResultState(1);
        containerFindOutput.setResult(List.of(containerFindResponse));

        // Mocking entities
        Container container = Container.builder()
                .id(CONTAINER_ID)
                .containerNumber(CONTAINER_NUMBER)
                .catSize(Catalog.builder().id(CAT_CONTAINER_SIZE_ID).build())
                .catContainerType(Catalog.builder().id(CAT_CONTAINER_TYPE_ID).build())
                .containerTare(CONTAINER_TARE)
                .catEquipMeasureTare(Catalog.builder().id(CAT_EQUIP_MEASURE_TARE_ID).build())
                .maximunPayload(MAXIMUM_PAYLOAD)
                .catEquipMeasurePayload(Catalog.builder().id(CAT_EQUIP_MEASURE_PAYLOAD_ID).build())
                .isoCode(IsoCode.builder().id(ISO_CODE_ID).build())
                .catGrade(Catalog.builder().id(CAT_GRADE_ID).build())
                .catReeferType(null)
                .catEngineBrand(null)
                .manufactureDate(LocalDateTime.now())
                .build();

        Booking booking = Booking.builder().id(BOOKING_ID)
                .remarkRulesName(BOOKING_REMARK_RULE)
                .shippingLine(ShippingLine.builder().id(SHIPPING_LINE_ID).build())
                .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                        .id(VESSEL_PROGRAMMING_DETAIL_ID)
                        .build())
                .build();

        Eir eir = Eir.builder()
                .id(EIR_ID)
                .bookingGout(booking)
                .subBusinessUnit(BusinessUnit.builder()
                        .id(SUB_BUSINESS_UNIT_ID)
                        .businesUnitAlias(SUB_BUSINESS_UNIT_ALIAS)
                        .build())
                .catOrigin(Catalog.builder().id(CATALOG_PROCEDENCIA_ID).build())
                .catEmptyFull(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).build())
                .container(Container.builder().id(CONTAINER_ID).containerNumber(CONTAINER_NUMBER).build())
                .build();

        ContainerPreassignment preassignment = ContainerPreassignment.builder()
                .id(PREASSIGNMENT_ID)
                .bookingDetail(BookingDetail.builder()
                        .id(BOOKING_DETAIL_ID)
                        .booking(Booking.builder()
                                .id(BOOKING_ID)
                                .shippingLine(null)
                                .vesselProgrammingDetail(VesselProgrammingDetail.builder().id(VESSEL_PROGRAMMING_DETAIL_ID).build())
                                .imo(Imo.builder().id(IMO_ID).build())
                                .build())
                        .build())
                .build();

        BookingDetail bookingDetail = BookingDetail.builder()
                .id(BOOKING_DETAIL_ID)
                .reservationQuantity(BOOKING_DETAIL_RESERVATION_QUANTITY)
                .build();

        // Mocking repositories and services methods calls
        when(catalogService.findIdsByAliases(any()))
                .thenReturn(catalogIds);

        when(containerRepository.findById(anyInt())).thenReturn(Optional.of(container));

        when(eirRepository.findById(anyInt()))
                .thenReturn(Optional.of(eir));

        when(stockEmptyRepository.findByContainerAndBusinessUnitAndInStockAndActive(anyInt(), anyInt(), eq(true), eq(true)))
                .thenReturn(Optional.of(StockEmpty.builder()
                                .gateInEir(Eir.builder().id(EIR_GATE_IN_ID).build())
                        .build()));

        when(containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionOne(any(), anyInt(), anyInt(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());
        when(containerPreassignmentRepository.findFirstActivePreassignmentByBookingAndContainerOptionTwo(any(), anyInt(), anyInt(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());

        when(bookingDetailRepository.findAllByBookingIdAndContainerSizeIdAndContainerTypeIdListWithNoRemarkRule(BOOKING_ID, CAT_CONTAINER_SIZE_ID, List.of(CAT_CONTAINER_TYPE_ID)))
                .thenReturn(List.of(
                        bookingDetail
                ));

        when(containerPreassignmentRepository.findAllByBookingDetailIdAndActive(List.of(BOOKING_DETAIL_ID), true))
                .thenReturn(List.of(ContainerPreassignment.builder()
                        .id(PREASSIGNMENT_ID)
                        .bookingDetail(bookingDetail)
                        .build()));

        when(systemRuleRepository.findRuleByIdAndActiveTrue("sds_subshippingline_equivalence"))
                .thenReturn(SYSTEM_RULE);

        when(gateTransmissionLocalSettingRepository.findByShippingLineIdAndBusinessUnitsAndCatStatusActivityFormatAndActive(
                SHIPPING_LINE_ID, SUB_BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_LOCAL_ID, true, catalogIds.get(IS_TELEX_FORMAT), true))
                .thenReturn(Optional.of(GateTransmissionLocalSetting.builder()
                        .id(GATE_TRANSMISSION_SETTING_ID)
                                .gateTransmissionSetting(GateTransmissionSetting.builder()
                                        .id(GATE_TRANSMISSION_SETTING_ID)
                                        .build())
                        .build()));

        // Input setup
        GateOutGeneralAssignmentRegisterInput.Input input = GateOutGeneralAssignmentRegisterInput.Input.builder()
                .eirId(EIR_ID)
                .containerId(CONTAINER_ID)
                .userRegistrationId(USER_REGISTRATION_ID)
                .languageId(LANGUAGE_ID)
                .photos(PHOTOS)
                .systemRuleId(SYSTEM_RULES_ALIAS)
                .subBusinessUnitLocalId(SUB_BUSINESS_UNIT_LOCAL_ID)
                .build();

        // Act
        gateoutGeneralAssignmentRegisterService.updateDamageCode(input);

        // Assert
        verify(gateTransmissionRepository, times(1)).telexUpdateDamageManual(
                eq(EIR_GATE_IN_ID),
                eq(GATE_TRANSMISSION_SETTING_ID),
                eq("N"),
                eq(USER_REGISTRATION_ID),
                eq(Short.valueOf("0")),
                eq(Short.valueOf("0")),
                eq(ANNOTATION)
        );
    }
}
