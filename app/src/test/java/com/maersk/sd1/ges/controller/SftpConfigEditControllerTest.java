package com.maersk.sd1.ges.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.SftpConfigEditInput;
import com.maersk.sd1.ges.dto.SftpConfigEditOutput;
import com.maersk.sd1.ges.service.SftpConfigEditService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class SftpConfigEditControllerTest {

    @Mock
    private SftpConfigEditService sftpConfigEditService;

    @InjectMocks
    private SftpConfigEditController sftpConfigEditController;

    private SftpConfigEditInput.Root request;
    private SftpConfigEditInput.Input input;
    private SftpConfigEditOutput output;

    @BeforeEach
    void setup() {
        input = new SftpConfigEditInput.Input();
        input.setSftpConfigId(1);
        input.setAlias("alias");
        input.setSftpHost("host");
        input.setSftpName("name");
        input.setSftpPass("pass");
        input.setSftpPort("22");
        input.setSftpPath("/path");
        input.setEventAfterUpload("event");
        input.setIsFtp(true);
        input.setRequiresPrivateKey(true);
        input.setAdjuntosPublicKey("publicKey");
        input.setAdjuntosPrivateKey("privateKey");
        input.setRequiresPassphrase(true);
        input.setPassphrase("passphrase");
        input.setStatus(true);
        input.setUsuarioModificacionId(1);

        request = new SftpConfigEditInput.Root();
        SftpConfigEditInput.Prefix prefix = new SftpConfigEditInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        output = new SftpConfigEditOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Record updated successfully.");
    }

    @Test
    void testSdgSftpConfigEditSuccess() {
        when(sftpConfigEditService.editSftpConfig(input)).thenReturn(output);

        ResponseEntity<ResponseController<SftpConfigEditOutput>> response = sftpConfigEditController.sdgSftpConfigEdit(request);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Record updated successfully.", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testSdgSftpConfigEditBadRequest() {
        ResponseEntity<ResponseController<SftpConfigEditOutput>> response = sftpConfigEditController.sdgSftpConfigEdit(null);

        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Invalid request", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testSdgSftpConfigEditException() {
        when(sftpConfigEditService.editSftpConfig(input)).thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<SftpConfigEditOutput>> response = sftpConfigEditController.sdgSftpConfigEdit(request);

        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Database error", response.getBody().getResult().getRespMensaje());
    }
}