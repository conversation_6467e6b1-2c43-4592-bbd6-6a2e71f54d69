package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.SftpConfig;
import com.maersk.sd1.common.repository.SftpConfigRepository;
import com.maersk.sd1.common.repository.AttachmentRepository;
import com.maersk.sd1.ges.controller.dto.SftpConfigRemoveAttachmentOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SftpConfigRemoveAttachmentServiceTest {

    @Mock
    private SftpConfigRepository sftpConfigRepository;

    @Mock
    private AttachmentRepository attachmentRepository;

    @InjectMocks
    private SftpConfigRemoveAttachmentService sftpConfigRemoveAttachmentService;

    private SftpConfig sftpConfig;

    @BeforeEach
    public void setUp() {
        sftpConfig = new SftpConfig();
        sftpConfig.setId(1);
        sftpConfig.setPublicKeyAttachedId(100);
        sftpConfig.setPrivateKeyAttachedId(200);
    }

    @Test
    @Transactional
    void givenSftpConfigExists_WhenPublicKeyAttachmentIsRemoved_ThenAttachmentIsDeleted() {
        when(sftpConfigRepository.findById(anyInt())).thenReturn(Optional.of(sftpConfig));

        SftpConfigRemoveAttachmentOutput response = sftpConfigRemoveAttachmentService.removeAttachment(1, "1", 1);

        assertEquals(1, response.getRespStatus());
        assertEquals("Adjunto eliminado correctamente", response.getRespMessage());
        verify(sftpConfigRepository, times(1)).save(sftpConfig);
        verify(attachmentRepository, times(1)).deleteById(100);
    }

    @Test
    @Transactional
    void givenSftpConfigExists_WhenPrivateKeyAttachmentIsRemoved_ThenAttachmentIsDeleted() {
        when(sftpConfigRepository.findById(anyInt())).thenReturn(Optional.of(sftpConfig));

        SftpConfigRemoveAttachmentOutput response = sftpConfigRemoveAttachmentService.removeAttachment(1, "2", 1);

        assertEquals(1, response.getRespStatus());
        assertEquals("Adjunto eliminado correctamente", response.getRespMessage());
        verify(sftpConfigRepository, times(1)).save(sftpConfig);
        verify(attachmentRepository, times(1)).deleteById(200);
    }

    @Test
    @Transactional
    void givenSftpConfigDoesNotExist_WhenRemovingAttachment_ThenSftpConfigNotFoundIsReturned() {
        when(sftpConfigRepository.findById(anyInt())).thenReturn(Optional.empty());

        SftpConfigRemoveAttachmentOutput response = sftpConfigRemoveAttachmentService.removeAttachment(1, "1", 1);

        assertEquals(0, response.getRespStatus());
        assertEquals("No SftpConfig found for id: 1", response.getRespMessage());
        verify(sftpConfigRepository, never()).save(any(SftpConfig.class));
        verify(attachmentRepository, never()).deleteById(anyInt());
    }

    @Test
    @Transactional
    void givenSftpConfigExists_WhenInvalidKeyTypeIsUsed_ThenNoAttachmentIsDeleted() {
        when(sftpConfigRepository.findById(anyInt())).thenReturn(Optional.of(sftpConfig));

        SftpConfigRemoveAttachmentOutput response = sftpConfigRemoveAttachmentService.removeAttachment(1, "3", 1);

        assertEquals(1, response.getRespStatus());
        assertEquals("Adjunto eliminado correctamente", response.getRespMessage());
        verify(sftpConfigRepository, never()).save(any(SftpConfig.class));
        verify(attachmentRepository, never()).deleteById(anyInt());
    }

    @Test
    @Transactional
    void givenDatabaseError_WhenRemovingAttachment_ThenExceptionIsHandled() {
        when(sftpConfigRepository.findById(anyInt())).thenThrow(new RuntimeException("Database error"));

        SftpConfigRemoveAttachmentOutput response = sftpConfigRemoveAttachmentService.removeAttachment(1, "1", 1);

        assertEquals(0, response.getRespStatus());
        assertEquals("Database error", response.getRespMessage());
        verify(sftpConfigRepository, never()).save(any(SftpConfig.class));
        verify(attachmentRepository, never()).deleteById(anyInt());
    }
}