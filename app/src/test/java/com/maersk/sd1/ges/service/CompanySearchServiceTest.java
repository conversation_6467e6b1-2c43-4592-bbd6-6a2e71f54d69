package com.maersk.sd1.ges.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maersk.sd1.common.repository.CompanyRoleRepository;
import com.maersk.sd1.ges.dto.CompanySearchInput;
import com.maersk.sd1.ges.dto.CompanySearchOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class CompanySearchServiceTest {

    @Mock
    private CompanyRoleRepository companyRoleRepository;

    @InjectMocks
    private CompanySearchService companySearchService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSearchCompanies() throws JsonProcessingException {
        // Arrange
        CompanySearchInput.Input input = new CompanySearchInput.Input();
        input.setBusinessUnitId(1);
        input.setCompanyRoles("[\"ROLE\"]");
        input.setFlagNull("1");
        input.setCompanyName("Test Company");

        CompanySearchOutput companySearchData = new CompanySearchOutput(1, "12345", "12345 - Test Company");
        List<CompanySearchOutput> companySearchDataList = Collections.singletonList(companySearchData);
        Page<CompanySearchOutput> companySearchDataPage = new PageImpl<>(companySearchDataList);

        when(companyRoleRepository.findCompanies(any(Integer.class), any(Boolean.class), anyList(), anyString(), any(Pageable.class)))
                .thenReturn(companySearchDataPage);

        // Act
        List<CompanySearchOutput> result = companySearchService.searchCompanies(input);

        // Assert
        assertEquals(1, result.size());
        assertEquals("12345 - Test Company", result.getFirst().getCompanyInfo());
    }

    @Test
    void testSearchCompanies_NoCompaniesFound() throws JsonProcessingException {
        // Arrange
        CompanySearchInput.Input input = new CompanySearchInput.Input();
        input.setBusinessUnitId(1);
        input.setCompanyRoles("[\"ROLE\"]");
        input.setFlagNull("1");
        input.setCompanyName("Nonexistent Company");

        Page<CompanySearchOutput> companySearchDataPage = new PageImpl<>(Collections.emptyList());

        when(companyRoleRepository.findCompanies(any(Integer.class), any(Boolean.class), anyList(), anyString(), any(Pageable.class)))
                .thenReturn(companySearchDataPage);

        // Act
        List<CompanySearchOutput> result = companySearchService.searchCompanies(input);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testSearchCompanies_ExceptionThrown() throws JsonProcessingException {
        // Arrange
        CompanySearchInput.Input input = new CompanySearchInput.Input();
        input.setBusinessUnitId(1);
        input.setCompanyRoles("[\"ROLE\"]");
        input.setFlagNull("1");
        input.setCompanyName("Test Company");

        when(companyRoleRepository.findCompanies(any(Integer.class), any(Boolean.class), anyList(), anyString(), any(Pageable.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act
        List<CompanySearchOutput> result = companySearchService.searchCompanies(input);

        // Assert
        assertEquals(0, result.size());
    }
    @Test
    void testSearchCompanies_InputValidationFails() {
        // Arrange
        CompanySearchInput.Input input = new CompanySearchInput.Input();
        // Intentionally not setting required fields to trigger validation failure

        // Act
        List<CompanySearchOutput> result = companySearchService.searchCompanies(input);

        // Assert
        assertEquals(0, result.size());
    }
}