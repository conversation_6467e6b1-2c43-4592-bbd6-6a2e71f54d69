package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.ConsultReportInput;
import com.maersk.sd1.ges.dto.ConsultReportOutput;
import com.maersk.sd1.ges.dto.ConsultReportInput;
import com.maersk.sd1.ges.dto.ConsultorReportOutputDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.CallableStatementCreator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlParameter;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Types;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConsultReportServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private ConsultReportService consultReportService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidInput_When_ExecuteReport_Then_ReturnsExpectedOutput() throws Exception {
        // Arrange
        ConsultReportInput.Parameter parametro = new ConsultReportInput.Parameter();
        parametro.setParameter("param1");
        parametro.setType("STRING");

        ConsultReportInput.DataConfig input = new ConsultReportInput.DataConfig();
        input.setStoreName("testProcedure");
        input.setParameter("[]");

        ConsultReportInput.PrefixDat prefixDat = new ConsultReportInput.PrefixDat();
        prefixDat.setDataConfio(input);

        ConsultReportInput.PrefixRep prefixRep = new ConsultReportInput.PrefixRep();

        Map<String, Object> data = new HashMap<String, Object>();

        prefixRep.setInput(data);

        ConsultReportInput.Root request = new ConsultReportInput.Root();
        request.setPrefixDat(prefixDat);
        request.setPrefixRep(prefixRep);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("#result-set-1", new ArrayList<Map<String, Object>>());
        resultMap.put("#result-set-2", new ArrayList<Map<String, Object>>());

        ConsultReportOutput result = consultReportService.executeReport(request);

        assertNotNull(result);
    }

    @Test
    void Given_InvalidInput_When_ExecuteReport_Then_ThrowsException() {

        ConsultReportInput.DataConfig input = new ConsultReportInput.DataConfig();
        input.setStoreName("testProcedure");
        input.setParameter("[]");

        ConsultReportInput.PrefixDat prefixDat = new ConsultReportInput.PrefixDat();
        prefixDat.setDataConfio(null);

        ConsultReportInput.Root request = new ConsultReportInput.Root();
        request.setPrefixDat(prefixDat);

        // Act & Assert
        assertThrows(RuntimeException.class, () -> consultReportService.executeReport(request));
    }

    @Test
    void Given_ValidInput_When_GetSqlType_Then_ReturnsCorrectSqlType() {
        // Act & Assert
        assertEquals(Types.VARCHAR, consultReportService.getSqlType("STRING"));
        assertEquals(Types.INTEGER, consultReportService.getSqlType("INTEGER"));
        assertEquals(Types.CHAR, consultReportService.getSqlType("CHARACTER"));
        assertEquals(Types.DATE, consultReportService.getSqlType("DATE"));
        assertEquals(Types.VARCHAR, consultReportService.getSqlType("UNKNOWN"));
    }

    @Test
    void Given_ValidInput_When_CallStoredProcedure_Then_ReturnsExpectedResult() throws Exception {
        // Arrange
        String procedureName = "testProcedure";
        List<SqlParameter> sqlParameters = Arrays.asList(
                new SqlParameter("param1", Types.VARCHAR),
                new SqlParameter("pagina", Types.INTEGER),
                new SqlParameter("cantidad", Types.INTEGER)
        );
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("param1", "value1");
        paramMap.put("pagina", 1);
        paramMap.put("cantidad", 10);

        CallableStatement mockCallableStatement = mock(CallableStatement.class);
        Connection mockConnection = mock(Connection.class);
        when(mockConnection.prepareCall(anyString())).thenReturn(mockCallableStatement);

        when(jdbcTemplate.call(any(), anyList())).thenAnswer(invocation -> {
            CallableStatementCreator csc = invocation.getArgument(0);
            csc.createCallableStatement(mockConnection);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("#result-set-1", new ArrayList<Map<String, Object>>());
            resultMap.put("#result-set-2", new HashMap<String, Object>());
            resultMap.put("total", 100);
            return resultMap;
        });

        // Act
        Map<String, Object> result = jdbcTemplate.call(con -> {
            StringBuilder callString = new StringBuilder("{call " + procedureName + "(");
            for (int i = 0; i < sqlParameters.size(); i++) {
                if (i > 0) {
                    callString.append(", ");
                }
                callString.append("?");
            }
            callString.append(")}");

            CallableStatement cs = con.prepareCall(callString.toString());
            int index = 1;
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                cs.setObject(index++, entry.getValue());
            }
            return cs;
        }, sqlParameters);

        // Assert
        assertNotNull(result);
        assertEquals(100, result.get("total"));
        assertTrue(result.containsKey("#result-set-1"));
        assertTrue(result.containsKey("#result-set-2"));
    }
}