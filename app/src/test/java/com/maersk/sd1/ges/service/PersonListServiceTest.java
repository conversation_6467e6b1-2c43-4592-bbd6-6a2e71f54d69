package com.maersk.sd1.ges.service;


import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.BusinessUnitConfigRepository;
import com.maersk.sd1.common.repository.CompanyPersonRepository;
import com.maersk.sd1.common.repository.PersonRepository;
import com.maersk.sd1.common.repository.PersonRoleRepository;
import com.maersk.sd1.ges.dto.PersonListInputDTO;
import com.maersk.sd1.ges.dto.PersonListOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PersonListServiceTest {

    @Mock
    private PersonRepository personRepository;

    @Mock
    private CompanyPersonRepository companyPersonRepository;

    @Mock
    private PersonRoleRepository personRoleRepository;

    @Mock
    private BusinessUnitConfigRepository businessUnitConfigRepository;

    @InjectMocks
    private PersonListService personListService;

    private PersonListInputDTO.Input input;
    private Person person;
    private CompanyPerson companyPerson;
    private PersonRole personRole;

    @BeforeEach
    public void setUp() {
        input = new PersonListInputDTO.Input();
        input.setUnidadNegocioId(1);
        input.setEmpresaId(1);
        input.setPersonaRolId(48083);
        input.setPerbTipoDocumentoIdentidadId(41562);
        input.setPerbDocumentoIdentidad("46188");
        input.setPerbApellidoPaterno("MARTORELL");
        input.setPerbApellidoMaterno("--");
        input.setPerbNombres("SEBASTIÁN");
        input.setPerbNombreCompleto("SEBASTIÁN MARTORELL --");
        input.setPerbCorreo(null);
        input.setPerbTelefono(null);
        input.setFechaNacimiento(LocalDate.of(2021, 2, 18));
        input.setRolPersonaId(48083);
        input.setCliente("APM TERMINALS INLAND SERVICES S.A.");
        input.setSituacionLaboral(41773);
        input.setPage(1);
        input.setSize(10);

        person = new Person();
        person.setId(1);
        person.setNames("SEBASTIÁN");
        person.setFirstLastName("MARTORELL");
        person.setSecondLastName("--");
        person.setBirthDate(LocalDateTime.of(2021, 2, 18, 0, 0));
        person.setRegistrationDate(LocalDateTime.now());
        person.setModificationDate(LocalDateTime.now());

        companyPerson = new CompanyPerson();
        companyPerson.setId(new CompanyPersonId(1, 1));
        Company company = new Company();
        company.setId(1);
        company.setLegalName("APM TERMINALS INLAND SERVICES S.A.");
        companyPerson.setCompany(company);
        companyPerson.setActive(true);

        personRole = new PersonRole();
        personRole.setCatPersonRole(new Catalog(48083));


    }

    @Test
    void Given_ValidInputs_When_personList_ThenReturnSuccessResponse() {

        Page<Person> pageResult = new PageImpl<>(List.of(person), PageRequest.of(0, 10), 1);
        // Mocking repository methods
        when(businessUnitConfigRepository.findTimeZoneOffsetValue(anyInt())).thenReturn("2");

        Mockito.when(personRepository.findPersonaList(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(Pageable.class)))
              .thenReturn(pageResult);

        when(companyPersonRepository.findByPersonId(anyInt())).thenReturn(companyPerson);
        when(personRoleRepository.findByPersonId(anyInt())).thenReturn(Arrays.asList(personRole));

        // Call the service method
        PersonListOutputDTO output = personListService.personList(input);

        // Assertions
        assertNotNull(output);
        assertEquals(List.of(List.of(Long.valueOf("1"))), output.getTotalRegistros());
        assertFalse(output.getPersonas().isEmpty());

        PersonListOutputDTO.PersonaListDTO dto = output.getPersonas().get(0);
        assertEquals("SEBASTIÁN", dto.getPerbNombres());
        assertEquals("MARTORELL", dto.getPerbApellidoPaterno());
        assertEquals("--", dto.getPerbApellidoMaterno());
        assertEquals("APM TERMINALS INLAND SERVICES S.A.", dto.getCliente());
        assertEquals("true", dto.getActivo());
    }

    @Test
    void Given_InvalidInputs_When_personList_ThenReturnFailureResponse() {
        Page<Person> pageResult = new PageImpl<>(List.of(person), PageRequest.of(0, 10), 1);
        // Mocking repository methods
        when(businessUnitConfigRepository.findTimeZoneOffsetValue(anyInt())).thenReturn("2");
        Mockito.when(personRepository.findPersonaList(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(Pageable.class)))
                .thenReturn(pageResult);
        when(companyPersonRepository.findByPersonId(anyInt())).thenReturn(null);
        when(personRoleRepository.findByPersonId(anyInt())).thenReturn(Arrays.asList(personRole));

        // Call the service method
        PersonListOutputDTO output = personListService.personList(input);

        // Assertions
        assertNotNull(output);
        assertEquals(List.of(List.of(Long.valueOf("1"))), output.getTotalRegistros());
        assertFalse(output.getPersonas().isEmpty());

        PersonListOutputDTO.PersonaListDTO dto = output.getPersonas().get(0);
        assertNull(dto.getEmpresaId());
    }

    @Test
    void Given_InvalidInputs_When_personList_ThenReturnException() {
        // Mocking repository methods
        when(businessUnitConfigRepository.findTimeZoneOffsetValue(anyInt())).thenThrow(new RuntimeException("Database error"));

        // Call the service method
        PersonListOutputDTO output = personListService.personList(input);

        // Assertions
        assertNotNull(output);
        assertEquals(List.of(List.of(Long.valueOf("0"))), output.getTotalRegistros());
        assertTrue(output.getPersonas().isEmpty());
    }
}
