package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.ListBusinessLineInput;
import com.maersk.sd1.ges.dto.ListBusinessLineOutput;
import com.maersk.sd1.ges.service.ListBusinessLineService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ListBusinessLineControllerTest {

    @Mock
    private ListBusinessLineService listBusinessLineService;

    @InjectMocks
    private ListBusinessLineController listBusinessLineController;

    private ListBusinessLineInput.Root input;
    private ListBusinessLineOutput output;

    @BeforeEach
    void setUp() {
        input = new ListBusinessLineInput.Root();
        ListBusinessLineInput.Prefix prefix = new ListBusinessLineInput.Prefix();
        ListBusinessLineInput.Input inputData = new ListBusinessLineInput.Input();
        inputData.setUnidadNegocioId(1);
        prefix.setInput(inputData);
        input.setPrefix(prefix);

        output = new ListBusinessLineOutput();
    }

    @Test
    void testListBusinessLines() {
        when(listBusinessLineService.listBusinessLines(any(Integer.class))).thenReturn(output);

        ResponseEntity<ResponseController<ListBusinessLineOutput>> response = listBusinessLineController.listBusinessLines(input);

        assertEquals(200, response.getStatusCode().value());
    }

    @Test
    void testListBusinessLinesWithNullUnidadNegocioId() {
        input.getPrefix().getInput().setUnidadNegocioId(null);

        when(listBusinessLineService.listBusinessLines(null)).thenReturn(output);

        ResponseEntity<ResponseController<ListBusinessLineOutput>> response = listBusinessLineController.listBusinessLines(input);

        assertEquals(200, response.getStatusCode().value());
        assertNull(response.getBody().getResult().getBusinessLines());
    }
}