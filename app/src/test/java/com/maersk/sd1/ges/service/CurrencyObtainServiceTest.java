package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.CurrencyObtainInput;
import com.maersk.sd1.ges.dto.CurrencyObtainOutput;
import com.maersk.sd1.ges.repository.CurrencyObtainReposiory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CurrencyObtainServiceTest {

    @Mock
    private CurrencyObtainReposiory monedaObtenerRepository;

    @InjectMocks
    private CurrencyObtainService monedaObtenerService;

    private CurrencyObtainInput.Root validRequest;

    @BeforeEach
    void setUp() {
        CurrencyObtainInput.Input validInput = new CurrencyObtainInput.Input();
        validInput.setMonedaId(1);

        CurrencyObtainInput.Prefix prefix = new CurrencyObtainInput.Prefix();
        prefix.setInput(validInput);

        validRequest = new CurrencyObtainInput.Root();
        validRequest.setPrefix(prefix);
    }

    @Test
    void Given_ValidMonedaId_When_ObtenerMoneda_Then_ReturnSuccessResponse() {
        CurrencyObtainReposiory.MonedaObtenerProjection projection = mock(CurrencyObtainReposiory.MonedaObtenerProjection.class);
        when(projection.getId()).thenReturn(1);
        when(projection.getName()).thenReturn("Euro");
        when(projection.getAbbreviation()).thenReturn("EUR");
        when(projection.getSymbol()).thenReturn("€");
        when(projection.getStatus()).thenReturn(1);

        when(monedaObtenerRepository.findMonedaByMonedaId(1)).thenReturn(Optional.of(projection));

        CurrencyObtainOutput output = monedaObtenerService.obtenerMoneda(validRequest);

        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertEquals("Success", output.getRespMensaje());
        assertEquals(1, output.getMonedaId());
        assertEquals("Euro", output.getNombre());
        assertEquals("EUR", output.getAbreviatura());
        assertEquals("€", output.getSimbolo());
    }

    @Test
    void Given_InvalidMonedaId_When_ObtenerMoneda_Then_ReturnNotFoundResponse() {
        when(monedaObtenerRepository.findMonedaByMonedaId(1)).thenReturn(Optional.empty());

        CurrencyObtainOutput output = monedaObtenerService.obtenerMoneda(validRequest);

        assertNotNull(output);
        assertEquals(0, output.getRespEstado());
        assertEquals("No currency record found", output.getRespMensaje());
    }

    @Test
    void Given_NullMonedaId_When_ObtenerMoneda_Then_ReturnErrorResponse() {
        validRequest.getPrefix().getInput().setMonedaId(null);

        CurrencyObtainOutput output = monedaObtenerService.obtenerMoneda(validRequest);

        assertNotNull(output);
        assertEquals(0, output.getRespEstado());
        assertEquals("moneda_id cannot be null", output.getRespMensaje());
    }

    @Test
    void Given_DatabaseIssue_When_ObtenerMoneda_Then_ReturnErrorResponse() {
        when(monedaObtenerRepository.findMonedaByMonedaId(anyInt())).thenThrow(new RuntimeException("Database error"));

        CurrencyObtainOutput output = monedaObtenerService.obtenerMoneda(validRequest);

        assertNotNull(output);
        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
    }
}
