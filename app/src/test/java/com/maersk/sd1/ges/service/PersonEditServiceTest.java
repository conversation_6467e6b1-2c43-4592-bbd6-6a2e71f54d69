package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.CompanyPerson;
import com.maersk.sd1.common.model.Person;
import com.maersk.sd1.common.model.PersonRole;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.CompanyPersonRepository;
import com.maersk.sd1.common.repository.PersonRepository;
import com.maersk.sd1.common.repository.PersonRoleRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.ges.dto.PersonEditOutput;
import com.maersk.sd1.ges.exception.PersonEditException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PersonEditServiceTest {

    @Mock
    private PersonRepository personRepository;

    @Mock
    private CompanyPersonRepository companyPersonRepository;

    @Mock
    private PersonRoleRepository personRoleRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private PersonEditService personEditService;

    private Person person;

    @BeforeEach
    void setUp() {
        person = new Person();
        person.setId(1001);
    }

    @Test
    void testEditPerson_Success() throws PersonEditException {
        // Arrange
        Mockito.when(personRepository.findById(1001)).thenReturn(Optional.of(person));
        // Act
        PersonEditOutput output = personEditService.editPerson(
                1001,
                1,
                "*********",
                "Perez",
                "Gomez",
                "Juan",
                LocalDateTime.now().minusYears(20),
                "<EMAIL>",
                "*********",
                2,
                null,
                true,
                10,
                "AAA",
                null,
                "BBB",
                null,
                'M',
                1,
                null,
                101
        );
        // Assert
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertEquals("Registro actualizado correctamente", output.getRespMensaje());
        Mockito.verify(personRepository, Mockito.times(1)).save(person);
    }


    @Test
    void testEditPerson_PersonNotFound() {
        // Arrange
        Mockito.when(personRepository.findById(9999)).thenReturn(Optional.empty());

        // Act & Assert
        PersonEditException exception = assertThrows(PersonEditException.class, () -> {
            personEditService.editPerson(
                    9999,
                    1,
                    "123456",
                    "Last",
                    "Second",
                    "Name",
                    LocalDateTime.now(),
                    null,
                    null,
                    2,
                    null,
                    true,
                    10,
                    null,
                    null,
                    null,
                    null,
                    'M',
                    1,
                    null,
                    101);
        });
        assertTrue(exception.getMessage().contains("Person with persona_id=9999 not found"));
    }
       
    @Test
    void testEditPerson_NewCompanyId() throws PersonEditException {
        // Arrange
        Mockito.when(personRepository.findById(1001)).thenReturn(Optional.of(person));
        Mockito.when(companyPersonRepository.findByIdPersonIdAndActiveIsTrue(1001)).thenReturn(Optional.empty());

        // Act
        PersonEditOutput output = personEditService.editPerson(
                1001,
                1,
                "*********",
                "Perez",
                "Gomez",
                "Juan",
                LocalDateTime.now().minusYears(20),
                "<EMAIL>",
                "*********",
                2,
                null,
                true,
                10,
                "AAA",
                null,
                "BBB",
                null,
                'M',
                1,
                null,
                102
        );

        // Assert
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertEquals("Registro actualizado correctamente", output.getRespMensaje());
        Mockito.verify(companyPersonRepository, Mockito.times(1)).save(Mockito.any(CompanyPerson.class));
    }

    @Test
    void testEditPerson_NullCompanyId() throws PersonEditException {
        // Arrange
        Mockito.when(personRepository.findById(1001)).thenReturn(Optional.of(person));
        CompanyPerson companyPerson = new CompanyPerson();
        companyPerson.setActive(true);
        Mockito.when(companyPersonRepository.findByIdPersonIdAndActiveIsTrue(1001)).thenReturn(Optional.of(companyPerson));

        // Act
        PersonEditOutput output = personEditService.editPerson(
                1001,
                1,
                "*********",
                "Perez",
                "Gomez",
                "Juan",
                LocalDateTime.now().minusYears(20),
                "<EMAIL>",
                "*********",
                2,
                null,
                true,
                10,
                "AAA",
                null,
                "BBB",
                null,
                'M',
                1,
                null,
                null
        );

        // Assert
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertEquals("Registro actualizado correctamente", output.getRespMensaje());
        Mockito.verify(companyPersonRepository, Mockito.times(1)).save(companyPerson);
    }

    @Test
    void testEditPerson_WithMultipleRoles() throws PersonEditException {
        // Arrange
        Mockito.when(personRepository.findById(1001)).thenReturn(Optional.of(person));
        String rolesJson = "[{\"tipo_rol_persona_id\": 1}, {\"tipo_rol_persona_id\": 2}]";

        // Act
        PersonEditOutput output = personEditService.editPerson(
                1001,
                1,
                "*********",
                "Perez",
                "Gomez",
                "Juan",
                LocalDateTime.now().minusYears(20),
                "<EMAIL>",
                "*********",
                2,
                null,
                true,
                10,
                "AAA",
                null,
                "BBB",
                null,
                'M',
                1,
                rolesJson,
                101
        );

        // Assert
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertEquals("Registro actualizado correctamente", output.getRespMensaje());
        Mockito.verify(personRoleRepository, Mockito.times(1)).deleteByPersonId(1001);
        Mockito.verify(personRoleRepository, Mockito.times(2)).save(Mockito.any(PersonRole.class));
    }

    @Test
    void testEditPerson_LinkedToUser() throws PersonEditException {
        // Arrange
        Mockito.when(personRepository.findById(1001)).thenReturn(Optional.of(person));
        User user = new User();
        user.setId(2001);
        Mockito.when(userRepository.findByPersonId(1001)).thenReturn(Optional.of(user));

        // Act
        PersonEditOutput output = personEditService.editPerson(
                1001,
                1,
                "*********",
                "Perez",
                "Gomez",
                "Juan",
                LocalDateTime.now().minusYears(20),
                "<EMAIL>",
                "*********",
                2,
                null,
                true,
                10,
                "AAA",
                null,
                "BBB",
                null,
                'M',
                1,
                null,
                101
        );

        // Assert
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertEquals("Registro actualizado correctamente", output.getRespMensaje());
        Mockito.verify(userRepository, Mockito.times(1)).save(user);
    }
}
