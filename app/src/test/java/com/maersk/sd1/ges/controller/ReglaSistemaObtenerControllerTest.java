package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.ReglaSistemaObtenerInput;
import com.maersk.sd1.ges.dto.ReglaSistemaObtenerOutput;
import com.maersk.sd1.ges.service.ReglaSistemaObtenerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReglaSistemaObtenerControllerTest {

    @Mock
    private ReglaSistemaObtenerService reglaSistemaObtenerService;

    @InjectMocks
    private ReglaSistemaObtenerController reglaSistemaObtenerController;

    private ReglaSistemaObtenerInput.Root validRequest;
    private ReglaSistemaObtenerOutput expectedOutput;

    @BeforeEach
    void setUp() {
        validRequest = new ReglaSistemaObtenerInput.Root();
        ReglaSistemaObtenerInput.Prefix prefix = new ReglaSistemaObtenerInput.Prefix();
        ReglaSistemaObtenerInput.Input input = new ReglaSistemaObtenerInput.Input();
        input.setReglaSistemaId(1);
        prefix.setInput(input);
        validRequest.setPrefix(prefix);

        expectedOutput = new ReglaSistemaObtenerOutput();
    }

    @Test
    void Given_ValidRequest_When_ObtenerReglaSistema_Then_ReturnsSuccessfulResponse() {
        when(reglaSistemaObtenerService.obtenerReglaSistema(1)).thenReturn(expectedOutput);

        ResponseEntity<ResponseController<ReglaSistemaObtenerOutput>> response =
                reglaSistemaObtenerController.obtenerReglaSistema(validRequest);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
    }

    @Test
    void Given_InvalidRequest_When_ObtenerReglaSistema_Then_ReturnsErrorResponse() {
        when(reglaSistemaObtenerService.obtenerReglaSistema(anyInt()))
                .thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<ReglaSistemaObtenerOutput>> response =
                reglaSistemaObtenerController.obtenerReglaSistema(validRequest);

        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
    }
}
