package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.BookingEdiFileRepository;
import com.maersk.sd1.common.repository.BookingEdiFileRepository1;
import com.maersk.sd1.common.repository.BookingEdiRepository;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sds.dto.RegisterCoparnFileResult;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.maersk.sd1.common.Parameter.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class RegisterCoparnFileServiceTest {

    @Mock
    private GESCatalogService catalogService;

    @Mock
    private MessageLanguageService messageLanguageService;

    @Mock
    private BookingEdiSettingRepository bookingEdiSettingRepository;

    @Mock
    private BookingEdiRepository bookingEdiRepository;

    @Mock
    private BookingEdiFileRepository bookingEdiFileRepository;

    @Mock
    private BookingEdiFileRepository1 bookingEdiFileRepository1;

    @InjectMocks
    private RegisterCoparnFileService registerCoparnFileService;

    private static void mockCatalogs() {
        List<String> catalogAlias = catalogAliases;
        Integer pvt = 0;
        catalogIds = new HashMap<>();
        for (String ca : catalogAlias) {
            catalogIds.put(ca, ++pvt);
        }
    }

    private static List<String> catalogAliases = Arrays.asList(
            IS_BKEDI_PENDING,
            IS_STATE_TO_PROCESS,
            IS_BKEDI_DONE,
            IS_BKEDI_REJECTED,
            IS_BKEDI_NOT_VALID,
            IS_BKEDI_REJECTED_DUPLICATE,
            IS_BOOKING_TYPE_UPDATE,
            IS_BOOKING_TYPE_UPDATE,
            CATALOG_IS_EDI_MESSAGE_TYPE_COPARN,
            CATALOG_IS_EDI_MESSAGE_TYPE_301
    );

    private static HashMap<String, Integer> catalogIds = new HashMap<>();

    @BeforeAll
    public static void setAll() {
        mockCatalogs();
    }

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenEdiInput_whenRegisterEdiFile_thenReturnResult() {
        //GIVEN
        Integer ediCoparnSettingId = 1, shippingLineId = 1, catBkEdiMessageTypeId = catalogIds.get(CATALOG_IS_EDI_MESSAGE_TYPE_COPARN);
        String coparnFileName = "DUMMY", coparnFileContent = """
                UNB+UNOA:1+CUSTOMER:ZZZ+MAEU:ZZZ+150227:0246+111100190'
                                               UNH+001+IFTMBF:D:99B:UN:2.0'
                                               BGM+335+45956716+9'
                                               CTA+IC+:Harlen Wang'
                                               COM+0411-88870089:TE'
                                               <EMAIL>:EM'
                                               DTM+137:201502270246:203'
                                               TSR+27'
                                               RFF+BN:NGE009123'
                                               RFF+CT:753876'
                                               RFF+FF:FORWARDERREF1'
                                               RFF+SI:SHIPPER REF'
                                               RFF+IV:INVOICE REF'
                                               RFF+ON:PURCHASE ORDER REF'
                                               RFF+AGB:CONTRACTUAL REF'
                                               RFF+ANT:CONSIGNEE REF'
                                               RFF+UCN:UCRREF'
                                               TDT+20+1507+1++MAERSK LINE+++:::MAERSK KAMPALA'
                                               LOC+197+BEANR:181:6:ANTWERPEN+:162:5:BELGIUM+:::ANTWERPEN'
                                               LOC+88+DESKO:181:6:SCHKOPAU+:162:5:GERMANY+:::SACHSEN-ANHALT'
                                               DTM+133:20150528:102'
                                               LOC+9+DEBRV:139:6:BREMERHAVEN+:162:5:GERMANY+:::BREMEN'
                                               LOC+11+INNSA:139:6:NHAVA SHEVA (JAWAHARLAL NEHRU)+:162:5:INDIA'
                                               LOC+7+INNAG:181:6:NAGPUR+:162:5:INDIA'
                                               DTM+132:20150626:102'
                                               NAD+CN+++CONSIGNEE NAME+CONSIGNEE ADDRESS 1:CONSIGNEE ADDRESS 2:CONSIGNEE ADDRESS 3'
                                               NAD+BA+++BOOKING AGENT NAME+BOOKING AGENT ADDRESS LINE 1:BOOKING AGENT ADDRESS LINE 2:BOOKING AGENT ADDRESS LINE 3'
                                               NAD+CZ+++SHIPPER NAME+SHIPPER ADDRESS LINE 1:SHIPPER ADDRESS LINE 2:SHIPPER ADDRESS LINE 3'
                                               NAD+FC+++PRICE OWNER NAME+PRICE OWNER ADDRESS LINE 1:PRICE OWNER ADDRESS LINE 2:PRICE OWNER ADDRESS LINE 3'
                                               CTA+IC+:MARTINE'
                                               <EMAIL>:EM'
                                               COM+0113 382210:TE'
                                               COM+0113 381223:FX'
                                               GID+1+5:DR:67:6:DRUMS'
                                               PIA+5+012345:HS'
                                               FTX+AAA+++HiTEC 8788B EuroDrum'
                                               MEA+AAE+G+KGM:20030'
                                               MEA+AAE+AAW+MTQ:45'
                                               EQD+CN++22G0:102:5+2'
                                               EQN+1'
                                               MEA+AAE+G+KGM:10030'
                                               MEA+AAE+AAW+MTQ:20'
                                               RFF+AEF:LOADREF1'
                                               NAD+SF+++SHIP FROM1A+ADDRESS LINE 1:ADDRESS LINE 2:ADDRESS LINE 3:ADDRESS LINE 4'
                                               DTM+181:201505261100:203'
                                               EQD+CN++42G0:102:5+2'
                                               EQN+1'
                                               91
                                               Classification: Public
                                               MEA+AAE+G+KGM:10030'
                                               MEA+AAE+AAW+MTQ:20'
                                               RFF+AEF:LOADREF2'
                                               NAD+SF+33368236++SHIP FROM2+ADDRESS:ADDRESS:ADDRESS:ADDRESS'
                                               DTM+181:201505261200:203'\s
                                               UNT+102+001'\s
                                               UNZ+1+111100190
                """, azureUrl = "dummyAzureUrl";
        BookingEdiSetting bookingEdiSettings = BookingEdiSetting.builder()
                .downloadFileExtension("txt")
                .shippingLine(ShippingLine.builder().id(shippingLineId).build())
                .catBkEdiMessageType(Catalog.builder().id(catBkEdiMessageTypeId).build())
                .build();
        BookingEdi bookingEdi = BookingEdi.builder()
                .id(1)
                .bookingEdiSetting(BookingEdiSetting.builder().id(ediCoparnSettingId).build())
                .shippingLine(ShippingLine.builder().id(shippingLineId).build())
                .originalBkEdiFileName("originalFileName")
                .bkEdiFileName("fileName")
                .catBkEdiStatus(Catalog.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .registrationUser(User.builder().id(1).build())
                .bkEdiCreationDate("fechaCreacionEdi")
                .bkEdiProcessedComment("ediCoparnObsProcesado")
                .active(true)
                .bookingNumber("bookingNumber")
                .bkEdiReservationSituation("ediCoparnSituacionReserva")
                .traceBkEdi2("bookingNumber")
                .dateProcessedCoparn(LocalDateTime.now())
                .ownerCorrelative(1)
                .build();

        when(catalogService.findIdsByAliases(this.catalogAliases)).thenReturn(catalogIds);
        when(bookingEdiSettingRepository.findBybookingEdiSetting(ediCoparnSettingId)).thenReturn(Optional.of(bookingEdiSettings));
        when(bookingEdiRepository.save(any())).thenReturn(bookingEdi);
        when(bookingEdiRepository.findByOriginalCoparnFileNameAndBookingEdiSettingId(any(), any(), any())).thenReturn(Arrays.asList());

        //WHEN
        RegisterCoparnFileResult result = registerCoparnFileService.registerCoparnFileTransactional(ediCoparnSettingId, coparnFileName, coparnFileContent, azureUrl);

        //THEN
        assertEquals(1, result.getRespStatus());
    }

    @Test
    void givenMaerskEdiInput_whenRegisterEdiFile_thenReturnResult() {
        //GIVEN
        Integer ediCoparnSettingId = 1, shippingLineId = 4104, catBkEdiMessageTypeId = catalogIds.get(CATALOG_IS_EDI_MESSAGE_TYPE_COPARN);
        String coparnFileName = "DUMMY", coparnFileContent = """
                UNB+UNOA:1+CUSTOMER:ZZZ+MAEU:ZZZ+150227:0246+111100190'
                                               UNH+001+IFTMBF:D:99B:UN:2.0'
                                               BGM+335+45956716+9'
                                               CTA+IC+:Harlen Wang'
                                               COM+0411-88870089:TE'
                                               <EMAIL>:EM'
                                               DTM+137:201502270246:203'
                                               TSR+27'
                                               RFF+BN:NGE009123'
                                               RFF+CT:753876'
                                               RFF+FF:FORWARDERREF1'
                                               RFF+SI:SHIPPER REF'
                                               RFF+IV:INVOICE REF'
                                               RFF+ON:PURCHASE ORDER REF'
                                               RFF+AGB:CONTRACTUAL REF'
                                               RFF+ANT:CONSIGNEE REF'
                                               RFF+UCN:UCRREF'
                                               TDT+20+1507+1++MAERSK LINE+++:::MAERSK KAMPALA'
                                               LOC+197+BEANR:181:6:ANTWERPEN+:162:5:BELGIUM+:::ANTWERPEN'
                                               LOC+88+DESKO:181:6:SCHKOPAU+:162:5:GERMANY+:::SACHSEN-ANHALT'
                                               DTM+133:20150528:102'
                                               LOC+9+DEBRV:139:6:BREMERHAVEN+:162:5:GERMANY+:::BREMEN'
                                               LOC+11+INNSA:139:6:NHAVA SHEVA (JAWAHARLAL NEHRU)+:162:5:INDIA'
                                               LOC+7+INNAG:181:6:NAGPUR+:162:5:INDIA'
                                               DTM+132:20150626:102'
                                               NAD+CN+++CONSIGNEE NAME+CONSIGNEE ADDRESS 1:CONSIGNEE ADDRESS 2:CONSIGNEE ADDRESS 3'
                                               NAD+BA+++BOOKING AGENT NAME+BOOKING AGENT ADDRESS LINE 1:BOOKING AGENT ADDRESS LINE 2:BOOKING AGENT ADDRESS LINE 3'
                                               NAD+CZ+++SHIPPER NAME+SHIPPER ADDRESS LINE 1:SHIPPER ADDRESS LINE 2:SHIPPER ADDRESS LINE 3'
                                               NAD+FC+++PRICE OWNER NAME+PRICE OWNER ADDRESS LINE 1:PRICE OWNER ADDRESS LINE 2:PRICE OWNER ADDRESS LINE 3'
                                               CTA+IC+:MARTINE'
                                               <EMAIL>:EM'
                                               COM+0113 382210:TE'
                                               COM+0113 381223:FX'
                                               GID+1+5:DR:67:6:DRUMS'
                                               PIA+5+012345:HS'
                                               FTX+AAA+++HiTEC 8788B EuroDrum'
                                               MEA+AAE+G+KGM:20030'
                                               MEA+AAE+AAW+MTQ:45'
                                               EQD+CN++22G0:102:5+2'
                                               EQN+1'
                                               MEA+AAE+G+KGM:10030'
                                               MEA+AAE+AAW+MTQ:20'
                                               RFF+AEF:LOADREF1'
                                               NAD+SF+++SHIP FROM1A+ADDRESS LINE 1:ADDRESS LINE 2:ADDRESS LINE 3:ADDRESS LINE 4'
                                               DTM+181:201505261100:203'
                                               EQD+CN++42G0:102:5+2'
                                               EQN+1'
                                               91
                                               Classification: Public
                                               MEA+AAE+G+KGM:10030'
                                               MEA+AAE+AAW+MTQ:20'
                                               RFF+AEF:LOADREF2'
                                               NAD+SF+33368236++SHIP FROM2+ADDRESS:ADDRESS:ADDRESS:ADDRESS'
                                               DTM+181:201505261200:203'\s
                                               UNT+102+001'\s
                                               UNZ+1+111100190
                """, azureUrl = "dummyAzureUrl";
        BookingEdiSetting bookingEdiSettings = BookingEdiSetting.builder()
                .downloadFileExtension("txt")
                .shippingLine(ShippingLine.builder().id(shippingLineId).build())
                .catBkEdiMessageType(Catalog.builder().id(catBkEdiMessageTypeId).build())
                .build();
        BookingEdi bookingEdi = BookingEdi.builder()
                .id(1)
                .bookingEdiSetting(BookingEdiSetting.builder().id(ediCoparnSettingId).build())
                .shippingLine(ShippingLine.builder().id(shippingLineId).build())
                .originalBkEdiFileName("originalFileName")
                .bkEdiFileName("fileName")
                .catBkEdiStatus(Catalog.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .registrationUser(User.builder().id(1).build())
                .bkEdiCreationDate("fechaCreacionEdi")
                .bkEdiProcessedComment("ediCoparnObsProcesado")
                .active(true)
                .bookingNumber("bookingNumber")
                .bkEdiReservationSituation("ediCoparnSituacionReserva")
                .traceBkEdi2("bookingNumber")
                .dateProcessedCoparn(LocalDateTime.now())
                .ownerCorrelative(1)
                .build();

        when(catalogService.findIdsByAliases(this.catalogAliases)).thenReturn(catalogIds);
        when(bookingEdiSettingRepository.findBybookingEdiSetting(ediCoparnSettingId)).thenReturn(Optional.of(bookingEdiSettings));
        when(bookingEdiRepository.save(any())).thenReturn(bookingEdi);
        when(bookingEdiRepository.findByOriginalCoparnFileNameAndBookingEdiSettingId(any(), any(), any())).thenReturn(Arrays.asList());

        //WHEN
        RegisterCoparnFileResult result = registerCoparnFileService.registerCoparnFileTransactional(ediCoparnSettingId, coparnFileName, coparnFileContent, azureUrl);

        //THEN
        assertEquals(1, result.getRespStatus());
    }

    @Test
    void given301EdiInput_whenRegisterEdiFile_thenReturnResult() {
        //GIVEN
        Integer ediCoparnSettingId = 1, shippingLineId = 4104, catBkEdiMessageTypeId = catalogIds.get(CATALOG_IS_EDI_MESSAGE_TYPE_301);
        String coparnFileName = "DUMMY", coparnFileContent = """
                UNB+UNOA:1+CUSTOMER:ZZZ+MAEU:ZZZ+190429:1446+0127084153779'
                                                              UNH+20190415+IFTMBF:D:99B:UN'
                                                              BGM+335+VO212202+9'
                                                              DTM+137:201904291446:203'
                                                              TSR+30+2'
                                                              RFF+CT:312468'
                                                              RFF+SI:0127084153779'
                                                              TDT+20++1+++++:::CAP SAN RAPHAEL'
                                                              LOC+88+BEANR:181:6:KAAI 869 ANTWERPEN'
                                                              DTM+133:20190509:102'
                                                              LOC+7+SLFNA:181:6:FREETOWN'
                                                              DTM+132:20190604:102'
                                                              LOC+9+BEANR:181:6:KAAI 869 ANTWERPEN'
                                                              LOC+11+SLFNA:139:6:FREETOWN'
                                                              NAD+CN+++CONSIGNEE NAME+CONSIGNEE ADDRESS 1:CONSIGNEE ADDRESS 2:CONSIGNEE ADDRESS 3'
                                                              NAD+BA+++BOOKING AGENT NAME+BOOKING AGENT ADDRESS LINE 1:BOOKING AGENT ADDRESS LINE 2:BOOKING AGENT ADDRESS LINE 3'
                                                              NAD+CZ+++SHIPPER NAME+SHIPPER ADDRESS LINE 1:SHIPPER ADDRESS LINE 2:SHIPPER ADDRESS LINE 3'
                                                              NAD+FC+++PRICE OWNER NAME+PRICE OWNER ADDRESS LINE 1:PRICE OWNER ADDRESS LINE 2:PRICE OWNER ADDRESS LINE 3'
                                                              CTA+IC+:MARTINE'
                                                              <EMAIL>:EM'
                                                              COM+0113 382210:TE'
                                                              COM+0113 381223:FX'
                                                              GID+1+3480:BG:67:6:BAG'
                                                              PIA+5+07031019:HS'
                                                              FTX+AAA+++ONIONS, NON-FROZEN, VEGETABLES'
                                                              MEA+AAE+G+KGM:85500'
                                                              EQD+CN+01+45R1:102:5:2'
                                                              EQN+3'
                                                              MEA+AAE+AAO+HMD:60'
                                                              MEA+AAE+AAS+CBM:40'
                                                              FTX+AEB++DHO:130:ZZZ+Y’
                                                              TMP+2+006:CEL'
                                                              UNT+32+1'
                                                              UNZ+1+0127084153779'
                """, azureUrl = "dummyAzureUrl";
        BookingEdiSetting bookingEdiSettings = BookingEdiSetting.builder()
                .downloadFileExtension("txt")
                .shippingLine(ShippingLine.builder().id(shippingLineId).build())
                .catBkEdiMessageType(Catalog.builder().id(catBkEdiMessageTypeId).build())
                .build();
        BookingEdi bookingEdi = BookingEdi.builder()
                .id(1)
                .bookingEdiSetting(BookingEdiSetting.builder().id(ediCoparnSettingId).build())
                .shippingLine(ShippingLine.builder().id(shippingLineId).build())
                .originalBkEdiFileName("originalFileName")
                .bkEdiFileName("fileName")
                .catBkEdiStatus(Catalog.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .registrationUser(User.builder().id(1).build())
                .bkEdiCreationDate("fechaCreacionEdi")
                .bkEdiProcessedComment("ediCoparnObsProcesado")
                .active(true)
                .bookingNumber("bookingNumber")
                .bkEdiReservationSituation("ediCoparnSituacionReserva")
                .traceBkEdi2("bookingNumber")
                .dateProcessedCoparn(LocalDateTime.now())
                .ownerCorrelative(1)
                .build();

        when(catalogService.findIdsByAliases(this.catalogAliases)).thenReturn(catalogIds);
        when(bookingEdiSettingRepository.findBybookingEdiSetting(ediCoparnSettingId)).thenReturn(Optional.of(bookingEdiSettings));
        when(bookingEdiRepository.save(any())).thenReturn(bookingEdi);
        when(bookingEdiRepository.findByOriginalCoparnFileNameAndBookingEdiSettingId(any(), any(), any())).thenReturn(Arrays.asList());

        //WHEN
        RegisterCoparnFileResult result = registerCoparnFileService.registerCoparnFileTransactional(ediCoparnSettingId, coparnFileName, coparnFileContent, azureUrl);

        //THEN
        assertEquals(1, result.getRespStatus());
    }

}