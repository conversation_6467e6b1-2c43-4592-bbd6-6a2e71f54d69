package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VehicleDeleteInput;
import com.maersk.sd1.sds.dto.VehicleDeleteOutput;
import com.maersk.sd1.sds.service.VehicleDeleteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehicleDeleteControllerTest {

    @Mock
    private VehicleDeleteService vehicleDeleteService;

    @InjectMocks
    private VehicleDeleteController vehicleDeleteController;

    private VehicleDeleteInput.Root request;
    private VehicleDeleteInput.Input input;
    private VehicleDeleteOutput output;

    @BeforeEach
    void setUp() {
        input = new VehicleDeleteInput.Input();
        input.setVehicleId(12345);
        input.setUserModificationId(67890);
        input.setLanguageId(1);

        VehicleDeleteInput.Prefix prefix = new VehicleDeleteInput.Prefix();
        prefix.setInput(input);

        request = new VehicleDeleteInput.Root();
        request.setPrefix(prefix);

        output = new VehicleDeleteOutput();
    }

    @Test
    void Given_ValidRequest_When_DeleteVehicleIsCalled_Then_ResponseIsSuccess() {
        // Arrange
        output.setResponseStatus(1);
        output.setResponseMessage("Vehicle deleted successfully");
        when(vehicleDeleteService.deleteVehicle(input.getVehicleId(), input.getUserModificationId(), input.getLanguageId())).thenReturn(output);

        // Act
        ResponseEntity<ResponseController<VehicleDeleteOutput>> response = vehicleDeleteController.sdgVehicleDelete(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getResponseStatus());
        assertEquals("Vehicle deleted successfully", response.getBody().getResult().getResponseMessage());
    }

    @Test
    void Given_ServiceThrowsException_When_DeleteVehicleIsCalled_Then_ResponseIsError() {
        // Arrange
        when(vehicleDeleteService.deleteVehicle(anyInt(), anyInt(), anyInt())).thenThrow(new RuntimeException("Internal error"));

        // Act
        ResponseEntity<ResponseController<VehicleDeleteOutput>> response = vehicleDeleteController.sdgVehicleDelete(request);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getResponseStatus());
        assertEquals("Internal error", response.getBody().getResult().getResponseMessage());
    }
}
