package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselScheduleDetailListInput;
import com.maersk.sd1.sds.dto.VesselScheduleDetailListOutput;
import com.maersk.sd1.sds.service.VesselScheduleDetailListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VesselScheduleDetailListControllerTest {

    @Mock
    private VesselScheduleDetailListService vesselScheduleDetailListService;

    @InjectMocks
    private VesselScheduleDetailListController vesselScheduleDetailListController;

    private VesselScheduleDetailListInput.Root request;

    @BeforeEach
    void setUp() {
        request = new VesselScheduleDetailListInput.Root();
        VesselScheduleDetailListInput.Prefix prefix = new VesselScheduleDetailListInput.Prefix();
        VesselScheduleDetailListInput.Input input = new VesselScheduleDetailListInput.Input();
        input.setProgramacionNaveDetalleId(1);
        input.setLanguageId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void testListarProgramacionNaveDetalleSuccess() {
        VesselScheduleDetailListOutput output = new VesselScheduleDetailListOutput();
        output.setDetailId(1);
        output.setOperation("Operation Name");
        when(vesselScheduleDetailListService.retrieveVesselProgrammingDetail(anyInt(), anyInt()))
                .thenReturn(output);

        ResponseEntity<ResponseController<VesselScheduleDetailListOutput>> response =
                vesselScheduleDetailListController.listarProgramacionNaveDetalle(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getResult().getDetailId());
        assertEquals("Operation Name", response.getBody().getResult().getOperation());
    }

    @Test
    void testListarProgramacionNaveDetalleBadRequest() {
        ResponseEntity<ResponseController<VesselScheduleDetailListOutput>> response =
                vesselScheduleDetailListController.listarProgramacionNaveDetalle(null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void testListarProgramacionNaveDetalleInternalServerError() {
        when(vesselScheduleDetailListService.retrieveVesselProgrammingDetail(anyInt(), anyInt()))
                .thenThrow(new RuntimeException("Internal Server Error"));

        ResponseEntity<ResponseController<VesselScheduleDetailListOutput>> response =
                vesselScheduleDetailListController.listarProgramacionNaveDetalle(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }
}
