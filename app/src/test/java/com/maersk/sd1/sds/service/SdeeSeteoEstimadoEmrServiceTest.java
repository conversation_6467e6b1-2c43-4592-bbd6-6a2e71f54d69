package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.EstimateEmrSettingRepository;
import com.maersk.sd1.sds.controller.dto.SdeeSeteoEstimadoEmrInput;
import com.maersk.sd1.sds.controller.dto.SdeeSeteoEstimadoEmrOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SdeeSeteoEstimadoEmrServiceTest {

    @Mock
    private EstimateEmrSettingRepository estimateEmrSettingRepository;

    @InjectMocks
    private SdeeSeteoEstimadoEmrService sdeeSeteoEstimadoEmrService;

    private SdeeSeteoEstimadoEmrInput.Input input;

    @BeforeEach
    public void setUp() {
        input = new SdeeSeteoEstimadoEmrInput.Input();
        input.setPage(1);
        input.setSize(10);
        input.setSeteoEstimadoEmrId(1);
        input.setUnidadNegocioId((float) 1L);
        input.setSubUnidadNegocioId(1F);
        input.setLineaNavieraId(1);
        input.setCatModoGenerarArchivoEstId((float) 1L);
        input.setDescripcionServicio("Test");
        input.setArchivoCorrelativo(1);
        input.setArchivoExtension("txt");
        input.setCorreoEnvio("<EMAIL>");
        input.setMinutosTranscurridos(10);
        input.setActivo(true);
        input.setFechaRegistroMin(LocalDate.now());
        input.setFechaRegistroMax(LocalDate.now());
        input.setUsuarioRegistro((float) 1L);
        input.setFechaModificacionMin(LocalDate.now());
        input.setFechaModificacionMax(LocalDate.now());
        input.setShopcodeMerc("SHOP1");
        input.setAzureId("AZURE1");
    }

    @Test
    public void testFindEstimateEmrSetting_Success() {
        Object[] record = new Object[]{1, BigDecimal.valueOf(1L), BigDecimal.valueOf(1L), 1, BigDecimal.valueOf(1L), "Test", 1, "txt", "<EMAIL>", 10, true, new Timestamp(System.currentTimeMillis()), BigDecimal.valueOf(1L), new Timestamp(System.currentTimeMillis()), "SHOP1", "AZURE1", BigDecimal.valueOf(1L), "Test", "Test"};
        Page<Object[]> page = new PageImpl<>(Collections.singletonList(record), PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "seteo_estimado_emr_id")), 1);

        when(estimateEmrSettingRepository.findEstimateEmrSettings(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(page);

        SdeeSeteoEstimadoEmrOutput output = sdeeSeteoEstimadoEmrService.findEstimateEmrSetting(input);

        assertEquals(1, output.getTotalRegistros().get(0).get(0));
        assertEquals(1, output.getDetalle().size());
        assertEquals("Test", output.getDetalle().get(0).getDescripcionServicio());
    }

    @Test
    public void testFindEstimateEmrSetting_Exception() {
        when(estimateEmrSettingRepository.findEstimateEmrSettings(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new RuntimeException("Database error"));

        SdeeSeteoEstimadoEmrOutput output = sdeeSeteoEstimadoEmrService.findEstimateEmrSetting(input);

        assertEquals(0, output.getTotalRegistros().get(0).get(0));
        assertEquals(0, output.getDetalle().size());
    }
}