package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ShipListInputDTO;
import com.maersk.sd1.sds.dto.ShipListOutputDTO;
import com.maersk.sd1.sds.service.ShipListService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ShipListControllerTest {

    private static final Logger logger = LogManager.getLogger(ShipListControllerTest.class);

    @Mock
    private ShipListService navesListService;

    @InjectMocks
    private ShipListController navesListController;

    private ShipListInputDTO.Root request;
    private ShipListInputDTO.Input input;

    @BeforeEach
    void setUp() {
        input = new ShipListInputDTO.Input();
        input.setPage(1);
        input.setSize(10);
        request = new ShipListInputDTO.Root();
        ShipListInputDTO.Prefix prefix = new ShipListInputDTO.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void Given_ValidRequest_When_GetNavesList_Then_ReturnsSuccessResponse() {
        ShipListOutputDTO output = new ShipListOutputDTO();
        when(navesListService.getNavesList(input)).thenReturn(output);

        ResponseEntity<ResponseController<ShipListOutputDTO>> response = navesListController.getNavesList(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(200, response.getStatusCode().value());
    }

    @Test
    void Given_ServiceThrowsException_When_GetNavesList_Then_ReturnsErrorResponse() {
        when(navesListService.getNavesList(input)).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<ShipListOutputDTO>> response = navesListController.getNavesList(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void Given_ValidRequest_When_GetNavesList_Then_ReturnsSuccessResponse2() {
        logger.info("Starting test: Given_ValidRequest_When_GetNavesList_Then_ReturnsSuccessResponse2");
        ShipListOutputDTO output = new ShipListOutputDTO();
        output.setData(new ArrayList<>());

        when(navesListService.getNavesList(input)).thenReturn(output);

        ResponseEntity<ResponseController<ShipListOutputDTO>> response = navesListController.getNavesList2(request);

        logger.info("Response: {}", response);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void Given_ServiceThrowsException_When_GetNavesList_Then_ReturnsErrorResponse2() {
        when(navesListService.getNavesList(input)).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<ShipListOutputDTO>> response = navesListController.getNavesList2(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
    }
}