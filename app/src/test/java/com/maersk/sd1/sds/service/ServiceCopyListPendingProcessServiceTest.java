package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.BookingEdiRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.sds.dto.PendingEdiDTO;
import com.maersk.sd1.sds.dto.ServiceCopyListPendingProcessInput;
import com.maersk.sd1.sds.dto.ServiceCopyListPendingProcessOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

class ServiceCopyListPendingProcessServiceTest {

    @InjectMocks
    private ServiceCopyListPendingProcessService service;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private BookingEdiRepository bookingEdiRepository;

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testServiceCopyListPendingProcessService() {
        ServiceCopyListPendingProcessInput.Root request = new ServiceCopyListPendingProcessInput.Root();
        ServiceCopyListPendingProcessInput.Input input = new ServiceCopyListPendingProcessInput.Input();
        input.setEdiCoparnSetupId(1);
        ServiceCopyListPendingProcessInput.Prefix prefix = new ServiceCopyListPendingProcessInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(catalogRepository.findIdByAlias(Parameter.IS_STATE_TO_READ)).thenReturn(1);
        when(catalogRepository.findIdByAlias(Parameter.IS_STATE_TO_PROCESS)).thenReturn(2);

        PendingEdiDTO pendingEdiDTO = getPendingEdiDTO();

        when(bookingEdiRepository.findPendingEdiDTOByEdiCoparnSetupId(1, 1)).thenReturn(Collections.singletonList(pendingEdiDTO));
        when(bookingEdiRepository.findPendingEdiDTOByEdiCoparnSetupIdAndIsstateProcess(1, 2)).thenReturn(Collections.emptyList());
        when(bookingEdiRepository.findPendingEdiDTOByEdiCoparnSetupIdAndIsstateProcess2(1, 2)).thenReturn(Collections.emptyList());

        when(shippingLineRepository.findShippingLineNamesByIds(Collections.singletonList(1)))
                .thenReturn(Collections.singletonList(new Object[]{"ShippingLine1", 1}));

        ResponseEntity<ResponseController<List<ServiceCopyListPendingProcessOutput>>> response = service.serviceCopyListPendingProcessService(request);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        List<ServiceCopyListPendingProcessOutput> outputList = response.getBody().getResult();
        assertEquals(1, outputList.size());
        ServiceCopyListPendingProcessOutput output = outputList.getFirst();
        assertEquals(1, output.getEdiCoparnSetupId());
        assertEquals(1, output.getShippingLineId());
        assertEquals("ShippingLine1", output.getShippingLine());
        assertEquals(1, output.getEdiCoparnId());
        assertEquals("file1", output.getEdiCoparnOriginalFileName());
        assertEquals(1, output.getOrder());
        assertEquals("BN1", output.getBookingNumber());
        assertEquals("2023-01-01", output.getEdiCoparnCreationDate());
        assertEquals("1", output.getEdiCoparnReservationStatus());
    }

    private static PendingEdiDTO getPendingEdiDTO() {
        PendingEdiDTO pendingEdiDTO = new PendingEdiDTO();
        pendingEdiDTO.setEdiCoparnSetupId(1);
        pendingEdiDTO.setShippingLineId(1);
        pendingEdiDTO.setEdiCoparnId(1);
        pendingEdiDTO.setOriginalFileName("file1");
        pendingEdiDTO.setOrder(1);
        pendingEdiDTO.setBookingNumber("BN1");
        pendingEdiDTO.setEdiCreationDate("2023-01-01");
        pendingEdiDTO.setRegistrationDate(null);
        pendingEdiDTO.setReservationStatus("1");
        pendingEdiDTO.setEdiCountByMinute(0);
        return pendingEdiDTO;
    }
}