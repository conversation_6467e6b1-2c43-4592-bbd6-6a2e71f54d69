package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.ServiceCoparnProcessFile9Input;
import com.maersk.sd1.sds.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class BookingProcessArchiveServiceTest {

    @InjectMocks
    private BookingProcessArchiveService service;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private BookingRepository bookingRepository;

    @Mock
    private BookingEdiRepository bookingEdiRepository;

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private CargoDocumentRepository cargoDocumentRepository;

    @Mock
    private BookingDetailRepository bookingDetailRepository;

    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    @Mock
    private VesselProgrammingPortRepository vesselProgrammingPortRepository;

    @Mock
    private PortRepository portRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private CompanyRoleRepository companyRoleRepository;

    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;

    @Mock
    private ImoRepository imoRepository;

    @Mock
    private ServiceCoparnProcessFile9Service serviceCoparnProcessFile9Service;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalled_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(true, "FLAG_TO_FLEX")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingDetailRepository.findDODetailsByBookingId(anyInt())).thenReturn(getMockDODetails(true, 1));
        when(eirDocumentCargoDetailRepository.findActiveEirCargoDocumentDetailByIds(anyList())).thenReturn(List.of(101, 102));
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(cargoDocumentRepository.updateCargoDocumentDetails(any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(false);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(cargoDocumentRepository.updateCargoDocument(any(Integer.class), any(Integer.class), any(Integer.class))).thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(cargoDocumentRepository.updateCargoDischargeDocument(any(Integer.class), any(Integer.class), any(Integer.class))).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(cargoDocumentRepository.updateCargoDocumentByClientId(any(Integer.class), any(Integer.class), any(Integer.class))).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(cargoDocumentDetailRepository.updateCargoDocumentDetail(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("test edi remrk rule");
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(cargoDocumentDetailRepository.updateActiveDetails(anyString(), anyInt(), anyList(), anyString())).thenReturn(1);


        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, atLeastOnce()).findIdsByAliases(anyList());
        verify(catalogRepository, atLeastOnce()).findCodeByCatalogId(anyInt());
        verify(bookingRepository, atLeastOnce()).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, atLeastOnce()).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, atLeastOnce()).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, atLeastOnce()).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, atLeastOnce()).findById(anyInt());
        verify(vesselProgrammingDetailRepository, atLeastOnce()).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, atLeastOnce()).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, atLeastOnce()).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingDetailRepository, atLeastOnce()).findDODetailsByBookingId(anyInt());
        verify(eirDocumentCargoDetailRepository, atLeastOnce()).findActiveEirCargoDocumentDetailByIds(anyList());
        verify(bookingRepository, atLeastOnce()).updateBookingDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(cargoDocumentRepository, atLeastOnce()).updateCargoDocumentDetails(anyInt(), anyInt(), anyInt());
        verify(vesselProgrammingDetailRepository, atLeastOnce()).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, atLeastOnce()).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, atLeastOnce()).updateBooking(anyInt(), anyInt(), anyInt(), anyInt());
        verify(cargoDocumentRepository, atLeastOnce()).updateCargoDocument(anyInt(), anyInt(), anyInt());
        verify(portRepository, atLeastOnce()).findById(anyInt());
        verify(bookingRepository, atLeastOnce()).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(cargoDocumentRepository, atLeastOnce()).updateCargoDischargeDocument(anyInt(), anyInt(), anyInt());
        verify(companyRepository, atLeastOnce()).findTopByDocument(anyString());
        verify(companyRoleRepository, atLeastOnce()).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, atLeastOnce()).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalledWithApprovedFlagTrueAndRemarksEmpty_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                        .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(true, "FLAG_TO_FLEX")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("");
        when(bookingRepository.updateBookingRemarkRulesWithCommodityLogic(anyString(), anyInt(), anyString(), anyBoolean(), anyInt()))
                .thenReturn(1);
        when(bookingDetailRepository.updateBookingDetailsRemarkRules(anyString(), anyInt(), anyString(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findDODetailsByBookingId(anyInt())).thenReturn(getMockDODetails(false, 1));
        when(bookingDetailRepository.updateActiveBookingDetails(anyString(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.findBookingDetailsByBookingId(anyInt())).thenReturn(getMockBookingDetailsDTO());

        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, times(1)).findIdsByAliases(anyList());
        verify(catalogRepository, times(1)).findCodeByCatalogId(1);
        verify(catalogRepository, times(1)).findCodeByCatalogId(2);
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).findById(anyInt());
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, times(1)).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, times(1)).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingRepository, times(1)).updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, times(2)).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(portRepository, times(4)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(1)).findTopByDocument(anyString());
        verify(companyRoleRepository, times(1)).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(imoRepository, times(2)).findById(anyInt());

    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalledWithApprovedFlagFalseAndBKRemarksEmpty_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData1());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                        .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(false, "")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("FLAG_TO_FLEX");
        when(bookingRepository.updateBookingRemarkRulesWithCommodityLogic(anyString(), anyInt(), anyString(), anyBoolean(), anyInt()))
                .thenReturn(1);
        when(bookingDetailRepository.updateBookingDetailsRemarkRules(anyString(), anyInt(), anyString(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(cargoDocumentDetailRepository.updateActiveDetails(anyString(), anyInt(), anyList(), anyString())).thenReturn(1);
        when(bookingDetailRepository.updateActiveBookingDetails(anyString(), anyInt(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findNewBookingDetailsByBookingId(anyInt())).thenReturn(getMockNewBKDetails());
        when(bookingRepository.findBookingDetailsByBookingId(anyInt())).thenReturn(getMockBookingDetailsDTO());

        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, times(1)).findIdsByAliases(anyList());
        verify(catalogRepository, times(1)).findCodeByCatalogId(1);
        verify(catalogRepository, times(1)).findCodeByCatalogId(2);
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).findById(anyInt());
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, times(1)).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, times(1)).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingRepository, times(1)).updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, times(2)).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(portRepository, times(4)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(1)).findTopByDocument(anyString());
        verify(companyRoleRepository, times(1)).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(imoRepository, times(2)).findById(anyInt());

    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalledWithApprovedFlagTrueAndBKRemarksEmpty_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData1());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                        .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(true, "")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(bookingDetailRepository.findDODetailsByBookingId(anyInt())).thenReturn(getMockDODetails(false, 1));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("FLAG_TO_FLEX");
        when(bookingRepository.updateBookingRemarkRulesWithCommodityLogic(anyString(), anyInt(), anyString(), anyBoolean(), anyInt()))
                .thenReturn(1);
        when(bookingDetailRepository.updateBookingDetailsRemarkRules(anyString(), anyInt(), anyString(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(cargoDocumentDetailRepository.updateActiveDetails(anyString(), anyInt(), anyList(), anyString())).thenReturn(1);
        when(bookingDetailRepository.updateActiveBookingDetails(anyString(), anyInt(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findNewBookingDetailsByBookingId(anyInt())).thenReturn(getMockNewBKDetails());
        when(bookingRepository.findBookingDetailsByBookingId(anyInt())).thenReturn(getMockBookingDetailsDTO());

        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, times(1)).findIdsByAliases(anyList());
        verify(catalogRepository, times(1)).findCodeByCatalogId(1);
        verify(catalogRepository, times(1)).findCodeByCatalogId(2);
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, times(2)).findById(anyInt());
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, times(1)).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, times(1)).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingRepository, times(1)).updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, times(2)).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(portRepository, times(4)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(1)).findTopByDocument(anyString());
        verify(companyRoleRepository, times(1)).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(imoRepository, times(2)).findById(anyInt());


    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalledWithApprovedFlagTrueCondition_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData1());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                        .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(true, "")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(bookingDetailRepository.findDODetailsByBookingId(anyInt())).thenReturn(getMockDODetails(true, 1));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("FLAG_TO_FLEX");
        when(bookingRepository.updateBookingRemarkRulesWithCommodityLogic(anyString(), anyInt(), anyString(), anyBoolean(), anyInt()))
                .thenReturn(1);
        when(bookingDetailRepository.updateBookingDetailsRemarkRules(anyString(), anyInt(), anyString(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(cargoDocumentDetailRepository.updateActiveDetails(anyString(), anyInt(), anyList(), anyString())).thenReturn(1);
        when(bookingDetailRepository.updateActiveBookingDetails(anyString(), anyInt(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findNewBookingDetailsByBookingId(anyInt())).thenReturn(getMockNewBKDetails());
        when(bookingRepository.findBookingDetailsByBookingId(anyInt())).thenReturn(getMockBookingDetailsDTO());
        when(cargoDocumentDetailRepository.findActiveCargoDocumentDetailCount(anyList())).thenReturn(2);
        when(bookingDetailRepository.updateReservationQuantity(anyInt(), anyInt(), anyString(), anyInt(), anyInt())).thenReturn(2);

        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, times(1)).findIdsByAliases(anyList());
        verify(catalogRepository, times(1)).findCodeByCatalogId(1);
        verify(catalogRepository, times(1)).findCodeByCatalogId(2);
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).findById(anyInt());
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, times(1)).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, times(1)).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingRepository, times(1)).updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, times(2)).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(portRepository, times(4)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(1)).findTopByDocument(anyString());
        verify(companyRoleRepository, times(1)).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(imoRepository, times(2)).findById(anyInt());

    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalledWithApprovedFlagTrueAndRemarksEmptyScenarios_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                        .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(true, "FLAG_TO_FLEX")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("");
        when(bookingRepository.updateBookingRemarkRulesWithCommodityLogic(anyString(), anyInt(), anyString(), anyBoolean(), anyInt()))
                .thenReturn(1);
        when(bookingDetailRepository.updateBookingDetailsRemarkRules(anyString(), anyInt(), anyString(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails()).thenReturn(getMockMyDetails1());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findDODetailsByBookingId(anyInt())).thenReturn(getMockDODetails1(20)).thenReturn(getMockDODetails1(20));
        when(bookingDetailRepository.updateActiveBookingDetails(anyString(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.findBookingDetailsByBookingId(anyInt())).thenReturn(getMockBookingDetailsDTO());
        when(eirDocumentCargoDetailRepository.findActiveEirCargoDocumentDetailByIds(anyList())).thenReturn(List.of(1101, 1102));
        when(cargoDocumentDetailRepository.findActiveCargoDocumentDetailCount(anyList())).thenReturn(2);
        when(cargoDocumentDetailRepository.updateActiveDetails(anyString(), anyInt(), anyList(), anyString())).thenReturn(1);
        when(bookingDetailRepository.updateReservationQuantity(anyInt(), anyInt(), anyString(), anyInt(), anyInt())).thenReturn(2);
        when(bookingDetailRepository.updateInactiveBookingDetails(anyInt(), anyInt())).thenReturn(1);


        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, times(1)).findIdsByAliases(anyList());
        verify(catalogRepository, times(1)).findCodeByCatalogId(1);
        verify(catalogRepository, times(1)).findCodeByCatalogId(2);
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).findById(anyInt());
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, times(1)).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, times(1)).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingRepository, times(1)).updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, times(2)).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(portRepository, times(4)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(1)).findTopByDocument(anyString());
        verify(companyRoleRepository, times(1)).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(imoRepository, times(2)).findById(anyInt());

    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalledWithApprovedFlagFalseAndRemarksEmpty_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                        .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(false, "FLAG_TO_FLEX")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("");
        when(bookingRepository.updateBookingRemarkRulesWithCommodityLogic(anyString(), anyInt(), anyString(), anyBoolean(), anyInt()))
                .thenReturn(1);
        when(bookingDetailRepository.updateBookingDetailsRemarkRules(anyString(), anyInt(), anyString(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findDODetailsByBookingId(anyInt())).thenReturn(getMockDODetails(false, 1));
        when(bookingDetailRepository.updateActiveBookingDetails(anyString(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.findBookingDetailsByBookingId(anyInt())).thenReturn(getMockBookingDetailsDTO());

        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, times(1)).findIdsByAliases(anyList());
        verify(catalogRepository, times(1)).findCodeByCatalogId(1);
        verify(catalogRepository, times(1)).findCodeByCatalogId(2);
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).findById(anyInt());
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, times(1)).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, times(1)).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingRepository, times(1)).updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, times(2)).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(portRepository, times(4)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(1)).findTopByDocument(anyString());
        verify(companyRoleRepository, times(1)).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(imoRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt());

    }

    @Test
    void Given_BookingExistsInRepository_When_ExecuteCalledWithApprovedFlagTrueAndRemarksNonEmptyScenarios_Then_ProcessSuccessfully() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData());
        when(catalogRepository.findCodeByCatalogId(1)).thenReturn("1");
        when(catalogRepository.findCodeByCatalogId(2)).thenReturn("1");
        when(bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt()))
                .thenReturn(Optional.empty());
        when(bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt()))
                .thenReturn(Optional.of(Booking.builder()
                        .id(1)
                        .catBookingStatus(Catalog.builder().id(43062).build())
                        .build()));
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(true);
        when(bookingRepository.findTopBookingByFilters(anyString(), anyInt(), anyInt()))
                .thenReturn(Optional.of(Booking.builder().id(2).build()));
        when(bookingRepository.findById(anyInt())).thenReturn(Optional.of(getMockBooking(true, "FLAG_TO_FLEX")));
        when(vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(anyInt())).thenReturn(getMockVesselDetailDTO());
        when(bookingDetailRepository.findCargoDocumentIdByBookingId(anyInt())).thenReturn(null);
        when(cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt()))
                .thenReturn(201);
        when(bookingRepository.updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(vesselProgrammingDetailRepository.findVesselDetail(anyInt())).thenReturn(Optional.of("vessel details string"));
        when(vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class)))
                .thenReturn(1);
        when(portRepository.findById(anyInt())).thenReturn(Optional.of(getMockPort()));
        when(bookingRepository.updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findTopByDocument(anyString())).thenReturn(Optional.of(999));
        when(companyRoleRepository.doesNotExistByCompanyAndRole(anyInt(), anyInt())).thenReturn(true);
        when(bookingRepository.updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(companyRepository.findById(anyInt())).thenReturn(Optional.of(getMockCompany()));
        when(bookingRepository.updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(imoRepository.findById(anyInt())).thenReturn(Optional.of(getMockImo()));
        when(bookingRepository.updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.updateBookingColdTreatment(anyBoolean(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(1);
        when(bookingEdiRepository.findRemarkRulesNameById(anyInt())).thenReturn("FLAG_TO_FLEX");
        when(bookingDetailRepository.findBookingDetailsAggregated(anyInt())).thenReturn(getMockMyDetails()).thenReturn(getMockMyDetails1());
        when(bookingRepository.updateBooking(anyInt(), anyInt(), anyInt(), anyInt(), anyBoolean())).thenReturn(1);
        when(bookingEdiRepository.updateBookingEdi(anyString(), anyString(), anyInt())).thenReturn(1);
        when(bookingEdiRepository.updateProcessed(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(bookingDetailRepository.findDODetailsByBookingId(anyInt())).thenReturn(getMockDODetails1(40)).thenReturn(getMockDODetails1(20));
        when(bookingDetailRepository.updateActiveBookingDetails(anyString(), anyInt(), anyInt())).thenReturn(1);
        when(bookingRepository.findBookingDetailsByBookingId(anyInt())).thenReturn(getMockBookingDetailsDTO());
        when(eirDocumentCargoDetailRepository.findActiveEirCargoDocumentDetailByIds(anyList())).thenReturn(List.of(1101, 1102));
        when(cargoDocumentDetailRepository.findActiveCargoDocumentDetailCount(anyList())).thenReturn(2);
        when(cargoDocumentDetailRepository.updateActiveDetails(anyString(), anyInt(), anyList(), anyString())).thenReturn(1);
        when(bookingDetailRepository.updateReservationQuantity(anyInt(), anyInt(), anyString(), anyInt(), anyInt())).thenReturn(2);
        when(bookingDetailRepository.updateInactiveBookingDetails(anyInt(), anyInt())).thenReturn(1);


        // WHEN
        service.execute(input);

        // THEN
        verify(catalogRepository, times(1)).findIdsByAliases(anyList());
        verify(catalogRepository, times(1)).findCodeByCatalogId(1);
        verify(catalogRepository, times(1)).findCodeByCatalogId(2);
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndVesselProgrammingDetailId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTop1ByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt());
        verify(bookingRepository, times(1)).findTopBookingByFilters(anyString(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).findById(anyInt());
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetailByProgrammingDetailId(anyInt());
        verify(bookingDetailRepository, times(1)).findCargoDocumentIdByBookingId(anyInt());
        verify(cargoDocumentRepository, times(1)).findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(anyString(), anyInt());
        verify(bookingRepository, times(1)).updateBookingDetails(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(vesselProgrammingDetailRepository, times(1)).findVesselDetail(anyInt());
        verify(vesselProgrammingPortRepository, times(2)).existsByVesselProgrammingIdAndPortId(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBooking(any(Integer.class), any(Integer.class), any(Integer.class), any(Integer.class));
        verify(portRepository, times(4)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingDischargeDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(1)).findTopByDocument(anyString());
        verify(companyRoleRepository, times(1)).doesNotExistByCompanyAndRole(anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingByCustIdAndBookingId(anyInt(), anyInt(), anyInt(), anyInt());
        verify(companyRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBooking(anyString(), anyInt(), anyInt(), anyInt(), anyInt());
        verify(bookingRepository, times(1)).updateBookingImoDetails(anyInt(), anyInt(), anyInt(), anyInt());
        verify(imoRepository, times(2)).findById(anyInt());
        verify(bookingRepository, times(1)).updateBookingTemperature(anyString(), anyInt(), anyInt(), anyInt());

    }

    @Test
    void Given_BookingDoesNotExistInRepository_When_ExecuteCalled_Then_ProcessAsNewArchive() {
        // GIVEN
        BookingProcessArchiveInputDTO input = getMockInputDTO();
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData());
        when(catalogRepository.findCodeByCatalogId(anyInt())).thenReturn("1");
        when(bookingRepository.existsByBookingNumberAndSubBusinessUnitId(anyString(), anyInt())).thenReturn(false);

        // WHEN
        service.execute(input);

        // THEN
        verify(serviceCoparnProcessFile9Service, times(1)).serviceCoparnProcessFile9Service(any(ServiceCoparnProcessFile9Input.class));
        verify(bookingRepository, never()).updateBookingDetails(anyInt(), anyInt(), anyInt(), anyInt());
    }


    private List<Object[]> getMockCatalogData() {
        return Arrays.asList(
                new Object[]{"48272", 101},
                new Object[]{"48273", 102},
                new Object[]{"cr_sd1_client", 103},
                new Object[]{"31049", 104},
                new Object[]{"31053", 105},
                new Object[]{"sd1_bkedi_reference_replace", 106}
        );
    }

    private List<Object[]> getMockCatalogData1() {
        return Arrays.asList(
                new Object[]{"48272", 101},
                new Object[]{"48273", 102},
                new Object[]{"cr_sd1_client", 103},
                new Object[]{"31049", 31048},
                new Object[]{"31053", 105},
                new Object[]{"sd1_bkedi_reference_replace", 106}
        );
    }

    private BookingProcessArchiveInputDTO getMockInputDTO() {
        return BookingProcessArchiveInputDTO.builder()
                .ediCoparnId(123)
                .businessUnitId(1)
                .subBusinessUnitId(2)
                .vesselProgrammingDetailId(1001)
                .booking("BOOK123")
                .containerDimensionId(20)
                .containerTypeId(1)
                .reservedQuantity(10)
                .secondaryContainerDimensionId(40)
                .secondaryContainerTypeId(2)
                .secondaryReservedQuantity(5)
                .clientId(2001)
                .clientRS("CLIENT_RS")
                .productGroupDescription("Group_A")
                .productId(5001)
                .portOfLoadingId(101)
                .portOfDestinationId(202)
                .portOfDischargeId(303)
                .temperature("5,5")
                .imoId(10)
                .lineBkId(3001)
                .grossWeightEdi(500)
                .secondaryGrossWeightEdi(300)
                .coldTreatment(true)
                .controlledAtmosphere(true)
                .userRegistrationId(777)
                .paramSequenceDetails("ParamSequence_ABC")
                .build();
    }

    private Booking getMockBooking(boolean approvedBooking, String remark) {
        return Booking.builder()
                .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                        .id(1)
                        .build())
                .approvedBooking(approvedBooking)
                .remarkRulesName(remark)
                .destinationPort(Port.builder().id(101).build())
                .dischargePort(Port.builder().id(102).build())
                .clientCompany(Company.builder().id(501).build())
                .commodity("TestCommodity | OK TO FLEX".replace(" | OK TO FLEX", ""))
                .imo(Imo.builder().id(301).build())
                .temperatureC("25,1")
                .isColdtreatment(false)
                .isControlledAtmosphere(false)
                .build();
    }

    private VesselDetailDTO getMockVesselDetailDTO() {
        VesselDetailDTO vesselDetailDTO = new VesselDetailDTO();
        vesselDetailDTO.setVesselProgrammingId(200);
        vesselDetailDTO.setVesselDetail("Test Vessel");
        return vesselDetailDTO;
    }

    private Port getMockPort() {
        Port port = new Port();
        port.setPort("Test Port");
        return port;
    }

    private Company getMockCompany() {
        Company company = new Company();
        company.setDocument("DUMMY_DOC");
        return company;
    }

    private Imo getMockImo() {
        Imo imo = new Imo();
        imo.setImoCode("IMOCODE1");
        return imo;
    }

    private List<DODetail> getMockDODetails(boolean active, int doNotTouch) {
        List<DODetail> list = new ArrayList<>();
        list.add(DODetail.builder()
                .cargoDocumentId(1)
                .cargoDocumentDetailId(101)
                .containerId(1001)
                .receivedQuantity(new BigDecimal("5.0"))
                .containerTypeManifestedId(201)
                .sizeManifestedId(301)
                .active(active)
                .bookingDetailId(401)
                .doNotTouch(doNotTouch)
                .build());
        list.add(DODetail.builder()
                .cargoDocumentId(2)
                .cargoDocumentDetailId(102)
                .containerId(1002)
                .receivedQuantity(new BigDecimal("10.0"))
                .containerTypeManifestedId(202)
                .sizeManifestedId(302)
                .active(active)
                .bookingDetailId(402)
                .doNotTouch(doNotTouch)
                .build());
        return list;
    }

    private List<MyDetail> getMockMyDetails() {
        List<MyDetail> list = new ArrayList<>();
        list.add(MyDetail.builder()
                .catSizeId(31048)
                .catContainerTypeId(20)
                .quantity(10)
                .build());
        list.add(MyDetail.builder()
                .catSizeId(40)
                .catContainerTypeId(31048)
                .quantity(5)
                .build());
        return list;
    }

    private BookingDataDTO getMockBookingDetailsDTO() {
        return BookingDataDTO.builder()
                .imoId(1)
                .catConditionLoadId(1)
                .productGroupDescription("product description")
                .build();
    }

    private List<NewBKDetail> getMockNewBKDetails() {
        return new ArrayList<>(List.of(
                NewBKDetail.builder()
                        .bookingDetailId(1)
                        .reservationQuantity(100)
                        .catSize(10)
                        .catContainerType(5)
                        .build()
        ));
    }

    private List<DODetail> getMockDODetails1(Integer sizeManifestedId) {
        List<DODetail> list = new ArrayList<>();
        list.add(DODetail.builder()
                .cargoDocumentId(1)
                .cargoDocumentDetailId(101)
                .containerTypeManifestedId(31048)
                .sizeManifestedId(sizeManifestedId)
                .active(true)
                .bookingDetailId(401)
                .doNotTouch(0)
                .build());
        list.add(DODetail.builder()
                .cargoDocumentId(2)
                .cargoDocumentDetailId(102)
                .containerId(1002)
                .containerTypeManifestedId(31048)
                .sizeManifestedId(sizeManifestedId)
                .active(true)
                .bookingDetailId(402)
                .doNotTouch(0)
                .build());
        return list;
    }

    private List<MyDetail> getMockMyDetails1() {
        List<MyDetail> list = new ArrayList<>();
        list.add(MyDetail.builder()
                .catSizeId(20)
                .catContainerTypeId(31048)
                .quantity(2)
                .build());
        list.add(MyDetail.builder()
                .catSizeId(40)
                .catContainerTypeId(31048)
                .quantity(2)
                .build());
        return list;
    }

}
