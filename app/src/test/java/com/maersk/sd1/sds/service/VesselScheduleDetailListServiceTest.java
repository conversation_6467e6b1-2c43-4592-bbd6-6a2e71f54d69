package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.sds.dto.VesselScheduleDetailListOutput;
import com.maersk.sd1.common.repository.VesselProgrammingDetailRepository;
import com.maersk.sd1.common.repository.VesselProgrammingCutoffRepository;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.model.VesselProgrammingCutoff;
import com.maersk.sd1.common.model.ShippingLine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VesselScheduleDetailListServiceTest {

    @InjectMocks
    private VesselScheduleDetailListService service;

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;

    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;

    private VesselProgrammingDetail vesselDetail;
    private VesselProgrammingCutoff vesselCutoff;

    @BeforeEach
    void setUp() {
        Catalog catOperation = new Catalog();
        catOperation.setId(101);

        vesselDetail = new VesselProgrammingDetail();
        vesselDetail.setId(1);
        vesselDetail.setCatOperation(catOperation);
        vesselDetail.setManifestYear(String.valueOf(2025));
        vesselDetail.setManifestNumber("MAN123");
        vesselDetail.setBeginningOperation(LocalDateTime.of(2025, 2, 10, 10, 0));
        vesselDetail.setEndingOperation(LocalDateTime.of(2025, 2, 12, 18, 0));

        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(202);
        shippingLine.setShippingLineCompany("Maersk");

        vesselCutoff = new VesselProgrammingCutoff();
        vesselCutoff.setShippingLine(shippingLine);
        vesselCutoff.setDateCutoffRetreatEmptyDry(LocalDateTime.of(2025, 2, 8, 12, 0));
        vesselCutoff.setDateCutoffRetreatEmptyReefer(LocalDateTime.of(2025, 2, 9, 14, 0));
    }

    @Test
    void retrieveVesselProgrammingDetail_ShouldReturnValidOutput_WhenDetailExists() {
        Integer detailId = 1;
        Integer languageId = 1;

        when(vesselProgrammingDetailRepository.findByIdAndActive(detailId, true))
                .thenReturn(Optional.of(vesselDetail));

        when(catalogLanguageRepository.fnCatalogoTraducidoDesLarga(101, languageId))
                .thenReturn("Operation Name");

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActive(detailId, true))
                .thenReturn(List.of(vesselCutoff));

        VesselScheduleDetailListOutput output = service.retrieveVesselProgrammingDetail(detailId, languageId);

        assertNotNull(output);
        assertEquals(1, output.getDetailId());
        assertEquals("Operation Name", output.getOperation());
        assertEquals("2025", output.getManifestYear());
        assertEquals(1, output.getCutoffShippingLines().size());
        assertEquals(202, output.getCutoffShippingLines().getFirst().getShippingLineId());
        assertEquals("Maersk", output.getCutoffShippingLines().getFirst().getShippingLineCompany());

        verify(vesselProgrammingDetailRepository, times(1)).findByIdAndActive(detailId, true);
        verify(vesselProgrammingCutoffRepository, times(1)).findByVesselProgrammingDetailIdAndActive(detailId, true);
        verify(catalogLanguageRepository, times(1)).fnCatalogoTraducidoDesLarga(101, languageId);
    }

    @Test
    void retrieveVesselProgrammingDetailShouldThrowException_WhenDetailNotFound() {
        Integer detailId = 99;
        Integer languageId = 1;

        when(vesselProgrammingDetailRepository.findByIdAndActive(detailId, true))
                .thenReturn(Optional.empty());

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> service.retrieveVesselProgrammingDetail(detailId, languageId));

        assertEquals("No VesselProgrammingDetail found for the provided detailId.", exception.getMessage());

        verify(vesselProgrammingDetailRepository, times(1)).findByIdAndActive(detailId, true);
        verifyNoInteractions(catalogLanguageRepository);
        verifyNoInteractions(vesselProgrammingCutoffRepository);
    }

    @Test
    void retrieveVesselProgrammingDetailShouldHandleNullOperation() {
        Integer detailId = 1;
        Integer languageId = 1;

        vesselDetail.setCatOperation(null);

        when(vesselProgrammingDetailRepository.findByIdAndActive(detailId, true))
                .thenReturn(Optional.of(vesselDetail));

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActive(detailId, true))
                .thenReturn(List.of(vesselCutoff));

        VesselScheduleDetailListOutput output = service.retrieveVesselProgrammingDetail(detailId, languageId);

        assertNull(output.getOperation());
        assertNull(output.getCatOperationId());

        verify(vesselProgrammingDetailRepository, times(1)).findByIdAndActive(detailId, true);
        verify(vesselProgrammingCutoffRepository, times(1)).findByVesselProgrammingDetailIdAndActive(detailId, true);
        verifyNoInteractions(catalogLanguageRepository);
    }

    @Test
    void retrieveVesselProgrammingDetailShouldReturnEmptyCutoffListWhenNoCutoffRecords() {
        Integer detailId = 1;
        Integer languageId = 1;

        when(vesselProgrammingDetailRepository.findByIdAndActive(detailId, true))
                .thenReturn(Optional.of(vesselDetail));

        when(catalogLanguageRepository.fnCatalogoTraducidoDesLarga(101, languageId))
                .thenReturn("Operation Name");

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActive(detailId, true))
                .thenReturn(List.of());

        VesselScheduleDetailListOutput output = service.retrieveVesselProgrammingDetail(detailId, languageId);

        assertNotNull(output);
        assertEquals(0, output.getCutoffShippingLines().size());

        verify(vesselProgrammingCutoffRepository, times(1)).findByVesselProgrammingDetailIdAndActive(detailId, true);
    }

    @Test
    void retrieveVesselProgrammingDetailShouldHandleDatabaseException() {
        Integer detailId = 1;
        Integer languageId = 1;

        when(vesselProgrammingDetailRepository.findByIdAndActive(detailId, true))
                .thenThrow(new DataAccessException("Database error") {});

        assertThrows(DataAccessException.class, () -> service.retrieveVesselProgrammingDetail(detailId, languageId));

        verify(vesselProgrammingDetailRepository, times(1)).findByIdAndActive(detailId, true);
    }
}

