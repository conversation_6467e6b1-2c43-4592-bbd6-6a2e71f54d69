package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.*;
import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.sds.method.MessageFunctionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BookingDetailEditServiceTest {

    @Mock
    private BookingDetailRepository bookingDetailRepository;

    @Mock
    private MessageFunctionService messageFunctionService;

    @InjectMocks
    private BookingDetailEditService bookingDetailEditService;

    private BookingDetailEditInputDTO.Input input;

    @BeforeEach
    public void setUp() {
        input = new BookingDetailEditInputDTO.Input();
        input.setBookingDetailId(1);
        input.setBookingId(1);
        input.setSizeCategoryId(1);
        input.setContainerTypeId(1);
        input.setReservedQuantity(10.0);
        input.setMaxRequiredLoad(100.0);
        input.setBookingCreationSourceId(1.0);
        input.setModifiedByUserId(1);
        input.setLanguageId(1);
    }


    @Test
    void Given_ApprovedBookingDetail_When_SetProperties_Then_PropertiesAreSetCorrectly() {
        BookingDetailEditService.ApprovedBookingDetail detail = new BookingDetailEditService.ApprovedBookingDetail();
        detail.setCargoDocumentoId(1);
        detail.setPackagingTypeId(43009.0);
        detail.setWeightUnitId(43012.0);
        detail.setIsDangerousCargo(true);
        detail.setMerchandise("Merchandise");
        detail.setLoadConditionId(1);
        detail.setBookingCreationSourceId(1.0);
        detail.setModifiedByUserId(1);
        detail.setQuantityUnitId(43014.0);
        detail.setBookingId(1);
        detail.setTempBookingId(1);

        assertEquals(1, detail.getCargoDocumentoId());
        assertEquals(43009.0, detail.getPackagingTypeId());
        assertEquals(43012.0, detail.getWeightUnitId());
        assertTrue(detail.getIsDangerousCargo());
        assertEquals("Merchandise", detail.getMerchandise());
        assertEquals(1, detail.getLoadConditionId());
        assertEquals(1.0, detail.getBookingCreationSourceId());
        assertEquals(1, detail.getModifiedByUserId());
        assertEquals(43014.0, detail.getQuantityUnitId());
        assertEquals(1, detail.getBookingId());
        assertEquals(1, detail.getTempBookingId());
    }

    @Test
    void Given_DocumentCargo_When_SetProperties_Then_PropertiesAreSetCorrectly() {
        BookingDetailEditService.DocumentCargo cargo = new BookingDetailEditService.DocumentCargo();
        cargo.setDocumentoCargaId(1);
        cargo.setProductoId(1);
        cargo.setCatEmbalajeId(1);
        cargo.setCantidadManifestada(100.0);
        cargo.setPesoManifestado(100.0);
        cargo.setVolumenManifestado(100.0);
        cargo.setCatUnidadMedidaPesoId(1);
        cargo.setDiceContener(1);
        cargo.setEsCargaPeligrosa(true);
        cargo.setEsCargaRefrigerada(true);
        cargo.setMercaderia("Merchandise");
        cargo.setCatCondicionCargaId(1);
        cargo.setGateoutEmptyLiquidado(1);
        cargo.setActivo(1);
        cargo.setCatOrigenCreacionId(1);
        cargo.setUsuarioRegistroId(1);
        cargo.setFechaRegistro("2024-11-28");
        cargo.setCatTipoContenedorManifestadoId(1);
        cargo.setCatTamanoManifestadoId(1);
        cargo.setCatUnidadMedidaCantidadId(1);
        cargo.setBookingDetalleId(1);
        cargo.setTraceDocCargaDetalle("Trace");

        assertEquals(1, cargo.getDocumentoCargaId());
        assertEquals(1, cargo.getProductoId());
        assertEquals(1, cargo.getCatEmbalajeId());
        assertEquals(100.0, cargo.getCantidadManifestada());
        assertEquals(100.0, cargo.getPesoManifestado());
        assertEquals(100.0, cargo.getVolumenManifestado());
        assertEquals(1, cargo.getCatUnidadMedidaPesoId());
        assertEquals(1, cargo.getDiceContener());
        assertTrue(cargo.getEsCargaPeligrosa());
        assertTrue(cargo.getEsCargaRefrigerada());
        assertEquals("Merchandise", cargo.getMercaderia());
        assertEquals(1, cargo.getCatCondicionCargaId());
        assertEquals(1, cargo.getGateoutEmptyLiquidado());
        assertEquals(1, cargo.getActivo());
        assertEquals(1, cargo.getCatOrigenCreacionId());
        assertEquals(1, cargo.getUsuarioRegistroId());
        assertEquals("2024-11-28", cargo.getFechaRegistro());
        assertEquals(1, cargo.getCatTipoContenedorManifestadoId());
        assertEquals(1, cargo.getCatTamanoManifestadoId());
        assertEquals(1, cargo.getCatUnidadMedidaCantidadId());
        assertEquals(1, cargo.getBookingDetalleId());
        assertEquals("Trace", cargo.getTraceDocCargaDetalle());
    }

    @Test
    void Given_BookingDetailNotFound_When_ValidUpdateRequest_Then_ErrorResponseReturned() {

        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(Collections.emptyList());

        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        assertNotNull(output, "Output should not be null");

    }


    @Test
    void Given_InvalidSizeCategory_When_ValidUpdateRequest_Then_ErrorResponseReturned() {
        input.setSizeCategoryId(9999);  // Invalid size category
        List<BookDetailDTO> bookingDetails = Collections.singletonList(new BookDetailDTO(1, 1, 100, 10));
        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(bookingDetails);

        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        assertNotNull(output, "Output should not be null");

    }


    @Test
    void Given_ApprovedBooking_When_UpdateDetailRequest_Then_UpdateBookingDetail() {
        List<BookDetailDTO> bookingDetails = Collections.singletonList(new BookDetailDTO(1, 1, 100, 10));
        List<BookingInfoDTO> fetchBookingDetails = Collections.singletonList(new BookingInfoDTO(true, 1, "BN123", 1, "Merchandise", 1, "Remark"));
        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(bookingDetails);
        when(bookingDetailRepository.fetchBookingDetails(anyInt())).thenReturn(fetchBookingDetails);
        when(messageFunctionService.getTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");

        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        assertEquals(1, output.getPrefix().getOutput().getResponseStatus());
        assertEquals("Success", output.getPrefix().getOutput().getResponseMessage());
        verify(bookingDetailRepository, times(1)).updateBookingDetail(anyInt(), anyInt(), anyDouble(), anyDouble(), anyInt(), anyInt());
    }

    @Test
    void Given_BookingApproved_When_DetailExistsAndQuantityDoesNotMatch_Then_ResponseWithError() {
        input.setReservedQuantity(20.0);
        List<BookDetailDTO> bookingDetails = Collections.singletonList(new BookDetailDTO(1, 1, 100, 10));
        List<BookingInfoDTO> fetchBookingDetails = Collections.singletonList(new BookingInfoDTO(true, 1, "BN123", 1, "Merchandise", 1, "Remark"));

        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(bookingDetails);
        when(bookingDetailRepository.fetchBookingDetails(anyInt())).thenReturn(fetchBookingDetails);

        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        assertNotNull(output, "Output should not be null");

    }


    @Test
    void Given_ExceptionDuringUpdate_When_ValidUpdateRequest_Then_ErrorMessageReturned() {
        when(bookingDetailRepository.getBookingDetails(anyInt())).thenThrow(new RuntimeException("Repository failure"));
        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        assertEquals(1, output.getPrefix().getOutput().getResponseStatus());
        assertEquals("Error occurred while processing the booking detail update", output.getPrefix().getOutput().getResponseMessage());
    }

    @Test
    void Given_ValidUpdateRequestWithRefrigeratedLoad_When_QuantityIncreased_Then_UpdateCargoDetail() {
        input.setReservedQuantity(30.0);
        List<BookDetailDTO> bookingDetails = Collections.singletonList(new BookDetailDTO(1, 1, 100, 10));
        List<BookingInfoDTO> fetchBookingDetails = Collections.singletonList(new BookingInfoDTO(true, 1, "BN123", 1, "Merchandise", 1, "Remark"));

        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(bookingDetails);
        when(bookingDetailRepository.fetchBookingDetails(anyInt())).thenReturn(fetchBookingDetails);
        when(bookingDetailRepository.getRefrigeratedLoadStatus(anyInt())).thenReturn(1);
        when(messageFunctionService.getTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");

        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        assertNotNull(output, "Output should not be null");

        assertEquals(30.0, input.getReservedQuantity(), "Reserved quantity should be updated to 30.0");
    }

    @Test
    void Given_ValidUpdateRequest_When_BookingDetailUpdated_Then_SuccessResponseReturned() {
        // Arrange
        List<BookDetailDTO> bookingDetails = Collections.singletonList(new BookDetailDTO(1, 1, 100, 10));
        List<BookingInfoDTO> fetchBookingDetails = Collections.singletonList(new BookingInfoDTO(true, 1, "BN123", 1, "Merchandise", 1, "Remark"));

        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(bookingDetails);
        when(bookingDetailRepository.fetchBookingDetails(anyInt())).thenReturn(fetchBookingDetails);
        when(messageFunctionService.getTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");

        // Act
        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        // Assert
        assertNotNull(output, "Output should not be null");
        assertEquals(1, output.getPrefix().getOutput().getResponseStatus());
        assertEquals("Success", output.getPrefix().getOutput().getResponseMessage());
        verify(bookingDetailRepository, times(1)).updateBookingDetail(anyInt(), anyInt(), anyDouble(), anyDouble(), anyInt(), anyInt());
    }

    @Test
    void Given_ValidUpdateRequestWithIncreasedQuantity_When_UpdateDetailDOToMore_Then_InsertDocumentoCargaDetalle() {
        // Arrange
        input.setReservedQuantity(30.0); // Increase the reserved quantity
        List<BookDetailDTO> bookingDetails = Collections.singletonList(new BookDetailDTO(1, 1, 100, 10));
        List<BookingInfoDTO> fetchBookingDetails = Collections.singletonList(new BookingInfoDTO(true, 1, "BN123", 1, "Merchandise", 1, "Remark"));

        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(bookingDetails);
        when(bookingDetailRepository.fetchBookingDetails(anyInt())).thenReturn(fetchBookingDetails);
        when(bookingDetailRepository.getRefrigeratedLoadStatus(anyInt())).thenReturn(1); // Refrigerated load
        when(messageFunctionService.getTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");
        // Act
        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        // Assert
        assertNotNull(output, "Output should not be null");
        assertEquals(2, output.getPrefix().getOutput().getResponseStatus());
    }

    @Test
    void Given_ValidUpdateRequestWithRefrigeratedLoad_When_QuantityDecreased_Then_UpdateCargoDetail() {
        input.setReservedQuantity(5.0);
        List<BookDetailDTO> bookingDetails = Collections.singletonList(new BookDetailDTO(1, 1, 100, 10));
        List<BookingInfoDTO> fetchBookingDetails = Collections.singletonList(new BookingInfoDTO(true, 1, "BN123", 1, "Merchandise", 1, "Remark"));

        when(bookingDetailRepository.getBookingDetails(anyInt())).thenReturn(bookingDetails);
        when(bookingDetailRepository.fetchBookingDetails(anyInt())).thenReturn(fetchBookingDetails);
        when(bookingDetailRepository.getRefrigeratedLoadStatus(anyInt())).thenReturn(1);  // Refrigerated load
        when(messageFunctionService.getTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");

        BookingDetailEditOutputDTO.Root output = bookingDetailEditService.editBookingDetail(input);

        assertNotNull(output, "Output should not be null");
    }

}
