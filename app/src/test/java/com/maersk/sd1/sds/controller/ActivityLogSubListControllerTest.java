package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ActivityLogSubListInput;
import com.maersk.sd1.sds.controller.dto.ActivityLogSubListOutput;
import com.maersk.sd1.sds.service.ActivityLogSubListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ActivityLogSubListControllerTest {

    @Mock
    private ActivityLogSubListService activityLogSubListService;

    @InjectMocks
    private ActivityLogSubListController controller;

    private ActivityLogSubListInput.Root validRequest;

    @BeforeEach
    void setUp() {
        validRequest = new ActivityLogSubListInput.Root();
        ActivityLogSubListInput.Prefix prefix = new ActivityLogSubListInput.Prefix();
        ActivityLogSubListInput.Input input = new ActivityLogSubListInput.Input();
        input.setActivityLogId(1);
        input.setPage(1);
        input.setSize(10);
        prefix.setInput(input);
        validRequest.setPrefix(prefix);
    }

    @Test
    void givenValidRequest_WhenGetSubList_ThenReturnSuccessResponse() {
        ActivityLogSubListOutput output = new ActivityLogSubListOutput();
        output.setTotalRegisters(5);
        when(activityLogSubListService.getActivityLogSubList(any())).thenReturn(output);

        ResponseEntity<ResponseController<ActivityLogSubListOutput>> response = controller.getSubList(validRequest);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
    }

    @Test
    void givenInvalidRequest_WhenGetSubList_ThenReturnBadRequestResponse() {
        ActivityLogSubListInput.Root invalidRequest = new ActivityLogSubListInput.Root();
        ResponseEntity<ResponseController<ActivityLogSubListOutput>> response = controller.getSubList(invalidRequest);

        assertNotNull(response);
        assertEquals(400, response.getStatusCode().value());
    }

    @Test
    void givenServiceThrowsException_WhenGetSubList_ThenReturnErrorResponse() {
        when(activityLogSubListService.getActivityLogSubList(any())).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<ActivityLogSubListOutput>> response = controller.getSubList(validRequest);

        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
    }
}