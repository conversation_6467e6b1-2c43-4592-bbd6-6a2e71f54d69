package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursGetInput;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursGetOutput;
import com.maersk.sd1.sds.service.EstimateEmrCostManHoursService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EstimateEmrCostManHoursControllerTest {

    @Mock
    private EstimateEmrCostManHoursService estimateEmrCostManHoursService;

    @InjectMocks
    private EstimateEmrCostManHoursController estimateEmrCostManHoursController;

    private EstimateEmrCostManHoursGetInput.Root validRequest;

    @BeforeEach
    void setUp() {
        EstimateEmrCostManHoursGetInput.Input input = new EstimateEmrCostManHoursGetInput.Input();
        input.setEstimateEmrCostManHoursId(1);

        EstimateEmrCostManHoursGetInput.Prefix prefix = new EstimateEmrCostManHoursGetInput.Prefix();
        prefix.setInput(input);

        validRequest = new EstimateEmrCostManHoursGetInput.Root();
        validRequest.setPrefix(prefix);
    }

    @Test
    void given_ValidRequest_When_ServiceReturnsOutput_Then_ReturnsStatus200() {
        // Arrange
        EstimateEmrCostManHoursGetOutput output = new EstimateEmrCostManHoursGetOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");
        output.setEstimateEmrCostManHoursId(1);

        when(estimateEmrCostManHoursService.getEstimateEmrCostManHours(any(Integer.class))).thenReturn(output);

        // Act
        ResponseEntity<ResponseController<EstimateEmrCostManHoursGetOutput>> response =
                estimateEmrCostManHoursController.getEstimateEmrCostManHours(validRequest);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void given_ValidRequest_When_ServiceThrowsException_Then_ReturnsStatus500() {
        // Arrange
        when(estimateEmrCostManHoursService.getEstimateEmrCostManHours(any(Integer.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        ResponseEntity<ResponseController<EstimateEmrCostManHoursGetOutput>> response =
                estimateEmrCostManHoursController.getEstimateEmrCostManHours(validRequest);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Test exception", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void given_InvalidRequest_When_RequestBodyIsNull_Then_ReturnsStatus500() {
        // Act
        ResponseEntity<ResponseController<EstimateEmrCostManHoursGetOutput>> response =
                estimateEmrCostManHoursController.getEstimateEmrCostManHours(null);

        // Assert
        assertEquals(500, response.getStatusCode().value());
    }
}

