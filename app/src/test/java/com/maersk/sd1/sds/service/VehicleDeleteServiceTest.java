package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.VehicleDeleteOutput;
import com.maersk.sd1.common.repository.TruckRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehicleDeleteServiceTest {

    @Mock
    private TruckRepository truckRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private VehicleDeleteService vehicleDeleteService;

    private final Integer vehicleId = 12345;
    private final Integer userModificationId = 67890;
    private final Integer languageId = 1;

    @BeforeEach
    void setUp() {
        vehicleDeleteService = new VehicleDeleteService(truckRepository, messageLanguageRepository);
    }

    @Test
    void Given_ValidVehicleId_When_DeleteVehicleIsCalled_Then_TruckIsDeactivatedSuccessfully() {
        // Arrange
        doNothing().when(truckRepository).deactivateTruck(eq(vehicleId), eq(userModificationId), any(LocalDateTime.class));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, languageId)).thenReturn("Success");

        // Act
        VehicleDeleteOutput output = vehicleDeleteService.deleteVehicle(vehicleId, userModificationId, languageId);

        // Assert
        assertEquals(1, output.getResponseStatus());
        assertEquals("Success", output.getResponseMessage());
        verify(truckRepository, times(1)).deactivateTruck(eq(vehicleId), eq(userModificationId), any(LocalDateTime.class));
        verify(messageLanguageRepository, times(1)).fnTranslatedMessage("GENERAL", 7, languageId);
    }

    @Test
    void Given_InvalidVehicleId_When_DeleteVehicleFails_Then_ErrorIsLoggedAndResponseStatusIsZero() {
        // Arrange
        doThrow(new RuntimeException("Vehicle not found"))
                .when(truckRepository).deactivateTruck(eq(vehicleId), eq(userModificationId), any(LocalDateTime.class));

        // Act
        VehicleDeleteOutput output = vehicleDeleteService.deleteVehicle(vehicleId, userModificationId, languageId);

        // Assert
        assertEquals(0, output.getResponseStatus());
        assertEquals("Vehicle not found", output.getResponseMessage());
        verify(truckRepository, times(1)).deactivateTruck(eq(vehicleId), eq(userModificationId), any(LocalDateTime.class));
        verify(messageLanguageRepository, never()).fnTranslatedMessage(any(), anyInt(), anyInt());
    }



    @Test
    void Given_RepositoryThrowsUnexpectedException_When_DeleteVehicleIsCalled_Then_ResponseStatusIsZero() {
        // Arrange
        doThrow(new RuntimeException("Unexpected error"))
                .when(truckRepository).deactivateTruck(anyInt(), anyInt(), any(LocalDateTime.class));

        // Act
        VehicleDeleteOutput output = vehicleDeleteService.deleteVehicle(vehicleId, userModificationId, languageId);

        // Assert
        assertEquals(0, output.getResponseStatus());
        assertEquals("Unexpected error", output.getResponseMessage());
    }
}