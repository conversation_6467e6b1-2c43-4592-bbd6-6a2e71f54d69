package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ActivityLogDisableInput;
import com.maersk.sd1.sds.controller.dto.ActivityLogDisableOutput;
import com.maersk.sd1.sds.service.ActivityLogDisableService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

 class ActivityLogDisableControllerTest {

    @Mock
    private ActivityLogDisableService activityLogDisableService;

    @InjectMocks
    private ActivityLogDisableController activityLogDisableController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
     void testDisableActivityLog_Success() {
        ActivityLogDisableInput.Input input = new ActivityLogDisableInput.Input();
        input.setUserModificationId(123);
        input.setActivityLogId(1234);

        ActivityLogDisableInput.Prefix prefix = new ActivityLogDisableInput.Prefix();
        prefix.setInput(input);

        ActivityLogDisableInput.Root request = new ActivityLogDisableInput.Root();
        request.setPrefix(prefix);

        ActivityLogDisableOutput output = new ActivityLogDisableOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");

        when(activityLogDisableService.disableActivityLog(123, 1234)).thenReturn(output);

        ResponseEntity<ResponseController<ActivityLogDisableOutput>> response = activityLogDisableController.disableActivityLog(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
     void testDisableActivityLog_Exception() {
        ActivityLogDisableInput.Input input = new ActivityLogDisableInput.Input();
        input.setUserModificationId(123);
        input.setActivityLogId(1234);

        ActivityLogDisableInput.Prefix prefix = new ActivityLogDisableInput.Prefix();
        prefix.setInput(input);

        ActivityLogDisableInput.Root request = new ActivityLogDisableInput.Root();
        request.setPrefix(prefix);

        doThrow(new RuntimeException("Error")).when(activityLogDisableService).disableActivityLog(123, 1234);

        ResponseEntity<ResponseController<ActivityLogDisableOutput>> response = activityLogDisableController.disableActivityLog(request);

        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("java.lang.RuntimeException: Error", response.getBody().getResult().getRespMensaje());
    }
}

