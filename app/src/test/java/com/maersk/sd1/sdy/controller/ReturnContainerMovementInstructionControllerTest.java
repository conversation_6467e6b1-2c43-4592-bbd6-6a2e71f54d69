package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.ReturnContainerMovementInstructionInput;
import com.maersk.sd1.sdy.dto.ReturnContainerMovementInstructionOutput;
import com.maersk.sd1.sdy.service.ReturnContainerMovementInstructionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReturnContainerMovementInstructionControllerTest {

    @Mock
    private ReturnContainerMovementInstructionService returnContainerMovementInstructionService;

    @InjectMocks
    private ReturnContainerMovementInstructionController returnContainerMovementInstructionController;

    private ReturnContainerMovementInstructionInput.Root request;

    @BeforeEach
    public void setUp() {
        ReturnContainerMovementInstructionInput.Input input = new ReturnContainerMovementInstructionInput.Input();
        input.setUserId(1);
        input.setMovementInstructionId(100);

        ReturnContainerMovementInstructionInput.Prefix prefix = new ReturnContainerMovementInstructionInput.Prefix();
        prefix.setInput(input);

        request = new ReturnContainerMovementInstructionInput.Root();
        request.setPrefix(prefix);
    }

    @Test
    void testReturnContainerMovementInstruction_success() {
        ReturnContainerMovementInstructionOutput output = new ReturnContainerMovementInstructionOutput();
        output.setResultState(1);
        output.setResultMessage("OK");
        output.setResultSdyLocation("");

        when(returnContainerMovementInstructionService.returnMovementInstruction(any(ReturnContainerMovementInstructionInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<ReturnContainerMovementInstructionOutput>> response = returnContainerMovementInstructionController.returnContainerMovementInstruction(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getResultState());
        assertEquals("OK", response.getBody().getResult().getResultMessage());
    }

    @Test
    void testReturnContainerMovementInstruction_failure() {
        when(returnContainerMovementInstructionService.returnMovementInstruction(any(ReturnContainerMovementInstructionInput.Input.class)))
                .thenThrow(new RuntimeException("Error in returnMovementInstruction"));

        ResponseEntity<ResponseController<ReturnContainerMovementInstructionOutput>> response = returnContainerMovementInstructionController.returnContainerMovementInstruction(request);

        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getResultState());
        assertEquals("Error in returnMovementInstruction", response.getBody().getResult().getResultMessage());
    }
}
