package com.maersk.sd1.sdf.service;

import com.google.gson.JsonParseException;
import com.maersk.sd1.common.model.Parametrization;
import com.maersk.sd1.common.model.Person;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdf.dto.EmrInspectionListInput;
import com.maersk.sd1.sdf.dto.EmrInspectionListOutput;
import com.maersk.sd1.sdf.dto.TableEmrInspectionListDTO;
import com.maersk.sd1.sdf.repository.SdfEirRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class EmrInspectionListServiceTest {

    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private EmrInspectionRepository emrInspectionRepository;
    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;
    @Mock
    private ParametrizationRepository parametrizationRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private SdfEirRepository sdfEirRepository;

    @InjectMocks
    private EmrInspectionListService emrInspectionListService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void emrInspectionList_validInput_returnsExpectedOutput() {
        EmrInspectionListInput.Input input = new EmrInspectionListInput.Input();
        input.setContainer("[{\"value\":\"container1\"}]");
        input.setLanguageId(1);
        input.setSubBusinessUnitLocalId(1);
        input.setEmrInspectionId(1);
        input.setGateInDateMin(LocalDateTime.now().minusDays(1));
        input.setGateInDateMax(LocalDateTime.now());
        input.setInStock(true);
        input.setStatusId(1);
        input.setFilterBox(1);
        input.setPage(1);
        input.setSize(10);
        input.setUserId(1);
        input.setBusinessUnitId(1);

        when(catalogRepository.findIdByAlias(ArgumentMatchers.anyString())).thenReturn(1);

        TableEmrInspectionListDTO tableEmrInspectionListDTO = new  TableEmrInspectionListDTO();
        tableEmrInspectionListDTO.setSizeEquipment("1");
        tableEmrInspectionListDTO.setEmrInspectionId(1);
        tableEmrInspectionListDTO.setEirId(1);
        tableEmrInspectionListDTO.setStatusAlias("status");
        tableEmrInspectionListDTO.setTypeEquipment("1");
        tableEmrInspectionListDTO.setEmrInspectionId(1);
        tableEmrInspectionListDTO.setTruckEntryDate(LocalDateTime.now());
        tableEmrInspectionListDTO.setStatus("1");
        tableEmrInspectionListDTO.setTypeReefer("1");
        tableEmrInspectionListDTO.setWeightUnitType("1");
        tableEmrInspectionListDTO.setOperationType("1");

        when(emrInspectionRepository.findTableEmrInspectionList( anyInt(), anyInt(), anyInt(), anyInt(), any(), any(), anyBoolean(), anyInt(), anyList()))
                .thenReturn(List.of(tableEmrInspectionListDTO));
        when(catalogLanguageRepository.fnCatalogTranslationDescLong(anyInt(), anyInt())).thenReturn("translated");
        Parametrization parametrization = new Parametrization();
        parametrization.setMinutesInspectionATime(1);
        parametrization.setMinutesInspection(1);
        parametrization.setMinutesInspectionWithDelay(1);


        when(parametrizationRepository.findPtiTimesByBusinessUnit(anyLong())).thenReturn(List.of(parametrization));
        when(userRepository.findById(anyInt())).thenReturn(Optional.empty());
        when(sdfEirRepository.countByLocalSubBusinessUnitAndTruckArrivalDatesAndInspectorPerson(anyInt(), any(), any(), anyInt())).thenReturn(1);

        EmrInspectionListOutput result = emrInspectionListService.emrInspectionList(input);

        assertNotNull(result);
        assertNotNull(result.getTlist());
        assertNotNull(result.getInspectionSummaryDTO());
    }
    @Test
    void emrInspectionList_validInput_returnsExpectedOutputFilterBoxNull() {
        EmrInspectionListInput.Input input = new EmrInspectionListInput.Input();
        input.setContainer("[{\"value\":\"container1\"}]");
        input.setLanguageId(1);
        input.setSubBusinessUnitLocalId(1);
        input.setEmrInspectionId(1);
        input.setGateInDateMin(LocalDateTime.now().minusDays(1));
        input.setGateInDateMax(LocalDateTime.now());
        input.setInStock(true);
        input.setStatusId(1);
        input.setPage(1);
        input.setSize(10);
        input.setUserId(1);
        input.setBusinessUnitId(1);

        when(catalogRepository.findIdByAlias(ArgumentMatchers.anyString())).thenReturn(1);

        TableEmrInspectionListDTO tableEmrInspectionListDTO = new  TableEmrInspectionListDTO();
        tableEmrInspectionListDTO.setSizeEquipment("1");
        tableEmrInspectionListDTO.setEmrInspectionId(1);
        tableEmrInspectionListDTO.setEirId(1);
        tableEmrInspectionListDTO.setStatusAlias("status");
        tableEmrInspectionListDTO.setTypeEquipment("1");
        tableEmrInspectionListDTO.setEmrInspectionId(1);
        tableEmrInspectionListDTO.setTruckEntryDate(LocalDateTime.now());
        tableEmrInspectionListDTO.setStatus("1");
        tableEmrInspectionListDTO.setTypeReefer("1");
        tableEmrInspectionListDTO.setWeightUnitType("1");
        tableEmrInspectionListDTO.setOperationType("1");

        when(emrInspectionRepository.findTableEmrInspectionList( anyInt(), anyInt(), anyInt(), anyInt(), any(), any(), anyBoolean(), anyInt(), anyList()))
                .thenReturn(List.of(tableEmrInspectionListDTO));
        when(catalogLanguageRepository.fnCatalogTranslationDescLong(anyInt(), anyInt())).thenReturn("translated");
        Parametrization parametrization = new Parametrization();
        parametrization.setMinutesInspectionATime(1);
        parametrization.setMinutesInspection(1);
        parametrization.setMinutesInspectionWithDelay(1);


        when(parametrizationRepository.findPtiTimesByBusinessUnit(anyLong())).thenReturn(List.of(parametrization));
        when(userRepository.findById(anyInt())).thenReturn(Optional.empty());
        when(sdfEirRepository.countByLocalSubBusinessUnitAndTruckArrivalDatesAndInspectorPerson(anyInt(), any(), any(), anyInt())).thenReturn(1);

        EmrInspectionListOutput result = emrInspectionListService.emrInspectionList(input);

        assertNotNull(result);
        assertNotNull(result.getTlist());
        assertNotNull(result.getInspectionSummaryDTO());
    }

    @Test
    void emrInspectionList_nullContainer_returnsEmptyList() {
        EmrInspectionListInput.Input input = new EmrInspectionListInput.Input();
        input.setContainer(null);
        input.setLanguageId(1);
        input.setSubBusinessUnitLocalId(1);
        input.setEmrInspectionId(1);
        input.setGateInDateMin(LocalDateTime.now().minusDays(1));
        input.setGateInDateMax(LocalDateTime.now());
        input.setInStock(true);
        input.setStatusId(1);
        input.setFilterBox(1);
        input.setPage(1);
        input.setSize(10);
        input.setUserId(1);
        input.setBusinessUnitId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        when(emrInspectionRepository.findTableEmrInspectionList( anyInt(), anyInt(), anyInt(), anyInt(), any(), any(), anyBoolean(), anyInt(), anyList()))
                .thenReturn(new ArrayList<>());
        when(catalogLanguageRepository.fnCatalogTranslationDescLong(anyInt(), anyInt())).thenReturn("translated");
        when(parametrizationRepository.findPtiTimesByBusinessUnit(anyLong())).thenReturn(List.of(new Parametrization()));
        when(userRepository.findById(anyInt())).thenReturn(Optional.empty());
        when(sdfEirRepository.countByLocalSubBusinessUnitAndTruckArrivalDatesAndInspectorPerson(anyInt(), any(), any(), anyInt())).thenReturn(1);

        EmrInspectionListOutput result = emrInspectionListService.emrInspectionList(input);

        assertNotNull(result);
        assertTrue(result.getTlist().isEmpty());
    }

    @Test
    void emrInspectionList_invalidJsonContainer_throwsJsonParseException() {
        EmrInspectionListInput.Input input = new EmrInspectionListInput.Input();
        input.setContainer("invalid json");
        input.setLanguageId(1);
        input.setSubBusinessUnitLocalId(1);
        input.setEmrInspectionId(1);
        input.setGateInDateMin(LocalDateTime.now().minusDays(1));
        input.setGateInDateMax(LocalDateTime.now());
        input.setInStock(true);
        input.setStatusId(1);
        input.setFilterBox(1);
        input.setPage(1);
        input.setSize(10);
        input.setUserId(1);
        input.setBusinessUnitId(1);

        assertThrows(JsonParseException.class, () -> emrInspectionListService.emrInspectionList(input));
    }

    @Test
    void emrInspectionList_emptyContainer_returnsEmptyList() {
        EmrInspectionListInput.Input input = new EmrInspectionListInput.Input();
        input.setContainer("[]");
        input.setLanguageId(1);
        input.setSubBusinessUnitLocalId(1);
        input.setEmrInspectionId(1);
        input.setGateInDateMin(LocalDateTime.now().minusDays(1));
        input.setGateInDateMax(LocalDateTime.now());
        input.setInStock(true);
        input.setStatusId(1);
        input.setFilterBox(1);
        input.setPage(1);
        input.setSize(10);
        input.setUserId(1);
        input.setBusinessUnitId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        when(emrInspectionRepository.findTableEmrInspectionList( anyInt(), anyInt(), anyInt(), anyInt(), any(), any(), anyBoolean(), anyInt(), anyList()))
                .thenReturn(new ArrayList<>());
        when(catalogLanguageRepository.fnCatalogTranslationDescLong(anyInt(), anyInt())).thenReturn("translated");
        when(parametrizationRepository.findPtiTimesByBusinessUnit(anyLong())).thenReturn(List.of(new Parametrization()));
        User user = new User();
        Person person = new Person();
        user.setPerson(person);

        when(userRepository.findById(anyInt())).thenReturn(Optional.of(user));
        when(sdfEirRepository.countByLocalSubBusinessUnitAndTruckArrivalDatesAndInspectorPerson(anyInt(), any(), any(), anyInt())).thenReturn(1);

        EmrInspectionListOutput result = emrInspectionListService.emrInspectionList(input);

        assertNotNull(result);
        assertTrue(result.getTlist().isEmpty());
    }
}