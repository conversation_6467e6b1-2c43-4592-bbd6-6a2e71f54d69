package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.EstimateEmrCostManHoursListInput;
import com.maersk.sd1.sde.controller.dto.EstimateEmrCostManHoursListOutput;
import com.maersk.sd1.sde.service.EstimateEmrCostManHoursListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EstimateEmrCostManHoursListControllerTest {

    @Mock
    private EstimateEmrCostManHoursListService estimateEmrCostManHoursListService;

    @InjectMocks
    private EstimateEmrCostManHoursListController estimateEmrCostManHoursListController;

    private EstimateEmrCostManHoursListInput.Root estimateEmrCostManHoursListRequest;

    @BeforeEach
    public void setUp() {
        EstimateEmrCostManHoursListInput.Input input = new EstimateEmrCostManHoursListInput.Input();
        EstimateEmrCostManHoursListInput.Prefix prefix = new EstimateEmrCostManHoursListInput.Prefix();
        prefix.setInput(input);
        estimateEmrCostManHoursListRequest = new EstimateEmrCostManHoursListInput.Root();
        estimateEmrCostManHoursListRequest.setPrefix(prefix);
    }

    @Test
    void given_ValidRequest_When_ListIsCalled_Then_ReturnsStatus200() {
        EstimateEmrCostManHoursListOutput output = new EstimateEmrCostManHoursListOutput();
        output.setTotalRegistros(List.of(List.of(5L)));
        output.setData(new ArrayList<>());

        when(estimateEmrCostManHoursListService.estimateEmrCost(any(EstimateEmrCostManHoursListInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<EstimateEmrCostManHoursListOutput>> response = estimateEmrCostManHoursListController.list(estimateEmrCostManHoursListRequest);

        assertEquals(200, response.getStatusCode().value());
    }

    @Test
    void given_ServiceThrowsException_When_ListIsCalled_Then_ReturnsStatus500() {
        when(estimateEmrCostManHoursListService.estimateEmrCost(any(EstimateEmrCostManHoursListInput.Input.class)))
                .thenThrow(new RuntimeException("Test exception"));

        ResponseEntity<ResponseController<EstimateEmrCostManHoursListOutput>> response = estimateEmrCostManHoursListController.list(estimateEmrCostManHoursListRequest);

        assertEquals(500, response.getStatusCode().value());
    }

    @Test
    void given_EmptyRequest_When_ListIsCalled_Then_ReturnsValidResponse() {
        EstimateEmrCostManHoursListOutput output = new EstimateEmrCostManHoursListOutput();
        output.setTotalRegistros(List.of(List.of(0L)));
        output.setData(new ArrayList<>());

        when(estimateEmrCostManHoursListService.estimateEmrCost(any(EstimateEmrCostManHoursListInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<EstimateEmrCostManHoursListOutput>> response = estimateEmrCostManHoursListController.list(estimateEmrCostManHoursListRequest);

        assertEquals(200, response.getStatusCode().value());
    }
}