package com.maersk.sd1.sde.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.EstimateAutoApprovalConfigEditInput;
import com.maersk.sd1.sde.dto.EstimateAutoApprovalConfigEditOutput;
import com.maersk.sd1.sde.service.EstimateAutoApprovalConfigEditService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class EstimateAutoApprovalConfigEditControllerTest {

    @InjectMocks
    private EstimateAutoApprovalConfigEditController controller;

    @Mock
    private EstimateAutoApprovalConfigEditService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void whenValidRequest_thenReturnsOkResponse() {
        EstimateAutoApprovalConfigEditOutput serviceOutput = new EstimateAutoApprovalConfigEditOutput();
        serviceOutput.setRespEstado(1);
        serviceOutput.setRespMensaje("Success");

        EstimateAutoApprovalConfigEditInput.Input input = new EstimateAutoApprovalConfigEditInput.Input();
        input.setEstimateAutoApprovalConfigId(1);
        input.setLanguageId(1);

        EstimateAutoApprovalConfigEditInput.Prefix prefix = new EstimateAutoApprovalConfigEditInput.Prefix();
        prefix.setInput(input);
        EstimateAutoApprovalConfigEditInput.Root request = new EstimateAutoApprovalConfigEditInput.Root();
        request.setPrefix(prefix);

        when(service.editEstimateAutoApprovalConfig(any(EstimateAutoApprovalConfigEditInput.Input.class)))
                .thenReturn(serviceOutput);

        ResponseEntity<ResponseController<EstimateAutoApprovalConfigEditOutput>> response =
                controller.editEstimateAutoApprovalConfig(request);

        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        verify(service).editEstimateAutoApprovalConfig(input);
    }

    @Test
    void whenServiceThrowsException_thenReturnsInternalServerErrorResponse() {
        EstimateAutoApprovalConfigEditInput.Input input = new EstimateAutoApprovalConfigEditInput.Input();
        input.setEstimateAutoApprovalConfigId(2);
        input.setLanguageId(2);

        EstimateAutoApprovalConfigEditInput.Prefix prefix = new EstimateAutoApprovalConfigEditInput.Prefix();
        prefix.setInput(input);
        EstimateAutoApprovalConfigEditInput.Root request = new EstimateAutoApprovalConfigEditInput.Root();
        request.setPrefix(prefix);

        RuntimeException exception = new RuntimeException("Service error");

        when(service.editEstimateAutoApprovalConfig(any(EstimateAutoApprovalConfigEditInput.Input.class)))
                .thenThrow(exception);

        ResponseEntity<ResponseController<EstimateAutoApprovalConfigEditOutput>> response =
                controller.editEstimateAutoApprovalConfig(request);

        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertTrue(response.getBody().getResult().getRespMensaje().contains("Service error"));
        verify(service).editEstimateAutoApprovalConfig(input);
    }

    @Test
    void testEditEstimateAutoApprovalConfigSuccess() {
        EstimateAutoApprovalConfigEditInput.Root request = new EstimateAutoApprovalConfigEditInput.Root();
        EstimateAutoApprovalConfigEditInput.Input input = new EstimateAutoApprovalConfigEditInput.Input();
        request.setPrefix(new EstimateAutoApprovalConfigEditInput.Prefix());
        request.getPrefix().setInput(input);

        EstimateAutoApprovalConfigEditOutput output = new EstimateAutoApprovalConfigEditOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");

        when(service.editEstimateAutoApprovalConfig(input)).thenReturn(output);

        ResponseEntity<ResponseController<EstimateAutoApprovalConfigEditOutput>> response = controller.editEstimateAutoApprovalConfig(request);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
        verify(service, times(1)).editEstimateAutoApprovalConfig(input);
    }

    @Test
    void testEditEstimateAutoApprovalConfigInvalidRequest() {
        EstimateAutoApprovalConfigEditInput.Root request = new EstimateAutoApprovalConfigEditInput.Root();

        ResponseEntity<ResponseController<EstimateAutoApprovalConfigEditOutput>> response = controller.editEstimateAutoApprovalConfig(request);

        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Invalid request", response.getBody().getResult().getRespMensaje());
        verifyNoInteractions(service);
    }

    @Test
    void testEditEstimateAutoApprovalConfigServiceException() {
        EstimateAutoApprovalConfigEditInput.Root request = new EstimateAutoApprovalConfigEditInput.Root();
        EstimateAutoApprovalConfigEditInput.Input input = new EstimateAutoApprovalConfigEditInput.Input();
        request.setPrefix(new EstimateAutoApprovalConfigEditInput.Prefix());
        request.getPrefix().setInput(input);

        when(service.editEstimateAutoApprovalConfig(input)).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<EstimateAutoApprovalConfigEditOutput>> response = controller.editEstimateAutoApprovalConfig(request);

        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Service error", response.getBody().getResult().getRespMensaje());
        verify(service, times(1)).editEstimateAutoApprovalConfig(input);
    }
}