package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.BookingBlockCancellationReleaseEditInput;
import com.maersk.sd1.sde.dto.BookingBlockCancellationReleaseEditOutput;
import com.maersk.sd1.sde.service.BookingBlockCancellationReleaseEditService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class BookingBlockCancellationReleaseEditControllerTest {

    @Mock
    private BookingBlockCancellationReleaseEditService service;

    @InjectMocks
    private BookingBlockCancellationReleaseEditController controller;

    private BookingBlockCancellationReleaseEditInput.Root request;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new BookingBlockCancellationReleaseEditInput.Root();
        BookingBlockCancellationReleaseEditInput.Prefix prefix = new BookingBlockCancellationReleaseEditInput.Prefix();
        BookingBlockCancellationReleaseEditInput.Input input = new BookingBlockCancellationReleaseEditInput.Input();
        input.setCancelBloqueoBookingId(1);
        input.setComentario("Test Comment");
        input.setUsuarioModificacionId(1);
        input.setIdiomaId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void testReleaseEdit_Success() {
        BookingBlockCancellationReleaseEditOutput output = new BookingBlockCancellationReleaseEditOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success Message");

        when(service.releaseEdit(any(BookingBlockCancellationReleaseEditInput.Input.class))).thenReturn(output);

        ResponseEntity<ResponseController<BookingBlockCancellationReleaseEditOutput>> response = controller.releaseEdit(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success Message", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testReleaseEdit_Exception() {
        when(service.releaseEdit(any(BookingBlockCancellationReleaseEditInput.Input.class))).thenThrow(new RuntimeException("Service Error"));

        ResponseEntity<ResponseController<BookingBlockCancellationReleaseEditOutput>> response = controller.releaseEdit(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Service Error", response.getBody().getResult().getRespMensaje());
    }
}