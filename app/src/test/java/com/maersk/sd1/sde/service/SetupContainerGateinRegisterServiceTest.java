package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.GateTransmissionEmptyActivitySettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionLocalSettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionSettingRepository;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterInput;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SetupContainerGateinRegisterServiceTest {

    @Mock
    private GateTransmissionSettingRepository gateTransmissionSettingRepository;

    @Mock
    private GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;

    @Mock
    private GateTransmissionEmptyActivitySettingRepository gateTransmissionEmptyActivitySettingRepository;

    @InjectMocks
    private SetupContainerGateinRegisterService seteoEdiCodecoRegisterService;

    private SetupContainerGateinRegisterInput.Input input;

    @BeforeEach
    void setUp() {
        input = new SetupContainerGateinRegisterInput.Input();
        input.setUnidadNegocioId(1L);
        input.setSubUnidadNegocioId(1L);
        input.setLineaNavieraId(4105);
        input.setSistemaEntrega("SistemaY");
        input.setInfoSistemaEntrega("Updated delivery system info");
        input.setIdentificadorReceptor("REC456");
        input.setEnviarGateInEmpty(false);
        input.setEnviarGateOutEmpty(true);
        input.setEnviarGateInFull(false);
        input.setEnviarGateOutFull(true);
        input.setEnviarStatusActivity(false);
        input.setCatFormatoGateOutEmpty(1L);
        input.setCatFormatoGateInFull(1L);
        input.setCatFormatoGateOutFull(1L);
        input.setCatFormatoGateInEmpty(1L);
        input.setCatFormatoStatusActivity(1L);
        input.setCatCanalEnvioId(1L);
        input.setCatModoGenerarArchivoId(1L);
        input.setCorreoCodecoDestino("<EMAIL>");
        input.setCorreoTelexDestino("<EMAIL>");
        input.setParametro1("Q1");
        input.setParametro2("Q2");
        input.setParametro3("Q3");
        input.setParametro4("Q4");
        input.setEsHistorico(true);
        input.setFechaDebaja("2026-06-30");
        input.setMotivoDebaja("Updated reason for deactivation");
        input.setActivo(false);
        input.setUsuarioRegistroId(2L);
        input.setParametro5("Q5");
        input.setParametro6("Q6");
        input.setAzureIdCodeco("AZURE_CODECO_789");
        input.setAzureIdTelex("AZURE_TELEX_987");
        input.setSftpId("SFTP_654");
        input.setExtensionArchivoEnviar(".json");
        input.setMinutosTranscurridos(45);
        input.setSubUnidadesJson(Arrays.asList(
                Map.of("subUnidad", "updatedExample1"),
                Map.of("subUnidad", "updatedExample2")
        ));
        input.setGateInEmptyProcedenciaJson(Collections.singletonList(
                Map.of("catalogo_id", 1)
        ));
        input.setGateOutEmptyProcedenciaJson(Collections.singletonList(
                Map.of("catalogo_id", 2)
        ));
    }

    @Test
    void Given_ExistingGateTransmissionSetting_When_RegisterCodecoSetting_Then_ReturnError() {
        when(gateTransmissionSettingRepository.findByUniqueKeys(anyLong(), anyLong(), anyInt(), anyString()))
                .thenReturn(Optional.of(new GateTransmissionSetting()));

        SetupContainerGateinRegisterOutput output = seteoEdiCodecoRegisterService.registerCodecoSetting(input);

        assertEquals(2, output.getRespEstado());
        assertEquals("There is another codeco with the same key (Business Unit, Sub Business Unit, Shipping Line and Delivery System)", output.getRespMensaje());
        assertNull(output.getRespNewId());
    }

    @Test
    void Given_ValidInput_When_RegisterCodecoSetting_Then_SuccessfullyRegister() {
        when(gateTransmissionSettingRepository.findByUniqueKeys(anyLong(), anyLong(), anyInt(), anyString()))
                .thenReturn(Optional.empty());

        SetupContainerGateinRegisterOutput output = seteoEdiCodecoRegisterService.registerCodecoSetting(input);

        assertEquals(0, output.getRespEstado());
        assertNull(output.getRespNewId());
    }

    @Test
    void Given_ValidInput_When_RegisterCodecoSetting_Then_SaveGateTransmissionLocalSetting() {
        when(gateTransmissionSettingRepository.findByUniqueKeys(anyLong(), anyLong(), anyInt(), anyString()))
                .thenReturn(Optional.empty());

        SetupContainerGateinRegisterOutput output = seteoEdiCodecoRegisterService.registerCodecoSetting(input);

        assertEquals(0, output.getRespEstado());
    }

    @Test
    void Given_ValidInput_When_RegisterCodecoSetting_Then_SaveGateTransmissionEmptyActivitySetting() {
        when(gateTransmissionSettingRepository.findByUniqueKeys(anyLong(), anyLong(), anyInt(), anyString()))
                .thenReturn(Optional.empty());

        SetupContainerGateinRegisterOutput output = seteoEdiCodecoRegisterService.registerCodecoSetting(input);

        assertNull(output.getRespNewId());
    }

    @Test
    void Given_Exception_When_RegisterCodecoSetting_Then_ReturnError() {
        when(gateTransmissionSettingRepository.findByUniqueKeys(anyLong(), anyLong(), anyInt(), anyString()))
                .thenThrow(new RuntimeException("Database error"));

        SetupContainerGateinRegisterOutput output = seteoEdiCodecoRegisterService.registerCodecoSetting(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
        assertNull(output.getRespNewId());
    }

    @Test
    void Given_ValidInput_When_GetGateInEmptyProcedenciaJson_Then_SaveGateTransmissionEmptyActivitySetting() {
        SetupContainerGateinRegisterInput.Input testInput = new SetupContainerGateinRegisterInput.Input();
        testInput.setGateInEmptyProcedenciaJson(Collections.singletonList(
                Map.of("catalogo_id", 1)
        ));
        GateTransmissionSetting setting = new GateTransmissionSetting();
        User regUser = new User();

        seteoEdiCodecoRegisterService.getGateInEmptyProcedenciaJson(testInput, setting, regUser);

        verify(gateTransmissionEmptyActivitySettingRepository, times(1)).save(any(GateTransmissionEmptyActivitySetting.class));
    }

    @Test
    void Given_ValidInput_When_GetGateOutEmptyProcedenciaJson_Then_SaveGateTransmissionEmptyActivitySetting() {
        SetupContainerGateinRegisterInput.Input testInput = new SetupContainerGateinRegisterInput.Input();
        testInput.setGateOutEmptyProcedenciaJson(Collections.singletonList(
                Map.of("catalogo_id", 2)
        ));
        GateTransmissionSetting setting = new GateTransmissionSetting();
        User regUser = new User();

        seteoEdiCodecoRegisterService.getGateOutEmptyProcedenciaJson(testInput, setting, regUser);

        verify(gateTransmissionEmptyActivitySettingRepository, times(1)).save(any(GateTransmissionEmptyActivitySetting.class));
    }

    @Test
    void Given_NullSubUnidadesJson_When_GetSubUnidadesJson_Then_DoNotSaveGateTransmissionLocalSetting() {
        SetupContainerGateinRegisterInput.Input testInput = new SetupContainerGateinRegisterInput.Input();
        testInput.setSubUnidadesJson(null);
        GateTransmissionSetting setting = new GateTransmissionSetting();
        User regUser = new User();

        seteoEdiCodecoRegisterService.getSubUnidadesJson(testInput, setting, regUser);

        verify(gateTransmissionLocalSettingRepository, never()).save(any(GateTransmissionLocalSetting.class));
    }

    @Test
    void Given_EmptyGateInEmptyProcedenciaJson_When_GetGateInEmptyProcedenciaJson_Then_DoNotSaveGateTransmissionEmptyActivitySetting() {
        SetupContainerGateinRegisterInput.Input testInput = new SetupContainerGateinRegisterInput.Input();
        testInput.setGateInEmptyProcedenciaJson(Collections.emptyList());
        GateTransmissionSetting setting = new GateTransmissionSetting();
        User regUser = new User();

        seteoEdiCodecoRegisterService.getGateInEmptyProcedenciaJson(testInput, setting, regUser);

        verify(gateTransmissionEmptyActivitySettingRepository, never()).save(any(GateTransmissionEmptyActivitySetting.class));
    }

    @Test
    void Given_NullGateOutEmptyProcedenciaJson_When_GetGateOutEmptyProcedenciaJson_Then_DoNotSaveGateTransmissionEmptyActivitySetting() {
        SetupContainerGateinRegisterInput.Input testInput = new SetupContainerGateinRegisterInput.Input();
        testInput.setGateOutEmptyProcedenciaJson(null);
        GateTransmissionSetting setting = new GateTransmissionSetting();
        User regUser = new User();

        seteoEdiCodecoRegisterService.getGateOutEmptyProcedenciaJson(testInput, setting, regUser);

        verify(gateTransmissionEmptyActivitySettingRepository, never()).save(any(GateTransmissionEmptyActivitySetting.class));
    }
}