package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.InspectionGatePhotoRepository;
import com.maersk.sd1.common.repository.InspectionGateRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.dto.EirListInspectionsOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EirListInspectionsServiceTest {

    @Mock
    private InspectionGateRepository inspectionGateRepository;

    @Mock
    private InspectionGatePhotoRepository inspectionGatePhotoRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private EirListInspectionsService eirListInspectionsService;

    private InspectionGate mockInspectionGate;
    private InspectionGatePhoto mockPhoto;

    @BeforeEach
    void setup() {
        mockInspectionGate = new InspectionGate();
        mockInspectionGate.setId(1);
        mockInspectionGate.setInspectionDate(LocalDateTime.now());
        mockInspectionGate.setAssignmentDate(LocalDateTime.now().minusDays(1));
        mockInspectionGate.setInspectionObservation("Sample Observation");
        mockInspectionGate.setInspectionControl((short) 1);
        mockInspectionGate.setApprovedInspection("1");

        Eir mockEir = new Eir();
        mockEir.setId(100);
        mockInspectionGate.setEir(mockEir);

        User inspector = new User();
        inspector.setId(200);
        inspector.setNames("John");
        inspector.setFirstLastName("Doe");
        mockInspectionGate.setAssignmentUser(inspector);

        mockPhoto = new InspectionGatePhoto();
        mockPhoto.setId(1);
        mockPhoto.setInspeccionGate(mockInspectionGate);

        Attachment mockAttachment = new Attachment();
        mockAttachment.setId(10);
        mockAttachment.setId1("guid-1234");
        mockAttachment.setUrl("http://mockurl.com/photo.jpg");
        mockPhoto.setAttachment(mockAttachment);
    }

    @Test
    void testListInspectionsSuccess() {
        when(inspectionGateRepository.findFirstSet(anyInt())).thenReturn(List.of(mockInspectionGate));
        when(inspectionGateRepository.findSecondSet(anyInt())).thenReturn(List.of());
        when(inspectionGatePhotoRepository.findPhotosByInspectionGateIds(anyList())).thenReturn(List.of(mockPhoto));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Mocked Message");

        EirListInspectionsOutputDTO result = eirListInspectionsService.listInspections(100, 1);

        assertNotNull(result);
        assertEquals(1, result.getInspections().size());
        assertEquals(100, result.getInspections().getFirst().getEirId());
        assertEquals("Mocked Message", result.getInspections().getFirst().getInspectionResult());
        assertEquals(1, result.getInspectionPhotos().size());
        assertEquals("http://mockurl.com/photo.jpg", result.getInspectionPhotos().getFirst().getAttachmentUrl());

        verify(inspectionGateRepository, times(1)).findFirstSet(100);
        verify(inspectionGateRepository, times(1)).findSecondSet(100);
        verify(inspectionGatePhotoRepository, times(1)).findPhotosByInspectionGateIds(anyList());
        verify(messageLanguageRepository, atLeastOnce()).fnTranslatedMessage(anyString(), anyInt(), anyInt());
    }

    @Test
    void testListInspectionsNoDataFound() {
        when(inspectionGateRepository.findFirstSet(anyInt())).thenReturn(List.of());
        when(inspectionGateRepository.findSecondSet(anyInt())).thenReturn(List.of());

        EirListInspectionsOutputDTO result = eirListInspectionsService.listInspections(200, 1);

        assertNotNull(result);
        assertTrue(result.getInspections().isEmpty());
        assertTrue(result.getInspectionPhotos().isEmpty());

        verify(inspectionGateRepository, times(1)).findFirstSet(200);
        verify(inspectionGateRepository, times(1)).findSecondSet(200);

        verify(inspectionGatePhotoRepository, never()).findPhotosByInspectionGateIds(anyList());
    }


    @Test
    void testListInspectionsPhotosAvailable() {
        when(inspectionGateRepository.findFirstSet(anyInt())).thenReturn(List.of(mockInspectionGate));
        when(inspectionGateRepository.findSecondSet(anyInt())).thenReturn(List.of());
        when(inspectionGatePhotoRepository.findPhotosByInspectionGateIds(anyList())).thenReturn(List.of(mockPhoto));

        EirListInspectionsOutputDTO result = eirListInspectionsService.listInspections(100, 1);

        assertFalse(result.getInspectionPhotos().isEmpty());
        assertEquals(1, result.getInspectionPhotos().size());
        assertEquals(10, result.getInspectionPhotos().getFirst().getAttachmentId());
        verify(inspectionGatePhotoRepository, times(1)).findPhotosByInspectionGateIds(anyList());
    }

    @Test
    void testListInspectionsExceptionHandling() {
        when(inspectionGateRepository.findFirstSet(anyInt())).thenThrow(new RuntimeException("Database Error"));

        Exception exception = assertThrows(RuntimeException.class, () -> {
            eirListInspectionsService.listInspections(100, 1);
        });

        assertEquals("Database Error", exception.getMessage());

        verify(inspectionGateRepository, times(1)).findFirstSet(100);
    }
}
