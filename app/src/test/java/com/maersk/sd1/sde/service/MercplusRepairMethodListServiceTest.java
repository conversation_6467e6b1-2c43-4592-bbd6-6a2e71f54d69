package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.MercplusRepairMethodListOutput;
import com.maersk.sd1.common.repository.CatalogRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.List;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class MercplusRepairMethodListServiceTest {

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private MercplusRepairMethodListService mercplusRepairMethodListService;

    @BeforeEach
    void setUp() {
        //setup() if needed

    }

    @Test
    void testGetRepairMethodList_Success() {
        // Arrange: Create a mock list of repair methods
        MercplusRepairMethodListOutput repairMethod1 = new MercplusRepairMethodListOutput(); // Set appropriate fields
        MercplusRepairMethodListOutput repairMethod2 = new MercplusRepairMethodListOutput(); // Set appropriate fields
        List<MercplusRepairMethodListOutput> mockRepairMethods = List.of(repairMethod1, repairMethod2);

        // Stub the repository call
        when(catalogRepository.findAllRepairMethods()).thenReturn(mockRepairMethods);

        // Act: Call the method under test
        List<MercplusRepairMethodListOutput> result = mercplusRepairMethodListService.getRepairMethodList();

        // Assert: Verify the result and the interactions
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(catalogRepository, times(1)).findAllRepairMethods();
    }

    @Test
    void testGetRepairMethodList_EmptyList() {
        // Arrange: Stub the repository to return an empty list
        when(catalogRepository.findAllRepairMethods()).thenReturn(List.of());

        // Act: Call the method under test
        List<MercplusRepairMethodListOutput> result = mercplusRepairMethodListService.getRepairMethodList();

        // Assert: Verify the result
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(catalogRepository, times(1)).findAllRepairMethods();
    }

    @Test
    void testGetRepairMethodList_Exception() {
        // Arrange: Stub the repository to throw an exception
        when(catalogRepository.findAllRepairMethods()).thenThrow(new RuntimeException("Database error"));

        // Act & Assert: Expect the method to throw a RuntimeException
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mercplusRepairMethodListService.getRepairMethodList();
        });

        assertEquals("Error retrieving repair methods: Database error", exception.getMessage());
        verify(catalogRepository, times(1)).findAllRepairMethods();
    }
}
