package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.CatalogTableInfoController;
import com.maersk.sd1.sde.dto.CatalogTableRequestInput;
import com.maersk.sd1.sde.dto.CatalogTableResponseOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CatalogTableInfoControllerTest {

    @Mock
    private CatalogTableInfoService catalogTableInfoService;

    @InjectMocks
    private CatalogTableInfoController catalogTableInfoController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void GivenValidRequest_WhenGetCatalogTablesIsCalled_ThenReturnSuccessResponse() {
        CatalogTableRequestInput.Root request = new CatalogTableRequestInput.Root();
        CatalogTableRequestInput.Input input = new CatalogTableRequestInput.Input();
        input.setBusinessUnitId(BigDecimal.valueOf(1));
        input.setSubBusinessUnitId(BigDecimal.valueOf(2));
        input.setLanguageId(1);

        CatalogTableRequestInput.Prefix prefix = new CatalogTableRequestInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        CatalogTableResponseOutput responseOutput = new CatalogTableResponseOutput();

        when(catalogTableInfoService.getCatalogTables(any())).thenReturn(responseOutput);

        ResponseEntity<ResponseController<CatalogTableResponseOutput>> response = catalogTableInfoController.getCatalogTables(request);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
    }

    @Test
    void GivenExceptionInService_WhenGetCatalogTablesIsCalled_ThenReturnErrorResponse() {
        CatalogTableRequestInput.Root request = new CatalogTableRequestInput.Root();
        CatalogTableRequestInput.Input input = new CatalogTableRequestInput.Input();
        input.setBusinessUnitId(BigDecimal.valueOf(1));
        input.setSubBusinessUnitId(BigDecimal.valueOf(2));
        input.setLanguageId(1);

        CatalogTableRequestInput.Prefix prefix = new CatalogTableRequestInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(catalogTableInfoService.getCatalogTables(any())).thenThrow(new RuntimeException("Test Exception"));

        ResponseEntity<ResponseController<CatalogTableResponseOutput>> response = catalogTableInfoController.getCatalogTables(request);

        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());

    }
}
