package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sde.controller.dto.EstimateEmrCostManHoursListInput;
import com.maersk.sd1.sde.controller.dto.EstimateEmrCostManHoursListOutput;
import com.maersk.sd1.common.repository.EstimateEmrCostManHoursRepository;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.EstimateEmrCostManHours;
import com.maersk.sd1.common.repository.CatalogRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class EstimateEmrCostManHoursListServiceTest {

    @Mock
    EstimateEmrCostManHoursRepository estimateEmrCostManHoursRepository;

    @Mock
    CatalogRepository catalogRepository;

    @InjectMocks
    EstimateEmrCostManHoursListService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidInput_When_List_Then_ReturnItems() {
        // Given
        var input = new EstimateEmrCostManHoursListInput.Input();
        input.setPage(1);
        input.setSize(10);
        input.setLanguageId(1);

        User registrationUser = User.builder()
                .id(3001)
                .names("John")
                .firstLastName("Doe")
                .secondLastName("Smith")
                .build();

        User modificationUser = User.builder()
                .id(3002)
                .names("Jane")
                .firstLastName("Brown")
                .secondLastName("Williams")
                .build();

        EstimateEmrCostManHours entity = EstimateEmrCostManHours.builder()
                .id(1001)
                .businessUnit(BusinessUnit.builder().id(2001).name("BU Test").build())
                .costManHour(500)
                .active(true)
                .registrationDate(LocalDateTime.now())
                .registrationUser(registrationUser)
                .modificationUser(modificationUser)
                .build();
        Page<EstimateEmrCostManHours> page = new PageImpl<>(List.of(entity),
                PageRequest.of(0, 10, Sort.by("id").descending()), 1);

        when(estimateEmrCostManHoursRepository.findByFilters(
                any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(),
                any(), any(), any(), any(PageRequest.class) // Ensure 15 parameters
        )).thenReturn(page);

        // When
        EstimateEmrCostManHoursListOutput result = service.estimateEmrCost(input);

        // Then
        assertEquals(1, result.getTotalRegistros().get(0).get(0));
        assertEquals(1, result.getData().size());
    }

    @Test
    void given_RepositoryThrowsException_When_List_Then_ReturnError() {
        // Given
        var input = new EstimateEmrCostManHoursListInput.Input();
        input.setPage(1);
        input.setSize(10);
        input.setLanguageId(1);

        when(estimateEmrCostManHoursRepository.findByFilters(
                any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(),
                any(), any(), any(), any(PageRequest.class) // Ensure 15 parameters
        )).thenThrow(new RuntimeException("DB error"));

        // When
        EstimateEmrCostManHoursListOutput result = service.estimateEmrCost(input);

        // Then
        assertEquals(0L, result.getTotalRegistros().get(0).get(0));
        assertEquals(Collections.emptyList(), result.getData());
    }
}