package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.EirZone;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.RepairManagementSaveInput;
import com.maersk.sd1.sde.dto.RepairManagementSaveOutput;
import com.maersk.sd1.sde.repository.SdeEirRepository;
import org.json.JSONException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class RepairManagementSaveServiceTest {

    @Mock
    private VesselProgrammingRepository vesselProgrammingRepository;
    @Mock
    private MessageLanguageRepository messageLanguageRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private BookingDetailRepository bookingDetailRepository;
    @Mock
    private BookingRepository bookingRepository;
    @Mock
    private CargoDocumentRepository cargoDocumentRepository;
    @Mock
    private EirRepository eirRepository;
    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;
    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    @Mock
    private SdeEirRepository sdeEirRepository;
    @Mock
    private EirZoneRepository eirZoneRepository;
    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;
    @Mock
    private EstimateEmrRepository estimateEmrRepository;

    @InjectMocks
    private RepairManagementSaveService repairManagementSaveService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void documentationEmptyList_validInput_returnsOutput() throws JsonProcessingException {
        RepairManagementSaveInput.Input input = new RepairManagementSaveInput.Input();
        input.setApprovalList("[{\"eir_id\":1,\"opcion_seleccionada\":1,\"siguiente_zona\":\"1\",\"obs_aprobacion\":\"obs\"}]");
        input.setRepairTypeId(48030);
        input.setRegistrationUserId(1);
        input.setLanguageId(1);


        when(catalogRepository.findIdByAlias(Parameter.IS_REPAIR_STRUCTURE)).thenReturn(48030);
        Catalog catalog = new Catalog();
        catalog.setId(1);
        catalog.setDescription("REP");
        when(catalogRepository.findById(any())).thenReturn(Optional.of(catalog));
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        Eir eir = new Eir();
        eir.setId(1);
        Container container = new Container();
        container.setId(1);
        eir.setContainer(container);
        when(sdeEirRepository.findById(anyInt())).thenReturn(Optional.of(eir));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Message {ID}");
        EirZone eirZone = new EirZone();
        eirZone.setId(1);
        when(eirZoneRepository.save(any())).thenReturn(eirZone);
        RepairManagementSaveOutput output = repairManagementSaveService.repairManagementSave(input);

        assertNotNull(output);
        assertEquals(1, output.getRespState());
        assertTrue(output.getRespMessage().contains("Message"));
    }

    @Test
    void documentationEmptyList_validInput_returnsOutput_input_machinery() throws JsonProcessingException {
        RepairManagementSaveInput.Input input = new RepairManagementSaveInput.Input();
        input.setApprovalList("[{\"eir_id\":1,\"opcion_seleccionada\":1,\"siguiente_zona\":\"1\",\"obs_aprobacion\":\"obs\"}]");
        input.setRepairTypeId(48031);
        input.setRegistrationUserId(1);
        input.setLanguageId(1);



        Catalog catalog = new Catalog();
        catalog.setId(1);
        catalog.setDescription("REP");
        when(catalogRepository.findById(any())).thenReturn(Optional.of(catalog));
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);

        when(catalogRepository.findIdByAlias(captor.capture())).thenAnswer(invocation -> {
            String alias = invocation.getArgument(0);
            if (Parameter.IS_REPAIR_STRUCTURE.equals(alias)) {
                return 48031;
            } else {
                return 1;
            }
        });

        Eir eir = new Eir();
        eir.setId(1);
        Container container = new Container();
        container.setId(1);
        eir.setContainer(container);
        when(sdeEirRepository.findById(anyInt())).thenReturn(Optional.of(eir));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Message {ID}");

        EirZone eirZone = new EirZone();
        eirZone.setId(1);
        when(eirZoneRepository.save(any())).thenReturn(eirZone);

        RepairManagementSaveOutput output = repairManagementSaveService.repairManagementSave(input);

        assertNotNull(output);
        assertEquals(1, output.getRespState());
        assertTrue(output.getRespMessage().contains("Message"));
    }

    @Test
    void documentationEmptyList_invalidJson_throwsException() {
        RepairManagementSaveInput.Input input = new RepairManagementSaveInput.Input();
        input.setApprovalList("invalid json");

        assertThrows(JSONException.class, () -> repairManagementSaveService.repairManagementSave(input));
    }

    @Test
    void documentationEmptyList_eirNotFound_returnsOutput() throws JsonProcessingException {
        RepairManagementSaveInput.Input input = new RepairManagementSaveInput.Input();
        input.setApprovalList("[{\"eir_id\":1,\"opcion_seleccionada\":1,\"siguiente_zona\":\"1\",\"obs_aprobacion\":\"obs\"}]");
        input.setRepairTypeId(48030);
        input.setRegistrationUserId(1);
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        when(sdeEirRepository.findById(anyInt())).thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Message {ID}");

        RepairManagementSaveOutput output = repairManagementSaveService.repairManagementSave(input);

        assertNotNull(output);
        assertEquals(2, output.getRespState());
        assertTrue(output.getRespMessage().contains("Message"));
    }

    @Test
    void documentationEmptyList_noRecords_returnsOutput() throws JsonProcessingException {
        RepairManagementSaveInput.Input input = new RepairManagementSaveInput.Input();
        input.setApprovalList("[]");
        input.setRepairTypeId(48030);
        input.setRegistrationUserId(1);
        input.setLanguageId(1);

        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Message {ID}");

        RepairManagementSaveOutput output = repairManagementSaveService.repairManagementSave(input);

        assertNotNull(output);
        assertEquals(2, output.getRespState());
        assertTrue(output.getRespMessage().contains("Message"));
    }

}