package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.PhotoDeleteOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;


@ExtendWith(MockitoExtension.class)
class PhotoDeleteServiceTest {

    @Mock
    private EirRepository eirRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private ChassisEstimateEirPhotoRepository chassisEstimateEirPhotoRepository;

    @Mock
    private EstimateEmrEirPhotoRepository estimateEmrEirPhotoRepository;

    @Mock
    private SdfEirPhotoRepository eirPhotoRepository;

    @Mock
    private AttachmentRepository attachmentRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private PhotoDeleteService photoDeleteService;

    private User modificationUser;
    private Catalog isChassisCatalog;
    private Catalog isEmptyCatalog;
    private Eir eir;

    @BeforeEach
    void init() {
        modificationUser = new User();
        modificationUser.setId(1);

        isChassisCatalog = new Catalog();
        isChassisCatalog.setId(1);

        isEmptyCatalog = new Catalog();
        isEmptyCatalog.setId(2);

        eir = new Eir();
        eir.setId(1);
    }

    @Test
    void Given_ValidRequest_When_DeletePhotoFromChassisEstimate_Then_ReturnSuccess() {
        Mockito.when(userRepository.findById(any())).thenReturn(Optional.of(modificationUser));
        Mockito.when(catalogRepository.findByAlias("sd1_equipment_category_chassis")).thenReturn(isChassisCatalog);
        Mockito.when(chassisEstimateEirPhotoRepository.findByEirChassis_IdAndAttachment_Id(any(), any()))
                .thenReturn(Optional.of(new ChassisEstimateEirPhoto()));

        PhotoDeleteOutput output = photoDeleteService.deletePhoto(1, 1, 1, 1);

        assertEquals(1, output.getRespEstado());
        assertEquals("Photo were successfully deleted", output.getRespMensaje());
    }

    @Test
    void Given_ValidRequest_When_DeletePhotoFromEirPhoto_Then_ReturnSuccess() {
        Mockito.when(userRepository.findById(any())).thenReturn(Optional.of(modificationUser));
        Mockito.when(catalogRepository.findByAlias("sd1_equipment_category_chassis")).thenReturn(isChassisCatalog);
        Mockito.when(catalogRepository.findByAlias("43083")).thenReturn(isEmptyCatalog);
        Mockito.when(eirRepository.findById(any())).thenReturn(Optional.of(eir));
        Mockito.when(eirPhotoRepository.findByEir_IdAndAttached_Id(any(), any()))
                .thenReturn(Optional.of(new SdfEirPhoto()));

        PhotoDeleteOutput output = photoDeleteService.deletePhoto(1, 1, 1, 3);

        assertEquals(1, output.getRespEstado());
        assertEquals("Photo were successfully deleted", output.getRespMensaje());
    }

    @Test
    void Given_InvalidUserModificationId_When_DeletePhoto_Then_ReturnError() {
        Mockito.when(userRepository.findById(any())).thenReturn(Optional.empty());

        PhotoDeleteOutput output = photoDeleteService.deletePhoto(1, 1, 1, 1);

        assertEquals(0, output.getRespEstado());
        assertEquals("Invalid userModificationId", output.getRespMensaje());
    }

    @Test
    void Given_InvalidEirId_When_DeletePhoto_Then_ReturnError() {
        Mockito.when(userRepository.findById(any())).thenReturn(Optional.of(modificationUser));
        Mockito.when(catalogRepository.findByAlias("sd1_equipment_category_chassis")).thenReturn(isChassisCatalog);
        Mockito.when(eirRepository.findById(any())).thenReturn(Optional.empty());

        PhotoDeleteOutput output = photoDeleteService.deletePhoto(1, 1, 1, 3);

        assertEquals(0, output.getRespEstado());
        assertEquals("EIR not found with id 1", output.getRespMensaje());
    }

    @Test
    void Given_ExceptionThrown_When_DeletePhoto_Then_ReturnError() {
        Mockito.when(userRepository.findById(any())).thenThrow(new RuntimeException("DB error"));

        PhotoDeleteOutput output = photoDeleteService.deletePhoto(1, 1, 1, 1);

        assertEquals(0, output.getRespEstado());
        assertEquals("DB error", output.getRespMensaje());
    }
}