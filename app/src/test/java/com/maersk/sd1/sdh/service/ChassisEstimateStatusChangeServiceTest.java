// File: ChassisEstimateStatusChangeServiceTest.java
package com.maersk.sd1.sdh.service;

import com.maersk.sd1.sdh.dto.ChassisEstimateStatusChangeInput;
import com.maersk.sd1.sdh.dto.ChassisEstimateStatusChangeOutput;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.ChassisEstimate;
import com.maersk.sd1.common.repository.ChassisEstimateRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChassisEstimateStatusChangeServiceTest {

    @Mock
    private ChassisEstimateRepository chassisEstimateRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private ChassisEstimateStatusChangeService service;

    private ChassisEstimateStatusChangeInput.Input validInput;
    private ChassisEstimateStatusChangeInput.ChassisEstimateDetail detail;

    @BeforeEach
    void setUp() {
        detail = new ChassisEstimateStatusChangeInput.ChassisEstimateDetail();
        detail.setChassisEstimateId(1);
        validInput = new ChassisEstimateStatusChangeInput.Input();
        validInput.setEstimatesList(List.of(detail));
        validInput.setLanguageId(1);
        validInput.setUser(200L);

       lenient().when(catalogRepository.findByAlias("sd1\\_chaest\\_created")).thenReturn(new Catalog(1));
       lenient().when(catalogRepository.findByAlias("sd1\\_chaest\\_finalized")).thenReturn(new Catalog(2));
       lenient().when(catalogRepository.findByAlias("sd1\\_chaest\\_submitted")).thenReturn(new Catalog(3));
        lenient().when(catalogRepository.findByAlias("sd1\\_chaest\\_approved")).thenReturn(new Catalog(100));
        lenient().when(catalogRepository.findByAlias("sd1\\_chaest\\_rejected")).thenReturn(new Catalog(101));
        lenient().when(catalogRepository.findByAlias("sd1\\_chaest\\_completed")).thenReturn(new Catalog(102));
        lenient().when(catalogRepository.findByAlias("sd1\\_chaest\\_canceled")).thenReturn(new Catalog(103));
    }

    @Test
    void returnsSuccessWhenEstimateApproved() {
        validInput.setStatus(100);
        ChassisEstimate estimate = new ChassisEstimate();
        estimate.setId(1);
        Catalog statusCatalog = new Catalog(0);
        estimate.setCatChaestimStatus(statusCatalog);
        when(chassisEstimateRepository.findByIdInAndActive(anyList(), eq(true)))
                .thenReturn(List.of(estimate));
        when(messageLanguageRepository.fnTranslatedMessage(eq("DOCUMENT_CHASSIS_GATE_IN"), anyInt(), eq(1)))
                .thenReturn("Updated successfully");

        ChassisEstimateStatusChangeOutput output = service.updateEstimateStatus(validInput);
        assertEquals(1, output.getRespEstado());
        assertEquals("Updated successfully", output.getRespMensaje());
        verify(chassisEstimateRepository, times(1)).save(estimate);
    }

    @Test
    void returnsSuccessWhenNoMatchingEstimateFound() {
        validInput.setStatus(100);
        when(chassisEstimateRepository.findByIdInAndActive(anyList(), eq(true)))
                .thenReturn(Collections.emptyList());
        when(messageLanguageRepository.fnTranslatedMessage(eq("DOCUMENT_CHASSIS_GATE_IN"), anyInt(), eq(1)))
                .thenReturn("Updated successfully");

        ChassisEstimateStatusChangeOutput output = service.updateEstimateStatus(validInput);
        assertEquals(1, output.getRespEstado());
        assertEquals("Updated successfully", output.getRespMensaje());
        verify(chassisEstimateRepository, never()).save(any());
    }

    @Test
    void returnsErrorOutputWhenExceptionOccurs() {
        validInput.setStatus(100);
        when(chassisEstimateRepository.findByIdInAndActive(anyList(), eq(true)))
                .thenThrow(new RuntimeException("Processing error"));

        ChassisEstimateStatusChangeOutput output = service.updateEstimateStatus(validInput);
        assertEquals(0, output.getRespEstado());
        assertEquals("Processing error", output.getRespMensaje());
    }

    @Test
    void returnsSuccessWhenEstimateCanceled() {
        validInput.setStatus(103);
        detail.setEstimateCancelReason(10);
        detail.setEstimateCancelDescription("Cancel description");

        ChassisEstimate estimate = new ChassisEstimate();
        estimate.setId(1);
        Catalog initialStatus = new Catalog(0);
        estimate.setCatChaestimStatus(initialStatus);

        when(chassisEstimateRepository.findByIdInAndActive(anyList(), eq(true)))
                .thenReturn(List.of(estimate));
        when(messageLanguageRepository.fnTranslatedMessage(eq("DOCUMENT_CHASSIS_GATE_IN"), anyInt(), eq(1)))
                .thenReturn("Updated successfully");

        ChassisEstimateStatusChangeOutput output = service.updateEstimateStatus(validInput);
        assertEquals(1, output.getRespEstado());
        assertEquals("Updated successfully", output.getRespMensaje());
        assertEquals(103, estimate.getCatChaestimStatus().getId());
        verify(chassisEstimateRepository, times(1)).save(estimate);
    }
}