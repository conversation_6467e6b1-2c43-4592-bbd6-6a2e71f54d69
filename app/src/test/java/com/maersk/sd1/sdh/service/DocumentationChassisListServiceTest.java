package com.maersk.sd1.sdh.service;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Chassis;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdh.dto.DocumentChassisProjection;
import com.maersk.sd1.sdh.dto.DocumentationChassisListInput;
import com.maersk.sd1.sdh.dto.DocumentationChassisListOutput;
import com.maersk.sd1.sdh.dto.DocumentationChassisListOutput.DocumentationChassisListData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentationChassisListServiceTest {

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @Mock
    private ChassisRepository chassisRepository;

    @Mock
    private ChassisDocumentRepository chassisDocumentRepository;

    @Mock
    private ChassisBookingDocumentRepository chassisBookingDocumentRepository;

    @Mock
    private ChassisDocumentDetailRepository chassisDocumentDetailRepository;

    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;

    @InjectMocks
    private DocumentationChassisListService documentationChassisListService;

    private DocumentationChassisListInput.Input input;
    private DocumentationChassisListOutput output;

    @BeforeEach
    void setUp() {
        input = new DocumentationChassisListInput.Input();
        input.setBusinessUnitId("BU123");
        input.setSubBusinessUnitId(1);
        input.setLanguageId(1);
        input.setCustomerCompany("CustomerCo");
        input.setOwnerCompany("OwnerCo");
        input.setOperationType(BigDecimal.valueOf(1));
        input.setDocumentNumber("DOC123");
        input.setDocumentType(BigDecimal.valueOf(1));
        input.setMovementType(BigDecimal.valueOf(1));
        input.setStateId(BigDecimal.valueOf(1));
        input.setRegisterDateMin("01/01/2023");
        input.setRegisterDateMax("31/12/2023");
        input.setChassisList("[\"Chassis123\"]");
        input.setPage(1);
        input.setSize(10);

        DocumentationChassisListData data = new DocumentationChassisListData();
        data.setRowId(1);
        data.setDocumentChassisId(123);
        data.setDocumentType("Type1");
        data.setDocumentNumber("DOC123");
        data.setMovementTypeId(BigDecimal.valueOf(1));
        data.setMovementType("MoveType1");
        data.setCustomerCompany("CustomerCo");
        data.setOwnerCompany("OwnerCo");
        data.setStatusId(BigDecimal.valueOf(1));
        data.setStatus("Status1");
        data.setOperationType("OpType1");
        data.setUserRegistrationId(BigDecimal.valueOf(1));
        data.setUserRegistrationName("User1");
        data.setUserRegistrationLastname("Lastname1");
        data.setUserRegistrationDate("01/01/2023"); // Updated format
        data.setUserModificationId(BigDecimal.valueOf(1));
        data.setUserModificationName("User2");
        data.setUserModificationLastname("Lastname2");
        data.setUserModificationDate("02/01/2023"); // Updated format
        data.setCreationSource("Source1");
        data.setDetail("Detail1");
        data.setRecordNumber(1);

        output = new DocumentationChassisListOutput();
        output.setTotalRecords(Collections.singletonList(Collections.singletonList(1)));
        output.setData(Collections.singletonList(data));
    }


    @Test
    void testDocumentationChassisListSuccess() {
        Catalog statusCompletedChassis = new Catalog();
        statusCompletedChassis.setId(1);
        Catalog completedDocument = new Catalog();
        completedDocument.setId(2);
        Catalog inProcessDocument = new Catalog();
        inProcessDocument.setId(3);
        Catalog pendingDocument = new Catalog();
        pendingDocument.setId(4);
        Catalog movementTypeGateIn = new Catalog();
        movementTypeGateIn.setId(43080);
        Catalog movementTypeGateOut = new Catalog();
        movementTypeGateOut.setId(43081);

        when(catalogRepository.findByAlias("sd1_chassisdoc_status_completed")).thenReturn(statusCompletedChassis);
        when(catalogRepository.findByAlias("sd1_status_completed")).thenReturn(completedDocument);
        when(catalogRepository.findByAlias("sd1_status_in_progress")).thenReturn(inProcessDocument);
        when(catalogRepository.findByAlias("sd1_status_pending")).thenReturn(pendingDocument);
        when(catalogRepository.findByAliasAndStatus("43080", true)).thenReturn(movementTypeGateIn);
        when(catalogRepository.findByAliasAndStatus("43081", true)).thenReturn(movementTypeGateOut);

        when(messageLanguageRepository.fnTranslatedMessage(Constants.GENERAL, 30, input.getLanguageId())).thenReturn("Type");
        when(messageLanguageRepository.fnTranslatedMessage(Constants.GENERAL, 31, input.getLanguageId())).thenReturn("Requested");
        when(messageLanguageRepository.fnTranslatedMessage(Constants.GENERAL, 33, input.getLanguageId())).thenReturn("Pending");
        when(messageLanguageRepository.fnTranslatedMessage(Constants.GENERAL, 34, input.getLanguageId())).thenReturn("Received");

        Chassis mockChassis = new Chassis();
        mockChassis.setId(1);
        mockChassis.setChassisNumber("Chassis123");
        mockChassis.setActive(true);

        List<Chassis> activeChassisList = Collections.singletonList(mockChassis);
        when(chassisRepository.findByChassisNumberInAndActiveTrue(Collections.singletonList("Chassis123"))).thenReturn(activeChassisList);

        List<DocumentChassisProjection> projections = Collections.singletonList(new DocumentChassisProjection() {
            @Override
            public Integer getDocumentChassisId() {
                return 123;
            }

            @Override
            public String getDocumentType() {
                return "Type1";
            }

            @Override
            public String getDocumentNumber() {
                return "DOC123";
            }

            @Override
            public BigDecimal getMovementTypeId() {
                return BigDecimal.valueOf(1);
            }

            @Override
            public String getMovementType() {
                return "MoveType1";
            }

            @Override
            public String getCustomerCompany() {
                return "CustomerCo";
            }

            @Override
            public String getOwnerCompany() {
                return "OwnerCo";
            }

            @Override
            public Integer getStatusId() {
                return 1;
            }

            @Override
            public String getStatus() {
                return "Status1";
            }

            @Override
            public String getOperationType() {
                return "OpType1";
            }

            @Override
            public Integer getUserRegistrationId() {
                return 1;
            }

            @Override
            public String getUserRegistrationName() {
                return "User1";
            }

            @Override
            public String getUserRegistrationLastname() {
                return "Lastname1";
            }

            @Override
            public String getRegistrationDate() {
                return "2023-01-01";
            }

            @Override
            public Integer getUserModificationId() {
                return 1;
            }

            @Override
            public String getUserModificationName() {
                return "User2";
            }

            @Override
            public String getUserModificationLastname() {
                return "Lastname2";
            }

            @Override
            public String getModificationDate() {
                return "2023-01-02";
            }

            @Override
            public String getCreationSource() {
                return "Source1";
            }

            @Override
            public String getDetail() {
                return "Detail1";
            }
        });
        when(chassisDocumentRepository.findDocumentChassiswithProjection(input.getSubBusinessUnitId(), input.getDocumentNumber(), input.getDocumentType(), input.getOperationType(), input.getCustomerCompany(), input.getOwnerCompany(), input.getMovementType(), "2023-01-01", "2023-12-31", Collections.singletonList(1), input.getLanguageId())).thenReturn(projections);

        DocumentationChassisListOutput result = documentationChassisListService.documentationChassisList(input);

        assertEquals(output.getTotalRecords(), result.getTotalRecords());
        assertEquals(output.getData().get(0).getDocumentChassisId(), result.getData().get(0).getDocumentChassisId());
        verify(catalogRepository, times(1)).findByAlias("sd1_chassisdoc_status_completed");
        verify(catalogRepository, times(1)).findByAlias("sd1_status_completed");
        verify(catalogRepository, times(1)).findByAlias("sd1_status_in_progress");
        verify(catalogRepository, times(1)).findByAlias("sd1_status_pending");
        verify(catalogRepository, times(1)).findByAliasAndStatus("43080", true);
        verify(catalogRepository, times(1)).findByAliasAndStatus("43081", true);
        verify(messageLanguageRepository, times(1)).fnTranslatedMessage(Constants.GENERAL, 30, input.getLanguageId());
        verify(messageLanguageRepository, times(1)).fnTranslatedMessage(Constants.GENERAL, 31, input.getLanguageId());
        verify(messageLanguageRepository, times(1)).fnTranslatedMessage(Constants.GENERAL, 33, input.getLanguageId());
        verify(messageLanguageRepository, times(1)).fnTranslatedMessage(Constants.GENERAL, 34, input.getLanguageId());
        verify(chassisRepository, times(1)).findByChassisNumberInAndActiveTrue(Collections.singletonList("Chassis123"));
        verify(chassisDocumentRepository, times(1)).findDocumentChassiswithProjection(input.getSubBusinessUnitId(), input.getDocumentNumber(), input.getDocumentType(), input.getOperationType(), input.getCustomerCompany(), input.getOwnerCompany(), input.getMovementType(), "2023-01-01", "2023-12-31", Collections.singletonList(1), input.getLanguageId());
    }

    @Test
    void testDocumentationChassisListFailure() {
        when(catalogRepository.findByAlias("sd1_chassisdoc_status_completed")).thenThrow(new RuntimeException("Service error"));

        try {
            documentationChassisListService.documentationChassisList(input);
        } catch (RuntimeException e) {
            assertEquals("Service error", e.getMessage());
        }

        verify(catalogRepository, times(1)).findByAlias("sd1_chassisdoc_status_completed");
    }
}
