package com.maersk.sd1.sdh.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdh.dto.ChassisInspectionSaveInputDTO;
import com.maersk.sd1.sdh.dto.ChassisInspectionSaveOutputDTO;
import com.maersk.sd1.sdh.service.ChassisInspectionSaveService;

import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ChassisInspectionSaveControllerTest {

    @Mock
    private ChassisInspectionSaveService chassisInspectionSaveService;

    @InjectMocks
    private ChassisInspectionSaveController controller;

    public ChassisInspectionSaveControllerTest() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidInputWhenSaveChassisInspectionThenReturnOkResponse() {
        ChassisInspectionSaveInputDTO.Input input = new ChassisInspectionSaveInputDTO.Input();
        input.setSystemAlias("SYS");

        ChassisInspectionSaveInputDTO.Prefix prefix = new ChassisInspectionSaveInputDTO.Prefix();
        prefix.setInput(input);

        ChassisInspectionSaveInputDTO.Root root = new ChassisInspectionSaveInputDTO.Root();
        root.setPrefix(prefix);

        ChassisInspectionSaveOutputDTO outputDTO = new ChassisInspectionSaveOutputDTO();
        outputDTO.setRespResult(1);
        outputDTO.setRespMessage("Saved");

        when(chassisInspectionSaveService.saveChassisInspection(any())).thenReturn(outputDTO);

        ResponseEntity<ResponseController<ChassisInspectionSaveOutputDTO>> response = controller.saveChassisInspection(root);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespResult());
        assertEquals("Saved", response.getBody().getResult().getRespMessage());
    }

    @Test
    void givenNullRootWhenSaveChassisInspectionThenReturnBadRequest() {
        ResponseEntity<ResponseController<ChassisInspectionSaveOutputDTO>> response = controller.saveChassisInspection(null);

        assertEquals(400, response.getStatusCode().value());
    }

    @Test
    void givenMissingInputWhenSaveChassisInspectionThenReturnBadRequest() {
        ChassisInspectionSaveInputDTO.Root root = new ChassisInspectionSaveInputDTO.Root();
        root.setPrefix(null);

        ResponseEntity<ResponseController<ChassisInspectionSaveOutputDTO>> response = controller.saveChassisInspection(root);

        assertEquals(400, response.getStatusCode().value());
    }

    @Test
    void givenExceptionInServiceWhenSaveChassisInspectionThenReturnServerError() {
        ChassisInspectionSaveInputDTO.Input input = new ChassisInspectionSaveInputDTO.Input();
        ChassisInspectionSaveInputDTO.Prefix prefix = new ChassisInspectionSaveInputDTO.Prefix();
        prefix.setInput(input);
        ChassisInspectionSaveInputDTO.Root root = new ChassisInspectionSaveInputDTO.Root();
        root.setPrefix(prefix);

        when(chassisInspectionSaveService.saveChassisInspection(any()))
                .thenThrow(new RuntimeException("DB error"));

        ResponseEntity<ResponseController<ChassisInspectionSaveOutputDTO>> response = controller.saveChassisInspection(root);

        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
    }
}
