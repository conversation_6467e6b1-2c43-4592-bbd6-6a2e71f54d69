package com.maersk.sd1.sdh.service;

import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.EstimateEmrCostManHoursRepository;
import com.maersk.sd1.sdh.dto.GetManHourCostChassisInputDTO;
import com.maersk.sd1.sdh.dto.GetManHourCostChassisOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GetManHourCostChassisIServiceTest {

    @InjectMocks
    private GetManHourCostChassisIService getManHourCostChassisIService;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private EstimateEmrCostManHoursRepository estimateEmrCostManHoursRepository;

    private GetManHourCostChassisInputDTO.Input validInput;

    @BeforeEach
    void setUp() {
        validInput = new GetManHourCostChassisInputDTO.Input();
        validInput.setSubBusinessUnitId(100L);
        validInput.setOwnerId(10);
        validInput.setCatEstimateTypeId(200L);
        validInput.setCurrencyId(300L);
    }

    @Test
    void Given_ValidInput_When_GetManHourCostChassisIsCalled_Then_ReturnsValidCost() {
        // Arrange
        when(catalogRepository.findIdByAlias("sd1_equipment_category_chassis")).thenReturn(5);
        when(estimateEmrCostManHoursRepository.findCostManHour(100, 10, 200, 300, 5)).thenReturn(500);

        // Act
        GetManHourCostChassisOutputDTO result = getManHourCostChassisIService.getManHourCostChassis(validInput);

        // Assert
        assertNotNull(result);
        assertEquals(500, result.getCostPerManHour());
        verify(catalogRepository, times(1)).findIdByAlias("sd1_equipment_category_chassis");
        verify(estimateEmrCostManHoursRepository, times(1)).findCostManHour(100, 10, 200, 300, 5);
    }

    @Test
    void Given_InvalidInput_When_GetManHourCostChassisThrowsException_Then_ReturnsNegativeOne() {
        // Arrange
        when(catalogRepository.findIdByAlias("sd1_equipment_category_chassis")).thenThrow(new RuntimeException("Database Error"));

        GetManHourCostChassisOutputDTO result = getManHourCostChassisIService.getManHourCostChassis(validInput);

        assertNotNull(result);
        assertEquals(-1, result.getCostPerManHour());
        verify(catalogRepository, times(1)).findIdByAlias("sd1_equipment_category_chassis");
    }
}