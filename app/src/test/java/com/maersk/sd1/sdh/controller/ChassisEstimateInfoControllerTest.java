package com.maersk.sd1.sdh.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdh.dto.ChassisEstimateInfoInput;
import com.maersk.sd1.sdh.dto.ChassisEstimateInfoOutput;
import com.maersk.sd1.sdh.service.ChassisEstimateInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class ChassisEstimateInfoControllerTest {

    @Mock
    private ChassisEstimateInfoService chassisEstimateInfoService;

    @InjectMocks
    private ChassisEstimateInfoController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidRequest_When_ServiceReturnsData_Then_ResponseIsOk() {
        // Arrange
        ChassisEstimateInfoInput.Root request = mock(ChassisEstimateInfoInput.Root.class);
        ChassisEstimateInfoInput.Prefix prefix = mock(ChassisEstimateInfoInput.Prefix.class);
        ChassisEstimateInfoInput.Input input = mock(ChassisEstimateInfoInput.Input.class);

        when(request.getPrefix()).thenReturn(prefix);
        when(prefix.getInput()).thenReturn(input);
        when(input.getEstimateId()).thenReturn(1);
        when(input.getLanguageId()).thenReturn(2);

        ChassisEstimateInfoOutput output = new ChassisEstimateInfoOutput();
        when(chassisEstimateInfoService.getEstimateChassisInfo(1, 2)).thenReturn(output);

        // Act
        ResponseEntity<ResponseController<ChassisEstimateInfoOutput>> response = controller.getEstimateChassisInfo(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(output, response.getBody().getResult());
        verify(chassisEstimateInfoService, times(1)).getEstimateChassisInfo(1, 2);
    }

    @Test
    void Given_NullEstimateId_When_RequestIsProcessed_Then_ThrowsIllegalArgumentException() {
        // Arrange
        ChassisEstimateInfoInput.Root request = mock(ChassisEstimateInfoInput.Root.class);
        ChassisEstimateInfoInput.Prefix prefix = mock(ChassisEstimateInfoInput.Prefix.class);
        ChassisEstimateInfoInput.Input input = mock(ChassisEstimateInfoInput.Input.class);

        when(request.getPrefix()).thenReturn(prefix);
        when(prefix.getInput()).thenReturn(input);
        when(input.getEstimateId()).thenReturn(null);
        when(input.getLanguageId()).thenReturn(2);

        // Act & Assert
        try {
            controller.getEstimateChassisInfo(request);
        } catch (IllegalArgumentException e) {
            assertEquals("estimate_id and language_id must not be null", e.getMessage());
        }

        verify(chassisEstimateInfoService, never()).getEstimateChassisInfo(anyInt(), anyInt());
    }

    @Test
    void Given_ServiceThrowsException_When_RequestIsProcessed_Then_ResponseIsInternalServerError() {
        // Arrange
        ChassisEstimateInfoInput.Root request = mock(ChassisEstimateInfoInput.Root.class);
        ChassisEstimateInfoInput.Prefix prefix = mock(ChassisEstimateInfoInput.Prefix.class);
        ChassisEstimateInfoInput.Input input = mock(ChassisEstimateInfoInput.Input.class);

        when(request.getPrefix()).thenReturn(prefix);
        when(prefix.getInput()).thenReturn(input);
        when(input.getEstimateId()).thenReturn(1);
        when(input.getLanguageId()).thenReturn(2);

        when(chassisEstimateInfoService.getEstimateChassisInfo(1, 2)).thenThrow(new RuntimeException("Service error"));

        // Act
        ResponseEntity<ResponseController<ChassisEstimateInfoOutput>> response = controller.getEstimateChassisInfo(request);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        verify(chassisEstimateInfoService, times(1)).getEstimateChassisInfo(1, 2);
    }
}