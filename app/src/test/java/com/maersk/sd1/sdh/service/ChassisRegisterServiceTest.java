package com.maersk.sd1.sdh.service;

import com.maersk.sd1.common.model.Chassis;
import com.maersk.sd1.common.model.ChassisDocument;
import com.maersk.sd1.common.model.ChassisDocumentDetail;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.ChassisRepository;
import com.maersk.sd1.common.repository.ChassisDocumentRepository;
import com.maersk.sd1.common.repository.ChassisDocumentDetailRepository;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sdh.dto.ChassisRegisterInput;
import com.maersk.sd1.sdh.dto.ChassisRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ChassisRegisterServiceTest {

    @Mock
    private ChassisRepository chassisRepository;

    @Mock
    private ChassisDocumentRepository chassisDocumentRepository;

    @Mock
    private ChassisDocumentDetailRepository chassisDocumentDetailRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private ChassisRegisterService chassisRegisterService;

    private ChassisRegisterInput.Input input;

    @BeforeEach
    public void setUp() {
        input = new ChassisRegisterInput.Input();
        input.setReferenceNumber("REF123");
        input.setReferenceType(1L);
        input.setChassisNumber("CH123");
        input.setCatChassisTypeId(1L);
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitLocalId(1);
        input.setUserRegistrationId(1L);
        input.setLanguageId(1);
    }

    @Test
    void testRegisterChassisInReference_success() {
        when(businessUnitRepository.findParentBusinessUnitIdOrNull(any(Integer.class)))
                .thenReturn(Optional.of(1));
        when(catalogRepository.findByAliasOrNull("43080"))
                .thenReturn(Optional.of(new Catalog(1)));
        when(catalogRepository.findByAliasOrNull("sd1_chassisdoc_status_pending"))
                .thenReturn(Optional.of(new Catalog(1)));
        when(chassisDocumentRepository.findActiveDocumentChassis(any(String.class), any(Integer.class), any(Integer.class), any(Long.class)))
                .thenReturn(Optional.of(new ChassisDocument()));
        when(chassisRepository.findByChassisNumber(any(String.class)))
                .thenReturn(Optional.empty());
        when(catalogRepository.findById(48727))
                .thenReturn(Optional.of(new Catalog(1)));
        when(catalogRepository.findByAliasOrNull("sd1_creationsource_chassis_gate"))
                .thenReturn(Optional.of(new Catalog(1)));
        when(chassisRepository.save(any(Chassis.class)))
                .thenReturn(new Chassis());
        when(catalogRepository.findByAliasOrNull("sd1_chassisdoc_status_Pending"))
                .thenReturn(Optional.of(new Catalog(1)));
        when(chassisDocumentDetailRepository.save(any(ChassisDocumentDetail.class)))
                .thenReturn(new ChassisDocumentDetail());
        when(messageLanguageRepository.fnTranslatedMessage(any(String.class), any(Integer.class), any(Integer.class)))
                .thenReturn("Success");

        ChassisRegisterOutput output = chassisRegisterService.registerChassisInReference(input);

        assertEquals(1, output.getRespEstado());
        assertEquals("Success", output.getRespMensaje());
    }

    @Test
    void testRegisterChassisInReference_parentBusinessUnitNotFound() {
        when(businessUnitRepository.findParentBusinessUnitIdOrNull(any(Integer.class)))
                .thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage(any(String.class), any(Integer.class), any(Integer.class)))
                .thenReturn("Parent Business Unit not found");

        ChassisRegisterOutput output = chassisRegisterService.registerChassisInReference(input);

        assertEquals(2, output.getRespEstado());
        assertEquals("Parent Business Unit not found", output.getRespMensaje());
    }

    @Test
    void testRegisterChassisInReference_movementCatalogNotFound() {
        when(businessUnitRepository.findParentBusinessUnitIdOrNull(any(Integer.class)))
                .thenReturn(Optional.of(1));
        when(catalogRepository.findByAliasOrNull("43080"))
                .thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage(any(String.class), any(Integer.class), any(Integer.class)))
                .thenReturn("Movement Catalog not found");

        ChassisRegisterOutput output = chassisRegisterService.registerChassisInReference(input);

        assertEquals(2, output.getRespEstado());
        assertEquals("Movement Catalog not found", output.getRespMensaje());
    }

    @Test
    void testRegisterChassisInReference_pendingReferenceFound() {
        lenient().when(businessUnitRepository.findParentBusinessUnitIdOrNull(any(Integer.class)))
                .thenReturn(Optional.of(1));
        lenient().when(catalogRepository.findByAliasOrNull("43080"))
                .thenReturn(Optional.of(new Catalog(1)));
        lenient().when(catalogRepository.findByAliasOrNull("sd1_chassisdoc_status_pending"))
                .thenReturn(Optional.of(new Catalog(1)));
        lenient().when(chassisDocumentRepository.findAll())
                .thenReturn(java.util.Collections.singletonList(new ChassisDocument()));
        lenient().when(chassisDocumentDetailRepository.findAll())
                .thenReturn(java.util.Collections.singletonList(new ChassisDocumentDetail()));
        lenient().when(messageLanguageRepository.fnTranslatedMessage(any(String.class), any(Integer.class), any(Integer.class)))
                .thenReturn("Pending Reference Found");

        ChassisRegisterOutput output = chassisRegisterService.registerChassisInReference(input);

        assertEquals(2, output.getRespEstado());
        assertEquals("Pending Reference Found", output.getRespMensaje());
    }
}